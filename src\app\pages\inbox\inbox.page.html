<ion-header>
  <ion-toolbar color="primary" class="custom-toolbar">
    <ion-buttons slot="start">
      <ion-menu-button autoHide="false" class="white-icon"></ion-menu-button>
    </ion-buttons>
    <ion-title class="left-title">Inbox</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="generateJson()" fill="clear" class="white-icon">
        Generate JSON
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>


<ion-content [fullscreen]="true">
 <ion-button expand="full" color="primary" (click)="sendLogs()">Send logs to server</ion-button>
 <ion-button expand="full" color="secondary" (click)="openWebview()">Open Form</ion-button>
</ion-content>
