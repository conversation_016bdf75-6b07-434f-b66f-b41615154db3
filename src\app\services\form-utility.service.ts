import { Injectable } from '@angular/core';
import { AlertService } from './alert.service';
import { AppConstants } from '../constants/appConstants';

@Injectable({
  providedIn: 'root'
})
export class FormUtilityService {

  constructor(
    private alertService: AlertService
  ) {}
  
    public getFormDataFromHTML(alertService: any, myBrowser: any, callback: any) {

        this.generateJSON(myBrowser, (result: any, error : any) => {
            if (result && result.length > 0) {
                callback(result);
            } else {
                myBrowser.close();
                this.displayNoDataErr(alertService, error);
            }
        });
    }

    public generateJSON(myBrowser: any, callback : any) {
        myBrowser.executeScript({
            code: "generateJSON()"
        }).then((result : any) => {
            if (result.length > 0) {
                callback(result);
            } else {
                callback();
            }
        }, ( error: any) => {
            callback([], error);
        })
    }

    public displayNoDataErr(alertService: any, error?: string) {
        let errMsg = AppConstants.READING_FORM_DATA_ERR_MSG;
        if(error) errMsg += "<br/>" + error;
        alertService.showAlert("Error reading data!", errMsg);
    }
}
