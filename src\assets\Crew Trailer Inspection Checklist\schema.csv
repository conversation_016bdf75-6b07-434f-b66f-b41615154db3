<ops>,,
(CRE<PERSON><PERSON><PERSON><PERSON>),,  
{Crew<PERSON>railerNew},, 
ParentReferenceNo,CHAR,50
ReferenceNo,CHAR,50
<PERSON>ig<PERSON><PERSON>ber,CHAR,40
RigManager,CHAR,100
CompanyName,CHAR,100
CompanyCode,CHAR,10
Operator,CHAR,100
Position,CHAR,100
CompletedBy,CHAR,100
InspType,CHAR,100
Driller1,CHAR,50
Comments,CHAR,255
AdditionalComments,CHAR,255
RMSignature,SIGNATURE,
CompletedBySignature,SIGNATURE,
SignOffRigManager,CHAR,100
SignOffDrillerManager,CHAR,100
SignOffDrillerManagerSignature,SIGNATURE,1
SignOffRigManagerSignature,SIGNATURE,1
SignOffCompletedBy,CHAR,100
SignOffCompletedByPosition,CHAR,100
SignOffCompletedBySignature,SIGNATURE,
CreateDate,DATETIM<PERSON>,
CreatedBy,CHAR,100
UpdateDate,DAT<PERSON><PERSON><PERSON>,
<PERSON><PERSON><PERSON>,CHAR,100
SkipDate,D<PERSON>ETIME,
Ski<PERSON><PERSON>y,CHAR,100
SkipComment,CHAR,1000
CreateDateTzName,CHAR,100
CreateDateTzAbbr,CHAR,100
CreateDateTzOffest,DECIMAL,7.2
,,

[CrewTrailerNewMonthly],, 
crewinspectchklst_visible,FLAG,

crewchklst_damagedwindows,CHAR,100
crewchklst_platformgood,CHAR,100
crewchklst_trailergrounded,CHAR,100
crewchklst_watersupply,CHAR,100
crewchklst_watertested,CHAR,100
crewchklst_gfcibreakers,CHAR,100
crewchklst_electricaloutlets,CHAR,100
crewchklst_doorwayclear,CHAR,100
crewchklst_passinspection,CHAR,100
crewchklst_smokedetectors,CHAR,100
crewchklst_fridgeworking,CHAR,100
crewchklst_freezerworking,CHAR,100
crewchklst_subfloorintact,CHAR,100
crewchklst_airventcleaned,CHAR,100
crewchklst_airfilterchanged,CHAR,100
crewchklst_floorwallclean,CHAR,100
crewchklst_nopests,CHAR,100
crewchklst_portablewater,CHAR,100
crewchklst_appliancesclean,CHAR,100
crewchklst_exhaustworking,CHAR,100
crewchklst_equipmentsecured,CHAR,100
crewchklst_designatedsmoking,CHAR,100
,,

[FormNotification],,
CreatedBy,CHAR,100
CreateDate,DATETIME,
NotificationNo,CHAR,100
NotificationID,CHAR,100
NotificationType,CHAR,100
CodeGroup,CHAR,100
Code,CHAR,100
MainWorkCenter,CHAR,100
SecondaryWorkCenter,CHAR,100
RequiredStartDate,DATETIME,
RequiredEndDate,DATETIME,
Timezone,CHAR,100
WorkType,CHAR,100
Reason,CHAR,1000
Action,CHAR,1000
Status,CHAR,100
Priority,CHAR,100
UpdatedBy,CHAR,100
UpdateDate,DATETIME,
CompletedDate,DATETIME,