ion-content {
  --background: var(--app-light);
  margin: 0 auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #4a4a4a;
  color: white;
  margin-top: 20px;
  margin-bottom: 12px;
  padding-left: 12px;
  border-bottom: 1px solid #4a4a4a;
  height: 45px !important;
}

.section-header h2 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.section-header .add-btn {
  --color: white;
  font-size: 1.1rem;
  font-weight: 600;
  height: 32px;
  margin: 0;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-content {
  background: white;
  color: var(--app-dark);
  padding: 16px;
  overflow: hidden;           /* Prevent content spillage */
  box-sizing: border-box;
}

/* Crew Supervisor Section */
.section-content.supervisor-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.supervisor-signature .signature-btn {
  --background: rgb(194, 4, 4);
  --color: #fff;
  --border-radius: 6px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  --padding-start: 16px;
  --padding-end: 16px;
  font-size: 12px;
  font-weight: normal;
  text-transform: none;
  white-space: nowrap;
}

.section-content.supervisor-section .supervisor-input {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.supervisor-input .input-with-dropdown {
  position: relative;
  width: 100%;
}

.supervisor-input .detail-input {
  width: 100%;
  border: none;
  border-bottom: 2px solid #e0e0e0;
  background: transparent;
  padding: 8px 0;
  font-size: 1rem;
  color: var(--app-dark);
  padding-bottom: 6px;
}

.supervisor-input .detail-input:focus {
  outline: none;
  border-bottom-color: var(--app-primary);
}

.supervisor-input .detail-input.error-input {
  border-bottom-color: rgb(194, 4, 4);
}

.supervisor-input .dropdown-btn {
  position: absolute;
  right: -10px;
  bottom: 0;
  --color: #757575;
}

.supervisor-input .dropdown-btn.error {
  --color: rgb(194, 4, 4);
}

.supervisor-signature {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 2px;
  width: 100%;
}

@media (max-width: 767px) {
  .supervisor-signature .signature-btn {
    --padding-start: 12px;
    --padding-end: 12px;
    width: 100%;
    max-width: 200px;
  }
}

@media (min-width: 768px) {
  .section-content.supervisor-section {
    flex-direction: row;
    align-items: flex-end;
    justify-content: space-between;
    gap: 16px;
  }
  
  .section-content.supervisor-section .supervisor-input {
    flex: 1;
    min-width: 200px;
  }
  
  .supervisor-signature {
    flex-shrink: 0;
    width: auto;
  }
}

@media (min-width: 1024px) {
  .section-content.supervisor-section {
    gap: 40px;
    justify-content: flex-start;
  }
  
  .section-content.supervisor-section .supervisor-input {
    min-width: 250px;
    max-width: 350px;
    flex: none;
  }
}



@media (max-width: 767px) {
  .supervisor-signature .signature-display {
    max-width: 100%;
    margin: 8px 0;
  }
  
  .supervisor-signature .signature-display .signature-image {
    max-width: 100%;
    max-height: 40px;
  }
}

.empty-message {
  padding: 8px 0;
}

.empty-message .error-text {
  color: rgb(194, 4, 4);
}

.empty-message .normal-text {
  color: #757575;
  display: inline-block;
  padding: 8px 12px;
}

ion-toolbar.main-toolbar {
  --background: var(--app-primary-green);
  --color: white;
}

ion-toolbar.main-toolbar .header-btn {
  --border-radius: 4px;
  --padding-start: 10px;
  --padding-end: 10px;
  height: 32px;
  font-size: 0.8rem;
  text-transform: none;
  --box-shadow: 0 2px 2px 0 rgba(0,0,0,0.14);
  margin: 0 4px;
}

ion-toolbar.main-toolbar .header-btn i {
  margin-right: 8px;
}

ion-toolbar.main-toolbar .header-btn .button-text {
  display: inline;
}

@media (max-width: 767px) {
  ion-toolbar.main-toolbar .header-btn .button-text {
    display: none !important;
  }
  
  ion-toolbar.main-toolbar .header-btn i {
    margin-right: 0;
  }
  
  ion-toolbar.main-toolbar .header-btn {
    --padding-start: 8px;
    --padding-end: 8px;
    min-width: 32px;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  ion-toolbar.main-toolbar .header-btn {
    font-size: 0.75rem;
  }
}

@media (min-width: 1024px) {
  .header-btn {
    font-size: 1rem;
  }
}

.details-content {
  padding: 16px;
  background: white;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
}

@media(min-width: 768px) {
  .details-grid {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
}

@media(min-width: 1024px) {
  .details-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }
}

.details-grid .detail-item {
  display: flex;
  flex-direction: column;
}

.details-grid .detail-item .label {
  font-size: 0.8rem;
  color: #757575;
}

.details-grid .detail-item .label.error {
  color: rgb(194, 4, 4);
}

.details-grid .detail-item .value {
  font-size: 1rem;
  color: var(--app-dark);
  margin-top: 4px;
}

.detail-input {
  width: 100%;
  border: none;
  border-bottom: 2px solid #e0e0e0;
  background: transparent;
  padding: 8px 0;
  font-size: 1rem;
  color: var(--app-dark);
  padding-bottom: 6px;
}

.detail-input:focus {
  outline: none;
  border-bottom-color: var(--app-primary);
}

.detail-input:disabled {
  opacity: 0.6;
}

.detail-input.error {
  border-bottom-color: rgb(194, 4, 4);
}

.input-with-dropdown {
  position: relative;
}

.input-with-dropdown .dropdown-btn {
  position: absolute;
  right: -10px;
  bottom: 0;
  --color: #757575;
}

.radio-group {
  display: flex;
  gap: 24px;
  margin-top: 8px;
}

.radio-group .radio-option {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.radio-group .radio-option input[type="radio"] {
  margin-right: 8px;
  accent-color: var(--app-primary-green);
}

.radio-group .radio-option .radio-label {
  font-size: 1rem;
  color: var(--app-dark);
}

/* Replace .crew-list */
.crew-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  padding: 0;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;   
}

/* New crew-item layout */
.crew-item {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
}

.crew-item .crew-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.crew-item .crew-info {
  flex: 1;
}

.crew-item .crew-info h3 {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: var(--app-dark);
}

.crew-item .crew-info p {
  font-size: 0.75rem;
  color: #757575;
  margin: 0;
}

.crew-item .crew-signature {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.crew-item .crew-actions {
   position: static;
  top: auto;
  right: auto;
  display: flex;
  align-items: center;
}

.crew-item .crew-actions .remove-btn {
  --color: #757575;
  font-size: 1rem;
  height: 32px;
  margin: 0;
  flex-shrink: 0;
  --padding-inline-start: 0px !important;
  --padding-inline-end: 0px !important;
  --padding-top: 0px !important;
  --padding-bottom: 0px !important;
  -webkit-padding-start: 0 !important;
  -webkit-padding-end: 0 !important ;
}


/* New 1024px media query for 3 columns */
@media (min-width: 1024px) {
  .crew-list {
    grid-template-columns: repeat(3, 1fr);
  }

  .crew-item .crew-content {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
  }
  
  .crew-item .crew-info {
    flex: 1;
    min-width: 0;
  }
  
  .crew-item .crew-signature {
    flex-shrink: 0;
    width: auto;
    justify-content: flex-end;
  }
}

@media (max-width: 779px){

.crew-item .crew-signature {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  
  .crew-signature .signature-btn {
    flex: 1;
    max-width: none;
  }
  
  .crew-item .crew-actions {
    flex-shrink: 0;
  }
}

/* Mobile layout: signature and delete button on same row */
@media (max-width: 767px) {
  .crew-item .crew-signature {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  
  .crew-signature .signature-btn {
    flex: 1;
    max-width: none;
  }
  
  .crew-item .crew-actions {
    flex-shrink: 0;
  }
}

/* Tablet screens: 2 columns */
@media (max-width: 768px)  {
  
  .crew-item .crew-content {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
  }
  
  .crew-item .crew-info {
    flex: 1;
    min-width: 0;
  }
  
  .crew-item .crew-signature {
    flex-shrink: 0;
    width: auto;
    justify-content: flex-end;
  }
}

/* Medium Desktop: 3 columns */
@media (min-width: 1204px) {
  .crew-list {
    grid-template-columns: repeat(3, 1fr);  /* 3 instead of 4 */
    gap: 14px;
  }
}

@media ( (min-width: 780px) and (max-width: 1156px)) {
  .crew-list {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

/* Signature button styling updates */
.crew-signature .signature-btn {
  --background: rgb(194, 4, 4);
  --color: #fff;
  --border-radius: 6px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  --padding-start: 16px;
  --padding-end: 16px;
  font-size: 12px;
  font-weight: normal;
  text-transform: none;
  white-space: nowrap;
}

.third-party-crew-item .crew-input-section .crew-input-group .label {
  font-size: 0.8rem;
  color: #757575;
  margin-bottom: 4px;
}

.third-party-crew-item .crew-input-section .crew-input-group .label.error {
  color: rgb(194, 4, 4);
}

.third-party-crew-item .crew-input-section .crew-input-group .crew-name-input {
  width: 100%;
  border: none;
  border-bottom: 2px solid #e0e0e0;
  background: transparent;
  padding: 8px 0;
  font-size: 1rem;
  color: var(--app-dark);
}

.third-party-crew-item .crew-input-section .crew-input-group .crew-name-input:focus {
  outline: none;
  border-bottom-color: var(--app-primary);
}

.third-party-crew-item .crew-input-section .crew-input-group .crew-name-input:disabled {
  opacity: 0.6;
}

.third-party-crew-item .crew-input-section .crew-input-group .crew-name-input.error {
  border-bottom-color: rgb(194, 4, 4);
}

/* Make third party crew match regular crew card styling */
.third-party-crew-item {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  width: 100%;
  min-width: 0;             /* Allow shrinking */
  max-width: 100%;          /* Never exceed grid cell */
  box-sizing: border-box;   /* Include padding in width */
  overflow: hidden;
}

.third-party-crew-item .crew-input-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.third-party-crew-item .crew-input-section .crew-input-group {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.third-party-crew-item .crew-input-section .crew-signature-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.third-party-crew-item .signature-display.readonly-signature:hover {
  background-color: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.third-party-crew-item .crew-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.third-party-crew-item .crew-actions .remove-btn {
  --color: #757575;
  font-size: 1rem;
  height: 32px;
  margin: 0;
  flex-shrink: 0;
}

/* Ensure third party crew items don't overflow */
.third-party-crew-item {
  box-sizing: border-box;
  min-width: 0;
  max-width: 100%;
  overflow: hidden;
}

.third-party-crew-item .crew-input-section {
  box-sizing: border-box;
  min-width: 0;
  overflow: hidden;
}

.third-party-crew-item .crew-input-section .crew-input-group {
  box-sizing: border-box;
  min-width: 0;
  overflow: hidden;
}

.third-party-crew-item .crew-input-section .crew-input-group .crew-name-input {
  box-sizing: border-box;
  min-width: 0;
  max-width: 100%;
}

/* Make third party crew signature button match regular crew */
.third-party-crew-item .crew-signature-section .signature-btn {
  --background: rgb(194, 4, 4);
  --color: #fff;
  --border-radius: 6px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  --padding-start: 16px;
  --padding-end: 16px;
  font-size: 12px;
  font-weight: normal;
  text-transform: none;
  white-space: nowrap;
}

/* Mobile: Match regular crew layout */
@media (max-width: 767px) {
  .third-party-crew-item .crew-input-section {
    flex-direction: column;
    gap: 16px;
  }
  
  .third-party-crew-item .crew-input-section .crew-input-group {
    width: 100%;
  }
  
  .third-party-crew-item .crew-input-section .crew-signature-section {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    gap: 12px;
  }
  
  .third-party-crew-item .crew-signature-section .signature-btn {
    --padding-start: 12px;
    --padding-end: 12px;
    flex: 1;
    max-width: 200px;
  }
  
  .third-party-crew-item .crew-input-section .crew-actions {
    flex-shrink: 0;
    width: auto;
    display: flex;
    justify-content: flex-end;
    align-self: center;
    margin-top: 0;
    box-sizing: border-box;
  }
  
  .third-party-crew-item .crew-actions .remove-btn {
    --color: #757575;
    font-size: 1rem;
    height: 32px;
    margin: 0;
  }
}

/* Tablet screens: 2 columns */
@media (min-width: 768px) and (max-width: 1023px) {
  .third-party-crew-item .crew-input-section {
    flex-direction: row;
    align-items: center;
    gap: 12px;
    width: 100%;
  }
  
  .third-party-crew-item .crew-input-section .crew-input-group {
    flex: 1;
    min-width: 100px;
    max-width: 150px;
    align-self: flex-end;
  }
  
  .third-party-crew-item .crew-input-section .crew-signature-section {
    flex: 1;
    max-width: 160px;
    justify-content: center;
    align-self: center;
  }
  
  .third-party-crew-item .crew-input-section .crew-actions {
    flex-shrink: 0;
    width: auto;
    justify-content: center;
    align-items: center;
    margin-top: 0;
  }
}

/* Desktop: 3 columns */
@media (min-width: 1024px) {
  .third-party-crew-item .crew-input-section {
    flex-direction: row;
    align-items: center;
    gap: 8px;
    width: 100%;
  }
  
  .third-party-crew-item .crew-input-section .crew-input-group {
    flex: 1;
    min-width: 100px;
    max-width: none;
    align-self: flex-end;
  }
  
  .third-party-crew-item .crew-input-section .crew-signature-section {
    flex: 1;
    max-width: 180px;
    justify-content: center;
    align-self: center;
  }
  
  .third-party-crew-item .crew-input-section .crew-actions {
    flex-shrink: 0;
    width: 32px;            /* Compact action area */
    min-width: 32px;
    justify-content: center;
  }
}


/* Button Styling */
.btn-save {
  background-color: var(--app-inpr);
  border-radius: 5%;
  padding: 18px 20px !important;
  font-size: 14px;
  text-transform: capitalize;
}

.signature-display {
  width: 100%;
  margin: 4px 0;
  border:1px solid #4a4a4a;
  background-color: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.signature-display:hover {
  background-color: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.signature-display .signature-image {
  width: 160px;
  height: auto;
  max-height: 32px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background-color: white;
  object-fit: contain;
  display: block;
  margin: 0 auto 4px auto;
}

/* Error States */
.error-text {
  color: rgb(194, 4, 4) !important;
}

.error-input {
  border-bottom-color: rgb(194, 4, 4) !important;
}

.detail-item .label {
  color: var(--app-dark);
}

.detail-input {
  color: var(--app-dark);
  border-bottom-color: #e0e0e0;
}

/* Dropdown Icon Adjustments */
.dropdown-btn i.fa-chevron-down {
  color: black;
}

.dropdown-btn.error i.fa-chevron-down,
.error .dropdown-btn i.fa-chevron-down {
  color: rgb(194, 4, 4) !important;
}

.topics-list {
  display: grid;
  grid-template-columns: 1fr;    /* 1 per row on mobile */
  gap: 16px;                      /* spacing between items replaces borders */
  margin: 0;                      /* was -16px; avoid overflow with grid */
}

@media (min-width: 768px) {
  .topics-list {
    grid-template-columns: repeat(2, 1fr); /* 2 per row */
  }
}

.topic-content {
  flex: 1;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
  min-width: 0;
}

.topic-content:hover {
  background-color: #f5f5f5;
}

.topic-info {
  width: 100%;
}

.topic-info h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--app-dark);
  line-height: 1.3;
  word-wrap: break-word;
}

.topic-details {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
  font-size: 0.85rem;
  font-weight: 500;
  color: #666;
  line-height: 1.4;
}

.topic-time {
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

.topic-description {
  color: #666;
  word-wrap: break-word;
  flex: 1;
}

.topic-description.no-description {
  color: #999;
  font-style: italic;
}

.topic-info p {
  font-size: 0.85rem;
  color: #666;
  margin: 4px 0 0;
  line-height: 1.4;
  word-wrap: break-word;
}

.topic-info p.no-description {
  color: #999;
  font-style: italic;
}

.topic-item {
  display: flex;
  align-items: flex-start;
  flex-direction: row;
  padding: 12px;
  border: 1px solid #e0e0e0;        /* <- Add full border */
  border-radius: 8px;               /* <- Add rounded corners */
  gap: 8px;
  min-height: 60px;
  background: white;                /* <- Ensure white background */
}

.topic-item .delete-btn {
  flex-shrink: 0;
  --background: rgb(194, 4, 4);
  --background-hover: rgb(164, 3, 3);
  --background-focused: rgb(164, 3, 3);
  --color: white;
  --border-radius: 8px;
  width: 32px;
  height: 32px;
  background: rgb(194, 4, 4);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin: 0;
}

.topic-item .delete-btn:hover {
  background: rgb(164, 3, 3);
  transform: scale(1.05);
}

.topic-item .delete-btn:active {
  background: rgb(144, 2, 2);
  transform: scale(0.95);
}

.topic-item .delete-btn i {
  color: white;
  font-size: 16px;
}

.topic-item .delete-btn[disabled] {
  --background: #ccc;
  background: #ccc;
  opacity: 0.6;
  cursor: not-allowed;
}

.topic-item .delete-btn[disabled]:hover {
  transform: none;
  background: #ccc;
}

/* Responsive Topics */
@media (max-width: 480px) {
  .topic-item {
    padding: 12px;
    gap: 8px;
  }
  
  .topic-content {
    padding: 4px 8px;
  }
  
  .topic-info h3 {
    font-size: 0.9rem;
    margin-bottom: 6px;
  }
  
  .topic-info p {
    font-size: 0.8rem;
  }
  
  .topic-details {
    font-size: 0.8rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }
  
  .topic-item .delete-btn {
    width: 36px;
    height: 36px;
  }
  
  .topic-item .delete-btn i {
    font-size: 16px;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .topic-item {
    padding: 14px;
  }
  
  .topic-details {
    flex-wrap: wrap;
  }
}

@media (min-width: 769px) {
  
  .topic-content:hover {
    background-color: #f8f9fa;
  }
  
  .topic-item .delete-btn {
    width: 44px;
    height: 44px;
  }
  
  .topic-item .delete-btn i {
    font-size: 20px;
  }
}

/* Additional Responsive Adjustments */
@media (min-width: 1024px) {
  .section-header h2 {
    font-size: 1.2rem;
    padding-top: 12px;
    padding-bottom: 12px;
    font-weight: 400;
  }

  .section-header .add-btn {
    font-size: 1.5rem;
  }

  .details-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }

  .detail-item {
    font-size: 1rem;
  }
}

.signature-display { border: none; }
.signature-display.readonly-signature{
  box-shadow: none;
}
