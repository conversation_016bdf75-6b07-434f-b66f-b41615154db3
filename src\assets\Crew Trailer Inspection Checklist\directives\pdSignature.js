angular.module('PDTest')
.directive('pdSignature', function() {
    function getHTML(model) {
        var html='<div ng-hide="'+model+'">';
        html +='    <button type="button" class="btn btn-danger" ';
        html +='        ng-click="setSignatureModal(\''+model+'\')"';
        html +='        data-toggle="modal" data-target="#signatureModal">Tap to add Signature</button>';
        html +='</div>';
        html +='<div class="signatureresult" ng-show="'+model+'" ';
        html +='    ng-click="setSignatureModal(\''+model+'\', '+model+')"';
        html +='    data-toggle="modal" data-target="#signatureModal">';
        html +='    <img ng-src="{{ '+model+' }}" class="img-responsive">';
        html +='</div>';
        return html;
    }

    return {
        restrict: 'EA',
        template: function(elem, attr){
            return getHTML(attr.model);
         },
    };

});