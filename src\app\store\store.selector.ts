import { AppState, NotificationState, ProgressState, TemplateState , PrefilledState, FormsState, STMRState, STMRTopicState, SearchState, ReleaseNotesState } from './app.state';
import { CounterState } from './store.reducer';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { RigState  } from "./app.state";
import { SyncState } from './app.state';


export const selectCounterState = (state: AppState) => state.counter;

export const selectCount = createSelector(
  selectCounterState,
  (state: CounterState) => state.counter
);


export const selectRigState = createFeatureSelector<RigState>('rig');

export const selectRigData = createSelector(
  selectRigState,
  (state) => {
    console.log('selectRigData selector called, state:', JSON.stringify(state, null, 2));
    return state.rigData;
  }
);

export const selectRigLoadedFromDb = createSelector(
  selectRigState,
  (state) => state.loadedFromDb
);

export const selectNotificationState = createFeatureSelector<NotificationState>('notification');

export const selectAllNotifications = createSelector(
  selectNotificationState,
  (state) => {
    console.log('Selector: selectAllNotifications ran');
    return state?.events ?? [];
  }
);

export const selectTemplateState = createFeatureSelector<TemplateState>('template');

// export const selectAllTemplates = createSelector(
//   selectTemplateState,
//   (state: TemplateState) => state.templates
// );

export const selectAllTemplates = createSelector(
  selectTemplateState,
  (state: TemplateState) => {
    console.log('👀 selectAllTemplates selector called with:', state);
    return state.templates;
  }
);


export const selectTemplatesLoadedFromDb = createSelector(
  selectTemplateState,
  (state) => state.loadedFromDb 
);



export const selectTemplateError = createSelector(
  selectTemplateState,
  (state) => state.error
);

export const selectProgressState = createFeatureSelector<ProgressState>('progress');

export const selectProgressPercentage = createSelector(
  selectProgressState,
  (state) => state.percentage
);

export const selectPrefilledState = createFeatureSelector<PrefilledState>('prefilled');
export const selectPrefilledData = createSelector(
  selectPrefilledState,
  (state) => {
    console.log('selectPrefilledData selector called, state:', state);
    return state.prefilledData
  }
);


export const selectPrefilledLoaded = createSelector(
  selectPrefilledState,
  (state) => state.loaded
);

export const selectPrefillNeedsRefresh = createSelector(
  selectRigState,
  (state: RigState) => state.prefillNeedsRefresh
);
export const selectSyncState = createFeatureSelector<SyncState>('sync');
export const selectIsSyncing = createSelector(selectSyncState, (state) => state.isSyncing);
export const selectSyncProgress = createSelector(selectSyncState, (state) => state.progress);
export const selectSyncCurrentFile = createSelector(selectSyncState, (state) => state.currentSyncFile);
export const selectGraphToken = createSelector(selectSyncState, (state) => state.graphToken);
export const selectSiteHeaders = createSelector(selectSyncState, (state) => state.siteHeaders);
export const selectSiteMeta = createSelector(selectSyncState, (state) => state.siteMeta);
export const selectSyncError = createSelector(selectSyncState, (state) => state.error);
export const selectInitialDataDownloadInProgress = createSelector(
  selectSyncState,
  (state) => state.initialDataDownloadInProgress
);

export const selectSpConfig = createSelector(
  selectRigState,
  (state: RigState) => state.spConfig
);

export const selectSpConfigLoaded = createSelector(
  selectRigState,
  (state: RigState) => state.spConfigLoaded
);

export const selectFormsState = createFeatureSelector<FormsState>('forms');

export const selectAllForms = createSelector(
  selectFormsState,
  (state: FormsState) => {
    console.log('[Selector] selectAllForms called, state:', state);
    return state.forms;
  }
);

export const selectFormsLoading = createSelector(
  selectFormsState,
  (state) => state.loading
);

export const selectFormsError = createSelector(
  selectFormsState,
  (state) => state.error
);


export const selectSTMRState = createFeatureSelector<STMRState>('stmr');


export const selectSTMRWithTopics = createSelector(
  selectSTMRState,
  (state: STMRState) => {
    console.log('[Selector] selectSTMRWithTopics called, state:', state);
    return state.stmrData;
  }
);

// export const selectReleaseNotesState = (state: AppState) => state.releaseNotes;

export function createSearchSelector<T>(
  selectItems: (state: object) => T[],
  selectSearch: (state: object) => string,
  matcher: (item: T, search: string) => boolean = (item, search) =>
    JSON.stringify(item).toLowerCase().includes(search.toLowerCase())
) {
  return createSelector(
    selectItems,
    selectSearch,
    (items, search) => {
      if (!search) return items;
      return items.filter((item) => matcher(item, search));
    }
  );
}


export const selectSearchState = createFeatureSelector<SearchState>('search');
export const selectSearchQuery = createSelector(
  selectSearchState,
  (state) => state.query
);

export const selectFilteredForms = createSearchSelector(
  selectAllForms,
  selectSearchQuery,
  (form, search) => (
  (form.TEMPLATE_DESC ?? '').toLowerCase().includes((search ?? '').toLowerCase()) ||
    (form.FORM_ID ?? '').toLowerCase().includes((search ?? '').toLowerCase()) ||
    (form.CATEGORY_DESC ?? '').toLowerCase().includes((search ?? '').toLowerCase()))
);


export const selectFilteredStmrWithTopics = createSearchSelector(
  selectSTMRWithTopics,
  selectSearchQuery,
  (stmr, search) =>
    (stmr.STMR_ID?.toLowerCase().includes(search.toLowerCase())) ||
    stmr.topics?.some(
      (t: any) =>
        t.TOPIC_NAME?.toLowerCase().includes(search.toLowerCase()) ||
        t.TOPIC_NOTE?.toLowerCase().includes(search.toLowerCase())
    )
);


export const selectReleaseNotesState = createFeatureSelector<ReleaseNotesState>('releaseNotes');
export const selectAllReleaseNotes = createSelector(
  selectReleaseNotesState,
  (state) => state.releaseNotes
);
export const selectReleaseNotesLoadedFromDb = createSelector(
  selectReleaseNotesState,
  (state) => state.loadedFromDb
);




