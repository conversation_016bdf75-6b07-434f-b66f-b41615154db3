angular.module('PDTest')
    .factory('CommonFactory', function ($filter, $alert, $location, ModelFactory) {

        function sort(key, down, item, items) {
            // find the item to replace with the target
            var replaceIndex = 0;

            if (down) {
                replaceIndex = item[key] + 1;
            } else {
                replaceIndex = item[key] - 1;
            }

            var result = items.filter(function (x) {
                return x[key] === replaceIndex;
            });

            if (result.length !== 0) {
                result[0][key] = item[key];
                item[key] = replaceIndex;
            }
        }

        // 2018-May-28 RD:
        // Append random number to url to provide
        //a unique link.  Workaround for the Unvired
        // Client App
        function emitAction(action, platform) {
            var rnd = Math.floor(Math.random() * 20000);
            action += rnd;

            if (platform === 'windows') {
                $location.hash(action);
            } else if (platform === 'ios') {
                $location.url(action);
            } else {
                // dont know the platform so...
                $location.url(action);
            }
        }

        return {
            logError: function (source, err) {
                console.log(err);
                var msg = 'There was an error in: ' + source + '.  Error message: ' + err.message;
                alert(msg);
            },

            showSuccessAlert: function (msg) {
                $alert({
                    content: msg,
                    type: 'material',
                    placement: 'top',
                    duration: 3
                });
            },

            sortUp: function (key, item, items) {
                sort(key, false, item, items);
            },

            sortDown: function (key, item, items) {
                sort(key, true, item, items);
            },

            callSaveAction: function (exit, platform) {
                var action;

                if (exit) {
                    action = "saveAndExitButtonClicked";
                } else {
                    action = "saveButtonClicked";
                }

                emitAction(action, platform);
            },

            //add for skipButtonUpdate
            callSkipAction: function (platform) {
                emitAction("skipButtonClicked", platform);
            },

            callSubmitAction: function (platform) {
                emitAction("submitButtonClicked", platform);
            },

            callBackAction: function (platform) {
                emitAction("backButtonClicked", platform);
            },

            callNotificationAction: function (platform) {
                return true;
                //emitAction("createNotificationButtonClicked", platform);
            },

            // 2018-May-28 RD:
            // Append random number to url to provide
            //a unique link.  Workaround for the Unvired
            // Client App
            callPrintAction: function (platform) {
                var rnd = Math.floor(Math.random() * 20000);

                if (platform === 'windows') {
                    $location.hash("printButtonClicked" + rnd);
                } else if (platform === 'ios') {
                    window.print();
                } else {
                    // dont know the platform so just try to print
                    window.print();
                }
            },

            // 2021-May-19 - RT
            //Used in createUserChanged(index.js) to match crew member position to list in model
            userPositionIsInList: function (position) {
                var positions = ModelFactory.positions();
                for (var i = 0; i < positions.length; i++) {
                    if (position === positions[i]) return true;
                }
                return false;

            },

            // 2021-Nov-8 - RT
            //Used in createUserChangedShop(index.js) to match crew member position to list in model for shops/offices
            userPositionIsInListShop: function (position) {
                var positions = ModelFactory.positionsShop();
                for (var i = 0; i < positions.length; i++) {
                    if (position === positions[i]) return true;
                }
                return false;

            },


            //2021-May-19 - RT
            //Used in the creatUserChanged(index.js) to compare crew member position to list in model and modify if required
            findCommonItem: function (positionArr, posListArr) {
                for (let i = 0; i < positionArr.length; i++) {
                    for (let j = 0; j < posListArr.length; j++) {
                        if (positionArr[i] === posListArr[j]) {
                            return posListArr[j];
                        }
                    }
                }
                return '';
            }
        }

    });


  