import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { <PERSON>ert<PERSON>ontroller, PopoverController } from '@ionic/angular/standalone';
import { IonButton, IonInput, IonItem, ModalController } from '@ionic/angular/standalone';
import { DataService } from 'src/app/services/data.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { Store } from '@ngrx/store';

import { AppState } from 'src/app/store/app.state'; // Import AppState type
import { clearRigState, switchRig } from 'src/app/store/store.actions';
import { selectAllTemplates, selectRigData, selectTemplatesLoadedFromDb } from 'src/app/store/store.selector';
import { filter, Observable, switchMap, take, tap } from 'rxjs';
import { RIG_HEADER } from 'src/models/RIG_HEADER';
import { CommonModule } from '@angular/common';
import { BusyIndicatorService } from 'src/app/services/busy-indicator.service';
import * as RigActions from 'src/app/store/store.actions';
 declare var ump: any;

@Component({
  selector: 'app-site-rig-modal',
  templateUrl: './site-rig-modal.component.html',
  styleUrls: ['./site-rig-modal.component.scss'],
  standalone: true,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [  FormsModule , CommonModule, IonInput ]
})
export class SiteRigModalComponent  {

 siteNumber: string = '';
 rigData$!: Observable<RIG_HEADER | null>;
errorMessage: string | null = null;
  constructor(
    private popoverController: PopoverController,
    private dataService: DataService,
    private device: Device,
    private busyIndicatorService: BusyIndicatorService,
    private alertController: AlertController,
    private store: Store<AppState> // Specify AppState here
  ) {
    console.log('device', this.device.platform);

   
  }
  
  async closeModal() {
    await this.popoverController.dismiss();
  }

  async submit() {
    console.log(this.siteNumber);
   if (this.rigData$) {
  this.rigData$.pipe(take(1)).subscribe((data) => {
    if (this.siteNumber === data?.RIG_NO) {
      this.errorMessage = 'Site number should not be the same as the current one';
    }
  });
} else  if (!this.siteNumber.trim()) {
    console.warn('Site number is empty – not proceeding');
    return;
  }
  this.busyIndicatorService.displayBusyIndicator('Switching site' )
    const rigId = this.siteNumber.trim();
  if (!rigId) {
    console.warn('Site number is empty – not proceeding');
    this.errorMessage = 'Please enter a site number';
    await this.busyIndicatorService.hideBusyIndicator();
    return;
  }
  console.log('the site number is ' , this.siteNumber)
    if (this.siteNumber.trim()) {
      const rigId = this.siteNumber.trim();
  
      // Optional: clear previous state if needed
      // this.store.dispatch(clearRigState());


      let rigHeaderResult = await this.dataService.getRigHeader(rigId, this.device.uuid)
      if (rigHeaderResult.RIG?.[0]?.RIG_HEADER) {
        let rigHeader = rigHeaderResult.RIG?.[0]?.RIG_HEADER
        if (rigHeader) {
          console.log('Loaded rig data from API:', rigHeader);
          RigActions.loadRigFromDbSuccess({ rigData: rigHeader });
          await this.busyIndicatorService.hideBusyIndicator();
          await ump.sendInitialDataDownloadRequest(this.dataService.inputDataForCustomization(rigHeader));
          console.log('Emitting initialDataDownloaded$ event');
          this.busyIndicatorService.presentToast('A request to download all data has been placed...Please wait')
          console.log('✅ Rig switched successfully:', rigHeader);
          let rigDataFromDB = await this.dataService.getRigHeaderFromDB();
          if(rigDataFromDB.RIG_NO != undefined){
           this.store.dispatch(RigActions.loadAllTemplatesFromDb());
            this.store.dispatch(RigActions.loadAllFormsFromDb());
            await this.popoverController.dismiss(rigHeader);
          }
        
        } else {
          console.log('No rig data found in DB');
        }
      } else if (rigHeaderResult.InfoMessage && Array.isArray(rigHeaderResult.InfoMessage)) {

        const errorMsg = rigHeaderResult.InfoMessage.map((msg: any) => msg.message).join(', ');
        this.errorMessage = errorMsg
        await this.busyIndicatorService.hideBusyIndicator();

      }
}
  }





}
