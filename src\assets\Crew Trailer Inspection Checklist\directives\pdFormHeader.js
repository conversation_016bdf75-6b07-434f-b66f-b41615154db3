angular.module('PDTest')
.directive('pdFormHeader', function() {
    function getHTML(model) {
        // Job ID, Rig #, Manager
        var html='                    <div class="row">';
        html +='                        <div class="col-sm-5">';
        html +='                            <div class="form-group">';
        html +='                                <label class="control-label">STMR Reference Number</label>';
        html +='                                <input type="text" class="form-control" ng-model="'+model+'.ParentReferenceNo" ng-readonly="true" placeholder="Unassigned">';
        html +='                            </div>';
        html +='                        </div>';
        html +='                        <div class="col-sm-5">';
        html +='                            <div class="form-group">';
        html +='                                <label class="control-label">Form Reference Number</label>';
        html +='                                <input type="text" class="form-control" ng-model="'+model+'.ReferenceNo" ng-readonly="true" placeholder="Unassigned">';
        html +='                            </div>';
        html +='                        </div>';
        html +='                        <div class="col-sm-3">';
        html +='                            <div class="form-group">';
        html +='                                <label class="control-label">Site Number</label>';
        html +='                                <input name="rigNumber" type="text" class="form-control"';
        html +='                                    ng-readonly="true"';
        html +='                                    ng-model="'+model+'.RigNumber" required>';
        html +='                            </div>';
		html +='                        </div>';
        html +='                        <div class="col-sm-7">';
		html +='                            <div class="form-group" ng-class="{ \'has-error\': contentForm.rigManager.$invalid }">';
        html +='                                <label class="control-label">Site Manager</label>';
        html +='                                <input name="rigManager" type="text" class="form-control"';
        html +='                                    ng-readonly="submitted" ng-disabled="submitted"' ;
        html +='                                    ng-model="'+model+'.RigManager"';
        html +='                                    bs-typeahead ';
        html +='                                    bs-options="item.USER_NAME as item.USER_NAME for item in lists.RIG_MGR | orderBy: \'USER_NAME\'"';
        html +='                                    data-min-length="0"';
        html +='                                    data-limit="20"';
		html +='                                    placeholder="Select or Add New" required>';
        html +='                                <div class="help-block" ng-messages="contentForm.rigManager.$error" role="alert">';
        html +='                                    <div ng-message="required">Site Manager is required.</div>';
		html +='                                </div>';
        html +='                            </div> ';
		html +='                        </div>';
        html +='                    </div>';
        //Date -->
        html +='                    <div class="row">';
        html +='                        <div class="col-sm-5">';
        html +='                            <div class="form-group" ng-class="{ \'has-error\': contentForm.createDate.$invalid }">';
        html +='                                <label class="control-label">Date Created</label>';
		html +='                                <div class="row">';
        html +='                                    <div class="col-xs-10">';
        html +='                                        <input name="createDate" type="text" size="10" class="form-control" ';
        html +='                                            ng-readonly="true"';
        html +='                                            ng-model="'+model+'.CreateDate"';
        html +='                                            data-date-format="MMM dd yyyy" bs-datepicker/>';
		html +='                                    </div>';
		html +='                                    <div class="col-xs-10">';
        html +='                                        <input class="form-control" type="text" size="8" ';
		html +='                                            ng-readonly="true"';
        html +='                                            value="{{'+model+'.CreateDate|date: \'H:mm\': \''+model+'.CreateDateTzOffset\'}} {{' +model+'.CreateDateTzAbbr}}  (24hrs)" '
		html +='                                            data-autoclose="1">';
		html +='                                    </div>';
		html +='                                </div>';
        html +='                                <div class="help-block" ng-messages="contentForm.createDate.$error" role="alert">';
        html +='                                    <div ng-message="required">Date and time are required.</div>';
        html +='                                </div>';
        html +='                            </div>';
        html +='                        </div>';
        //User and Postion Fields for Rig OR Shop/Office -->
        html +='                        <div class="col-sm-5" ng-show= rigPatternValid('+model+'.RigNumber);>';
        html +='                            <div class="form-group" ng-class="{ \'has-error\': contentForm.completedBy.$invalid }">';
        html +='                                <label class="control-label">Completed By</label>';
        html +='                                <input name="completedBy" type="text" class="form-control" ';
        html +='                                    ng-readonly="submitted"';
        html +='                                    ng-model="'+model+'.CompletedBy" ';
        html +='                                    ng-change="createdUserChanged('+model+'.CompletedBy);" ';
        html +='                                    ng-maxlength="100"';
        html +='                                    bs-typeahead';
        html +='                                    bs-options="item.USER_NAME as item.USER_NAME for item in lists.CREW | orderBy: \'USER_NAME\'"';
        html +='                                    data-min-length="0"';
        html +='                                    data-limit="50"';
        html +='                                    placeholder="Select or Add New"';
        html +='                                    required>';
        html +='                                <div class="help-block" ng-messages="contentForm.completedBy.$error" role="alert">';
        html +='                                    <div ng-message="required">Name is required.</div>';
        html +='                                </div>';
        html +='                            </div>';
        html +='                        </div>';
        html +='                        <div class="col-sm-5" ng-hide= rigPatternValid('+model+'.RigNumber);>';
        html +='                            <div class="form-group" ng-class="{ \'has-error\': contentForm.completedBy.$invalid }">';
        html +='                                <label class="control-label">Completed By</label>';
        html +='                                <input name="completedBy" type="text" class="form-control" ';
        html +='                                    ng-readonly="submitted"';
        html +='                                    ng-model="'+model+'.CompletedBy" ';
        html +='                                    ng-change="createdUserChangedShop('+model+'.CompletedBy);"';
        html +='                                    ng-maxlength="100"';
        html +='                                    bs-typeahead';
        html +='                                    bs-options="item.USER_NAME as item.USER_NAME for item in lists.CREW | orderBy: \'USER_NAME\'"';
        html +='                                    data-min-length="0"';
        html +='                                    data-limit="50"';
        html +='                                    placeholder="Select or Add New"';
        html +='                                    required>';
        html +='                                <div class="help-block" ng-messages="contentForm.completedBy.$error" role="alert">';
        html +='                                    <div ng-message="required">Name is required.</div>';
        html +='                                </div>';
        html +='                            </div>';
        html +='                        </div>';
        html +='                        <div class="col-sm-3" ng-show= rigPatternValid('+model+'.RigNumber); >';
        html +='                            <div class="form-group" ng-class="{ \'has-error\': contentForm.position.$invalid }">';
        html +='                                <label class="control-label">Position</label>';
        html +='                                <select name="position" type="text" class="form-control"';
        html +='                                    ng-readonly="submitted"';
         html +='                                    ng-options="item for item in positions"'
        html +='                                    ng-model="'+model+'.Position" placeholder="Unassigned" required>';       
        html +='                                </select>'
        html +='                                <div class="help-block" ng-messages="contentForm.position.$error" role="alert">';
        html +='                                    <div ng-message="required">Position is required.</div>';
        html +='                                </div>';
        html +='                            </div>';
        html +='                        </div>';
        html +='                        <div class="col-sm-3" ng-hide= rigPatternValid('+model+'.RigNumber); >';
        html +='                            <div class="form-group" ng-class="{ \'has-error\': contentForm.position.$invalid }">';
        html +='                                <label class="control-label">Position</label>';
        html +='                                <select name="position" type="text" class="form-control"';
        html +='                                    ng-readonly="submitted"';
        html +='                                    ng-options="item for item in positionsShop"'
        html +='                                    ng-model="'+model+'.Position" placeholder="Unassigned" required>';       
        html +='                                </select>'
        html +='                                <div class="help-block" ng-messages="contentForm.position.$error" role="alert">';
        html +='                                    <div ng-message="required">Position is required.</div>';
        html +='                                </div>';
        html +='                            </div>';
        html +='                        </div>';
        //Operator  -->
        html +='                        <div class="col-sm-7">';
        html +='                            <div class="form-group" ng-class="{ \'has-error\': contentForm.operator.$invalid }">';
        html +='                                <label class="control-label">Operator</label>';
        html +='                                <input name="operator" type="text" class="form-control" ';
        html +='                                    ng-readonly="submitted"';
        html +='                                    ng-model="'+model+'.Operator" placeholder="Unassigned" required>';
        html +='                                <div class="help-block" ng-messages="contentForm.operator.$error" role="alert">';
        html +='                                    <div ng-message="required">Operator is required.</div>';
        html +='                                </div>';
        html +='                            </div>';
        html +='                        </div>';
        html +='                    </div>';
        return html;
    }

    return {
        restrict: 'EA',
        template: function(elem, attr){
            return getHTML(attr.model);
         },
    };

});