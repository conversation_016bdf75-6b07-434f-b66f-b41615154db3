<ion-header>
  <ion-toolbar color="primary" class="custom-toolbar" mode="md">
    <ion-buttons slot="start">
      <ion-menu-button autoHide="false" class="white-icon"></ion-menu-button>
    </ion-buttons>
    <ion-title class="left-title">{{ 'Release Notes' | translate }}</ion-title>
    <ion-buttons slot="end">
      <ion-button class="btn-save-lower" (click)="navigateToDataEnhancedForm()" *ngIf="newInboxItemAvailable">
        <span *ngIf="newInboxItemAvailable == 1">{{newInboxItemAvailable}} {{'New item in inbox' | translate}}</span>
        <span *ngIf="newInboxItemAvailable > 1">{{newInboxItemAvailable}} {{'New items in inbox' | translate}}</span>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>


<ion-content [fullscreen]="true">
  <div style="width:100%">
    <ion-list class="my-10px">
       <ion-item-divider class="group-header">
        <ion-label>
          <div class="group-header-content">
            <span>{{ 'App Release Notes' | translate }}</span>
            <span style="margin-left: 5px; margin-bottom: 2px;">({{ appReleaseNotes?.length || 0 }})</span>
          </div>
        </ion-label>
      </ion-item-divider>
      <ion-item class="no-items" *ngIf="!appReleaseNotes || appReleaseNotes.length <= 0">
        {{ 'No app release notes found.' | translate }}
      </ion-item>
      <ion-item *ngFor="let releasenote of appReleaseNotes; let i = index" >
        <ion-thumbnail slot="start" style="margin-left: 1% !important; display: inline-flex; position: relative;">
          <i class="fa-solid fa-star overlap-star-comment" *ngIf="releasenote.IS_READ != 'true'"></i>
          <img style="width: 44px; height: 44px; position: absolute; top: 10px;" src="assets/imgs/comment-alt-exclamation.png" />
        </ion-thumbnail>
        <ion-label>
          <h2 class="align-vertically">{{ releasenote.TITLE }}</h2>
          <div style="margin-top:12px; margin-bottom: 9px">
            <h3 class="align-vertically bwrap" >{{ releasenote.DESCRIPTION }}</h3>
          </div>
          <div class="meta-row">
            {{ 'Published' | translate }}: {{ returnDisplayDate(releasenote.PBLSH_ON, 'X') || '- ' }}
          </div>
        </ion-label>
      </ion-item>
    </ion-list>

    <ion-list class="my-10px">
      <ion-item-divider class="group-header">
        <ion-label>
          <div class="group-header-content">
            <span>{{ 'Template Release Notes' | translate }}</span>
            <span style="margin-left: 5px; margin-bottom: 2px;">({{ templateReleaseNotes?.length || 0 }})</span>
          </div>
        </ion-label>
      </ion-item-divider>
      <ion-item class="no-items" *ngIf="!templateReleaseNotes || templateReleaseNotes.length <= 0">
        {{ 'No template release notes found.' | translate }}
      </ion-item>
      <ion-item *ngFor="let releasenote of templateReleaseNotes; let i = index">
        <ion-thumbnail slot="start" style="margin-left: 1% !important; display: inline-flex; position: relative;">
          <i class="fa-solid fa-wifi overlap-wifi-comment" *ngIf="releasenote.IS_DATA_ENH == 'true'"></i>
          <i class="fa-solid fa-star overlap-star-comment" *ngIf="releasenote.IS_READ != 'true'"></i>
          <img style="width: 44px; height: 44px; position: absolute; top: 10px;" src="assets/imgs/comment-alt-exclamation.png" />
        </ion-thumbnail>
        <ion-label>
          <h2 class="align-vertically">{{ releasenote.TMPLT_NAME }}</h2>
          <div style="margin-top:12px; margin-bottom: 9px">
            <h2 class="align-vertically bwrap" style="color: black;">{{ releasenote.DESCRIPTION }}</h2>
          </div>
          <div class="meta-row">
            {{ 'Version' | translate }}: {{ releasenote.VER_NO || '- ' }} • {{ 'Published' | translate }}:
            {{ returnDisplayDate(releasenote.PBLSH_ON, 'X') || '- ' }}
          </div>
        </ion-label>
        <ion-button color="primary" fill="solid" class="normal-case" slot="end" (click)="createForm($event, releasenote)"  shape="round">
          {{ 'Create Form' | translate }}
        </ion-button>
      </ion-item>
    </ion-list>
  </div>
</ion-content>

