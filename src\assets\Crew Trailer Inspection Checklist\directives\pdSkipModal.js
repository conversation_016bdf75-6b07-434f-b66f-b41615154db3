angular.module('PDTest')
.directive('pdSkipModal', function() {
    var html='    <div id="skipModal" class="modal fade" role="dialog">';
    html +='        <div class="modal-dialog">';
    html +='            <div class="modal-content">';
    html +='                <div class="modal-header">';
    html +='                    <button type="button" class="close" data-dismiss="modal">&times;</button>';
    html +='                    <h4 class="modal-title">Skip This Form</h4>';
    html +='                </div>';
    html +='                <div class="modal-body">';
    html +='                    <p>Are you sure you want to skip entering information on this form?  If so, please provide a comment below</p>';
    html +='                    <textarea name="skipComment" class="form-control"';
    html +='                        ng-readonly="submitted"';
    html +='                        ng-maxlength="1000"';
    html +='                        rows="5"';
    html +='                        ng-model="data[form.mainTable].SkipComment" required>';
    html +='                    </textarea>';
    html +='                    <span class="text-muted">{{1000-data[form.mainTable].SkipComment.length}} characters remaining</span>';

    html +='                </div>';
    html +='                <div class="modal-footer">';
    html +='                    <button type="button" class="btn btn-danger" data-dismiss="modal"';
    html +='                        ng-disabled="!data[form.mainTable].SkipComment"';
    html +='                        ng-click="skipConfirmed()">Skip</button>';
    html +='                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>';
    html +='                </div>';
    html +='            </div>';
    html +='        </div>';
    html +='    </div>';

    return {
        restrict: 'EA',
        template: html
    };

});