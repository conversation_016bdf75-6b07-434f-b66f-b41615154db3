import { Component, OnInit, Input, NgZone, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  IonContent,
  IonHeader,
  IonTitle,
  IonToolbar,
  IonButtons,
  IonButton,
  IonCard,
  IonCardContent,
  IonRow,
  IonCol,
  IonInput,
  IonIcon,
  ModalController,
  AlertController,
  IonGrid
} from '@ionic/angular/standalone';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Store } from '@ngrx/store';
import { triggerPrefillRefresh } from 'src/app/store/store.actions';

import { CREW_HEADER } from 'src/models/CREW_HEADER';
import { AppConstants } from '../../constants/appConstants';
import { AlertService } from '../../services/alert.service';
import { UtilityService } from '../../services/utility.service';
import { DataService } from '../../services/data.service';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';

import { addIcons } from 'ionicons';
import { close, addCircle, arrowBack } from 'ionicons/icons';

addIcons({
  close,
  addCircle,
  arrowBack
});

@Component({
  selector: 'app-add-crew',
  templateUrl: './add-crew.page.html',
  styleUrls: ['./add-crew.page.scss'],
  standalone: true,
  imports: [
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonButtons,
    IonButton,
    IonCard,
    IonCardContent,
    IonRow,
    IonCol,
    IonInput,
    IonIcon,
    CommonModule,
    FormsModule,
    TranslateModule,
    IonGrid
  ]
})
export class AddCrewPage implements OnInit {
  // Input properties for modal data
  @Input() data: any;
  @Input() crewPage: any;
  @Input() selectedCrew: CREW_HEADER[] = [];
  @Input() theme: string = 'normal';
  rigNo: string = '';
  completedCrewMembers: CREW_HEADER[] = [];
  crewMembers = signal<CREW_HEADER[]>([]);
  deletedCrewMembers = signal<CREW_HEADER[]>([]);
  stmrCrewId = signal<Array<{ Id: string }>>([]);
  allSavedData = signal<boolean>(true);
  validInputField = signal<boolean>(false);
  showDeleteCrewMemberButton = signal<boolean>(false);

  // Computed values
  hasUnsavedChanges = computed(() => {
    const members = this.crewMembers();
    return members.some(member => !member.saved) || this.deletedCrewMembers().length > 0;
  });

  constructor(
    private modalCtrl: ModalController,
    private translate: TranslateService,
    private alertService: AlertService,
    private ngZone: NgZone,
    private utilityService: UtilityService,
    private dataService: DataService,
    private alertCtrl: AlertController,
    private unviredSDK: UnviredCordovaSDK,
    private store: Store
  ) {}

  async ngOnInit() {
    console.log('[AddCrewPage] ngOnInit - data:', this.data);
    console.log('[AddCrewPage] ngOnInit - theme:', this.theme);

    this.rigNo = this.data?.RIG_NO || '';
    await this.initializeData();
  }

  private async initializeData() {
    try {
      await this.getStmrCrewFromDB();
      await this.getCrewMembersFromDB();

      // Only add a new crew member if none exist
      const currentMembers = this.crewMembers();
      if (currentMembers.length === 0) {
        this.addCrewMember();
      }

      console.log('[AddCrewPage] Initialized with crew members:', currentMembers.length);
    } catch (error) {
      console.error('[AddCrewPage] Error initializing data:', error);
      this.alertService.showAlert('Error', 'Failed to load crew data');
    }
  }

  async closeModal() {
    if (this.hasUnsavedChanges()) {
      await this.showUnsavedAlertMessage();
    } else {
      // Just dismiss without saving if no changes
      await this.modalCtrl.dismiss(null, 'cancel');
    }
  }

  private async showUnsavedAlertMessage() {
    const alert = await this.alertCtrl.create({
      header: this.translate.instant('Are you sure you want to go back?'),
      subHeader: this.translate.instant("Your unsaved changes will be lost if you don't save them."),
      buttons: [
        {
          text: this.translate.instant('Save'),
          handler: () => {
            console.log('[AddCrewPage] Saving crew member...');
            this.saveCrewMember();
            this.goToCrewPage();  
          }
        },
        {
          text: this.translate.instant("Don't Save"),
          handler: () => {
            console.log('[AddCrewPage] Going back to CrewPage list discarding all changes in memory.');
            this.goToCrewPage();
          }
        },
        {
          text: this.translate.instant("Cancel"),
          role: 'cancel'
        }
      ],
    });
    await alert.present();
  }

  private async goToCrewPage() {
    this.store.dispatch(triggerPrefillRefresh());
    await this.getCrewMembersFromDB();
    const updatedCrewMembers = this.crewMembers();
    console.log('[AddCrewPage] Closing modal with updated crew members:', updatedCrewMembers.length);
    if (this.crewPage?.callbackAfterAddCrew) {
      this.crewPage.callbackAfterAddCrew(updatedCrewMembers);
    }
    // Dismiss modal with updated crew data
    await this.modalCtrl.dismiss({
      updatedCrew: updatedCrewMembers,
      action: 'saved'
    }, 'confirm');
  }

  private async getCrewMembersFromDB() {
    try {
      console.log('[AddCrewPage] Fetching crew members from DB using DataService...');
      const allCrewMembers = await this.dataService.getCrewHeaderData();
      console.log('[AddCrewPage] All crew members from DataService:', allCrewMembers);
      const localCrewMembers = allCrewMembers.filter((member: CREW_HEADER) => member.SOURCE === 'LOCAL');
      console.log('[AddCrewPage] Local crew members:', localCrewMembers.length, localCrewMembers);
      this.crewMembers.set(localCrewMembers);
      this.checkCrewIdIsDistinctOrNot();
      console.log('[AddCrewPage] Crew members signal updated:', this.crewMembers().length);
    } catch (error) {
      console.error('[AddCrewPage] getCrewMembersFromDB error:', error);
      console.log('[AddCrewPage] No crew members found, starting with empty list');
      this.crewMembers.set([]);
      // Don't show error alert if it's just "No Crew data is present"
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (!errorMessage.includes('No Crew data is present')) {
        this.alertService.showAlert('Error', 'Failed to load crew members');
      }
    }
  }

  async removeNewEntry(crewMember: CREW_HEADER) {
    this.ngZone.run(async () => {
      const currentMembers = this.crewMembers();
      const index = currentMembers.indexOf(crewMember);
       if (index > -1) {
        const memberToRemove = currentMembers[index];
      try {
          if (memberToRemove.SOURCE === 'LOCAL') {
            // For local crew members, use USER_NAME as identifier if saved, otherwise just remove from UI
            if (memberToRemove.USER_NAME && memberToRemove.saved) {
              const result = await this.unviredSDK.dbDelete(
                AppConstants.TABLE_CREW_HEADER,
                { 'PERSON_NO': memberToRemove.PERSON_NO }
              );

              if (result.type === ResultType.success) {
                console.log('[AddCrewPage] Successfully deleted local crew member from database:', memberToRemove.USER_NAME);
              } else {
                console.error('[AddCrewPage] Error deleting local crew member from database:', result.error);
                this.alertService.showAlert('Error', 'Failed to delete crew member from database');
                return;
              }
            } else {
              console.log('[AddCrewPage] Removing unsaved local crew member from UI');
            }
            // Remove from current list in UI
            const updatedMembers = currentMembers.filter((_, i) => i !== index);
            this.crewMembers.set(updatedMembers);
            this.store.dispatch(triggerPrefillRefresh());

          } else {
            const result = await this.unviredSDK.dbDelete(
              AppConstants.TABLE_CREW_HEADER,
              { 'PERSON_NO': memberToRemove.PERSON_NO }
            );

            if (result.type === ResultType.success) {
              console.log('[AddCrewPage] Successfully deleted crew member from database:', memberToRemove.PERSON_NO);
              const updatedMembers = currentMembers.filter((_, i) => i !== index);
              this.crewMembers.set(updatedMembers);
              this.store.dispatch(triggerPrefillRefresh());
            } else {
              console.error('[AddCrewPage] Error deleting crew member from database:', result.error);
              this.alertService.showAlert('Error', 'Failed to delete crew member from database');
            }
          }
        } catch (error) {
          console.error('[AddCrewPage] Error deleting crew member:', error);
          this.alertService.showAlert('Error', 'Failed to delete crew member');
        }
      }
    });
  }

  addCrewMember() {
  this.ngZone.run(async () => {
    try {
      const newCrew = {
        PERSON_NO: this.utilityService.guid32(), 
        SOURCE: 'LOCAL',
        USER_NAME: '',
        DESIGNATION: '',
        RIG_NO: this.rigNo,
        LID: this.utilityService.guid32(), 
        COMP_CODE: '',
        EMAIL: '',
        MAINPH: '',
        CELPH: '',
        CREW_SIGN: '',
        GLOBALID: '',
        // UI-only fields
        isSelected: false,
        userNameUnsaved: false,
        designationUnsaved: false,
        crewIdDistinct: false,
        saved: false
      } as CREW_HEADER;
      if (newCrew.SOURCE === 'LOCAL') {
        // Generate a temporary ID for UI purposes only
        const tempId = 'temp_' + Date.now();
        const crewWithTempId = { ...newCrew, LID: tempId };

        const currentMembers = this.crewMembers();
        this.crewMembers.set([...currentMembers, crewWithTempId]);

        console.log('[AddCrewPage] Local crew member added to UI (not saved to DB yet)');
      } else {
        // For non-local crew members, insert into DB immediately
        const result = await this.unviredSDK.dbInsert(
          AppConstants.TABLE_CREW_HEADER,
          newCrew,
          true
        );
        if (result.type === ResultType.success) {
          // Merge back LID assigned by SDK
          const crewWithLID = { ...newCrew, LID: result.data?.LID };
          const currentMembers = this.crewMembers();
          this.crewMembers.set([...currentMembers, newCrew]);
          this.unviredSDK.logInfo(
            'AddCrewPage',
            'addCrewMember',
            'New crew member inserted and added to signal.'
          );
        } else {
          throw new Error(result.error || 'Insert failed');
        }
      }
    } catch (err) {
      console.error('[AddCrewPage] addCrewMember error:', err);
      this.unviredSDK.logError(
        'AddCrewPage',
        'addCrewMember',
        'Error creating new crew record: ' + JSON.stringify(err)
      );
      this.alertService.showAlert('Error', 'Failed to create new crew member.');
    }
  });
}

async saveCrewMember() {
  console.log('[AddCrewPage] saveCrewMember called');
  const members = this.crewMembers();
  console.log('[AddCrewPage] Current crew members:', members);
  this.completedCrewMembers = [];
  let isValid = true;
  // Validate all crew members
  for (let i = 0; i < members.length; i++) {
    const member = members[i];
    console.log(`[AddCrewPage] Validating member ${i + 1}:`, member);
    if ((!member.USER_NAME) && (!member.DESIGNATION)) {
      console.log(`[AddCrewPage] Skipping empty member ${i + 1}`);
      continue;
    } else if ((member.USER_NAME?.trim().length > 0) && (!member.DESIGNATION)) {
      await this.showAlert('Alert', `Please enter Position for Crew Member ${i + 1}`);
      isValid = false;
      break;
    } else if ((member.DESIGNATION?.trim().length > 0) && (!member.USER_NAME)) {
      await this.showAlert('Alert', `Please enter Username for Crew Member ${i + 1}`);
      isValid = false;
      break;
    } else if (member.USER_NAME?.trim().length === 0) {
      await this.showAlert('Alert', `Please enter valid Username for Crew Member ${i + 1}`);
      isValid = false;
      break;
    } else if (member.DESIGNATION?.trim().length === 0) {
      await this.showAlert('Alert', `Please enter valid Position for Crew Member ${i + 1}`);
      isValid = false;
      break;
    } else {
      // Valid entry
      member.USER_NAME = member.USER_NAME.trim();
      member.DESIGNATION = member.DESIGNATION.trim();
      member.RIG_NO = this.rigNo;
      // For local crew members, don't generate PERSON_NO
      if (!member.PERSON_NO || member.PERSON_NO.trim().length === 0) {
      member.PERSON_NO = this.utilityService.guid32();
    }
      member.designationUnsaved = false;
      member.userNameUnsaved = false;
      member.saved = true;
      this.completedCrewMembers.push(member);
    }
  }
  console.log('[AddCrewPage] Completed crew members to save:', this.completedCrewMembers);
  this.validInputField.set(isValid);
   if (isValid && this.completedCrewMembers.length > 0) {
    try {
      await this.utilityService.insertOrUpdateCrewList(this.completedCrewMembers);   
      console.log('[AddCrewPage] Crew members saved successfully using utility service');
      for (const deletedCrew of this.deletedCrewMembers()) {
        try {
          await this.unviredSDK.dbDelete(
            AppConstants.TABLE_CREW_HEADER,
            { 'PERSON_NO': deletedCrew.PERSON_NO }
          );
        } catch (deleteError) {
          console.error('[AddCrewPage] Error deleting crew member:', deleteError);
        }
      }
      this.deletedCrewMembers.set([]); 
      await this.goToCrewPage(); 
    } catch (error) {
      console.error('[AddCrewPage] Error saving crew members with utility service:', error);
      await this.alertService.showAlert('Error', 'Failed to save crew members');
    }
  }
}

  private async showAlert(title: string, message: string) {
    const alert = await this.alertCtrl.create({
      header: this.translate.instant(title),
      subHeader: message,
      buttons: [{
        text: this.translate.instant('OK')
      }],
    });
    await alert.present();
  }

  private async getStmrCrewFromDB(): Promise<void> {
    try {
      const query = "SELECT DISTINCT CREW_ID FROM STMR_CREW WHERE P_MODE IS NULL OR P_MODE = 'A' OR P_MODE = 'M'";
      const result = await this.unviredSDK.dbExecuteStatement(query);
      if (result.type === ResultType.success && result.data) {
        const crewIds: Array<{ Id: string }> = [];
        for (const row of result.data) {
          if (row.CREW_ID) {
            crewIds.push({ Id: row.CREW_ID });
          }
        }
        this.stmrCrewId.set(crewIds);
        console.log('[AddCrewPage] STMR CREW ID:', crewIds);
      } else {
        console.error('[AddCrewPage] Error fetching STMR crew:', result.error);
        this.alertService.showAlert('Error', 'Failed to load STMR crew data');
      }
    } catch (error) {
      console.error('[AddCrewPage] getStmrCrewFromDB error:', error);
      this.alertService.showAlert('Error', JSON.stringify(error));
    }
  }

  private checkCrewIdIsDistinctOrNot() {
    this.ngZone.run(() => {
      const members = this.crewMembers();
      const stmrIds = this.stmrCrewId();
      const updatedMembers = members.map(member => {
        member.saved = true;
        member.crewIdDistinct = this.checkCrewIdIsPresentOrNot(member.PERSON_NO, stmrIds);
        return member;
      });

      this.crewMembers.set(updatedMembers);
    });
  }

  private checkCrewIdIsPresentOrNot(personalNo: string, stmrIds: Array<{ Id: string }>): boolean {
    return stmrIds.some(stmrCrew => stmrCrew.Id === personalNo);
  }

  onChangeInput(_event: any, fieldType: 'userName' | 'designation', crew: CREW_HEADER) {
    crew.saved = false;
    if (fieldType === 'userName') {
      crew.userNameUnsaved = true;
    } else if (fieldType === 'designation') {
      crew.designationUnsaved = true;
    }
    // Update the signal to trigger change detection
    const currentMembers = this.crewMembers();
    this.crewMembers.set([...currentMembers]);
  }

}
