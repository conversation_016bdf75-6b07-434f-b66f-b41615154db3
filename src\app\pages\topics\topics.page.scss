/* ---------- SECTION LAYOUT (consistent padding) ---------- */
.section-header {
  background: #4a4a4a;
  color: #fff;
  font-size: 20px;
  padding: 12px 16px;
  font-weight: 500;
  margin-bottom: 0;
  display: flex;
  align-items: center;
}

.section-body {
  background: #fff;
  padding: 16px; /* ensure same padding used for topic details and forms */
  border-bottom: 1px solid #e0e0e0;
}

/* forms header variant (so Add button sits right) */
.forms-header {
  padding: 12px 16px;
  justify-content: space-between;
}

/* ---------- FIELD WRAPPER: labels above inputs ---------- */
.field-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

/* label default */
.field-label {
  font-size: 16px;
  color: #222; /* default label color */
  font-weight: 500;
  margin-bottom: 0;
  display: block;
}

/* required asterisk style (keeps the star) */
.required {
  color: #d32f2f;
  font-weight: bold;
}

/* Select-link styled as a button but inline/unstyled */
.select-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  text-align: left;
  color: #222;
}

/* ---------- Required / Filled visual states ---------- */
/* When required & empty: label and control turn red */
.field-label.required-empty,
.select-link.required-empty,
.datetime-input.required-empty,
.desc-textarea.required-empty {
  color: #d32f2f !important;
}

/* For inputs/controls also show red border for required-empty */
.desc-textarea.required-empty,
.datetime-input.required-empty {
  border-color: #d32f2f !important;
  color: #d32f2f !important;
}

/* When filled: label and control text becomes black */
.field-label.filled,
.select-link.filled,
.datetime-input.filled,
.desc-textarea.filled {
  color: #000 !important;
}

/* normal textarea style */
.desc-textarea {
  width: 100%;
  min-height: 80px;
  border: 2px solid #888;
  border-radius: 2px;
  font-size: 16px;
  padding: 8px;
  color: #222;
  background: #fff;
  resize: vertical;
}

/* datetime inside ion-item style override */
.datetime-input {
  width: 100%;
  --color: #222;
}

/* ---------- Documents styles ---------- */
.documents-section .doc-row {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  cursor: pointer;
}
.documents-section .doc-name {
  color: #222;
}

/* ---------- Forms styles ----------
   Keep the forms area visually aligned with Topic Details
*/
.forms-body .forms-row {
  display: flex;
  align-items: center;
  gap: 12px;
  min-height: 48px;
  padding: 8px 0;
  cursor: pointer;
}

.no-forms {
  color: #888;
  font-size: 16px;
  font-style: italic;
}

/* Add button inside header - right aligned */
.add-form-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* show counts in headers */
.section-header .count {
  font-weight: 600;
  margin-left: 8px;
  color: #fff;
}

/* ---------- Responsive: stack columns on small screens ---------- */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 18px;
  gap: 8px;
  box-sizing: border-box;
}

/* For desktop two columns, for mobile stacked */
.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
  padding-right: 16px;
  box-sizing: border-box;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* small screens: stack vertically */
@media (max-width: 768px) {
  .col-6 {
    flex: 0 0 100%;
    max-width: 100%;
    padding-right: 0;
  }

  .add-form-btn {
    font-size: 18px;
  }

  .section-header .count {
    font-weight: 600;
    color: #fff;
  }

  /* collapse date-time grid if present */
  .date-time-grid {
    grid-template-columns: 1fr !important;
  }
}

/* small tweaks for very small devices */
@media (max-width: 480px) {
  .add-form-btn {
    font-size: 16px;
  }

  .field-wrapper {
    gap: 6px;
  }

  .desc-textarea {
    min-height: 70px;
  }
}

/* Default = red */
.desc-label {
  color: #d32f2f; /* red */
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
  display: block;
}

/* Turns black once filled */
.desc-label.filled {
  color: #222; /* black */
}

/* Optional: make it bold red if empty (extra clarity) */
.desc-label.required-empty {
  font-weight: bold;
}

  .icon-wrapper {
    position: relative;
    width: 2.5rem;
    height: 2.5rem;
  
    .base-icon {
      font-size: 2.1rem;
      color: #444;
    }
  
    .star-icon,
    .wifi-icon {
      position: absolute;
      font-size: 0.9rem;
      color: orange;
    }
  
    .star-icon {
      top: -0.3rem;
      right: -0.3rem;
      color: orange;
    }
  
    .wifi-icon {
      top: -0.3rem;
      left: -0.8rem;
      color: orange;
    }

  }

    .thumbnail-tight {
    margin-right: 0px; // Reduce spacing between thumbnail and label
  }

