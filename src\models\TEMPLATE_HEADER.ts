import { UnviredCordovaSDK } from "@awesome-cordova-plugins/unvired-cordova-sdk/ngx";
import { DATA_STRUCTURE } from "./DATA_STRUCTURE";

export class TEMPLATE_HEADER extends DATA_STRUCTURE {
    TMPLT_ID: string;
    NAME: string;
    CATEGORY_DESC: string;
    CAT_ID: string;
    L_VER_NO: string;
    L_VER_ID: string;
    L_CRTD_BY: string;
    L_CRTD_ON: string;
    L_READ_FLAG: string;
    P_MODE: string;
    IS_ACTIVE: string;
    CHGD_BY: string;
    CHGD_ON: number;
    PBLSH_BY: string;
    PBLSH_ON: number;
    IS_DATA_ENH: string;

    constructor() {
        super();
        this.TMPLT_ID = '';
        this.NAME = '';
        this.CATEGORY_DESC = '';
        this.CAT_ID = '';
        this.L_VER_NO = '';
        this.L_VER_ID = '';
        this.L_CRTD_ON = '';
        this.L_CRTD_BY = '';
        this.L_READ_FLAG ='';
        this.P_MODE = '';
        this.IS_ACTIVE = '';
        this.CHGD_BY = '';
        this.CHGD_ON = 0;
        this.PBLSH_BY = '';
        this.PBLSH_ON = 0;
        this.IS_DATA_ENH = '';
            
    }
}

