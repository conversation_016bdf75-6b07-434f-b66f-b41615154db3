angular.module('PDTest')
    .directive('pdFormHeaderPrint', function() {
        function getHTML(model) {
            // Job ID, Rig #, Manager
            var html = '<div class="row">';
            html += '    <div class="col-sm-5">';
            html += '        <div class="form-group">';
            html += '            <label class="control-label">STMR Reference Number: </label>';
            html += '            <br>';
            html += '            <label class="control-label print-line">{{' + model + '.ParentReferenceNo}}</label>';
            html += '        </div>';
            html += '    </div>';
            html += '    <div class="col-sm-4">';
            html += '        <div class="form-group">';
            html += '            <label class="control-label">Form Reference Number: </label>';
            html += '            <br>';
            html += '            <label class="control-label print-line">{{' + model + '.ReferenceNo}}</label>';
            html += '        </div>';
            html += '    </div>';
            html += '    <div class="col-sm-4">';
            html += '        <div class="form-group">';
            html += '            <label class="control-label">Site Number:</label>';
            html += '            <br>';
            html += '            <span class="print-line">{{' + model + '.RigNumber}}</span>';
            html += '        </div>';
            html += '    </div>';
            html += '    <div class="col-sm-7">';
            html += '        <div class="form-group">';
            html += '            <label class="control-label">Site Manager: </label>';
            html += '            <br>';
            html += '            <span class="print-line">{{' + model + '.RigManager}}</span>';
            html += '        </div>';
            html += '    </div>';
            html += '</div>';
            html += '<div class="row">';
            html += '    <div class="col-sm-5">';
            html += '        <div class="form-group">';
            html += '            <label class="control-label">Date Created: </label>';
			html += '            <br>';

			html += '            <span ng-if="'+model+'.CreateDate" class="print-line">{{'+model+'.CreateDate|date: \'MMM dd yyyy H:mm\': \''+model+'.CreateDateTzOffset\'}} {{' +model+'.CreateDateTzAbbr}}  (24hrs)</span>';
			html += '            <span ng-if="!'+model+'.CreateDate" class="print-line">{{'+model+'.CreateDate}}</span>';

            html += '        </div>';
            html += '    </div>';
            html += '    <div class="col-sm-4">';
            html += '        <div class="form-group">';
            html += '            <label class="control-label">Completed By:  </label>';
            html += '            <br>';
            html += '            <span class="print-line">{{' + model + '.CompletedBy}}</span>';
            html += '        </div>';
            html += '    </div>';
            html += '    <div class="col-sm-4">';
            html += '        <div class="form-group">';
            html += '            <label class="control-label">Position: </label>';
            html += '            <br>';
            html += '            <span class="print-line">{{' + model + '.Position}}</span>';
            html += '        </div>';
            html += '    </div>';
            html += '    <div class="col-sm-7">';
            html += '        <div class="form-group">';
            html += '            <label class="control-label">Operator: </label>';
            html += '            <br>';
            html += '           <span class="print-line">{{' + model + '.Operator}}</span>';
            html += '        </div>';
            html += '    </div>';
            html += '</div>';

            return html;
        }

        return {
            restrict: 'EA',
            template: function(elem, attr) {
                return getHTML(attr.model);
            },
        };

    });