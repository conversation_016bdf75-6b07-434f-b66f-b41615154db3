import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { SettingsGearPage } from './settings-gear.page';

describe('SettingsGearPage', () => {
  let component: SettingsGearPage;
  let fixture: ComponentFixture<SettingsGearPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [SettingsGearPage],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SettingsGearPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
