import { Injectable } from '@angular/core';
import { Platform, AlertController } from '@ionic/angular';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';

declare var cordova:any

@Injectable({
  providedIn: 'root'
})
export class ExternalLinkService {
  private mimeTypes = {
    pdf: 'application/pdf',
    csv: 'text/csv',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    doc: 'application/msword',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    xls: 'application/vnd.ms-excel',
    txt: 'text/plain',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png'
  };

  constructor(
    private platform: Platform,
    private iab: InAppBrowser,
    private alertController: AlertController  // Inject AlertController
  ) {}

  openFormtoPrint(url: string): void {
    if (window?.electronAPI?.openExternal) {
      //Electron (via preload.js contextBridge)
      window.electronAPI.openExternal(url);
    } else if (cordova.platformId == 'ios') {
      // iOS 
      this.iab.create(url, '_system');
    } else {
      // Fallback for web 
      window.open(url, '_blank');
    }
  }
  private getMimeType(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase() || '';
    return this.mimeTypes[extension as keyof typeof this.mimeTypes] || 'application/pdf';
  }

  private async handleError(error: any): Promise<void> {
    console.error('Error opening file:', error);
    if (error.status) {
      console.error('Error status:', error.status);
    }
    if (error.message) {
      console.error('Error message:', error.message);
    }

    const alert = await this.alertController.create({
      header: 'Error',
      message: 'The document could not be opened. It may not exist on your device.',
      buttons: ['OK']
    });

    await alert.present();
  }

  private handleSuccess(): void {
    console.log('File opened successfully');
  }

  openDocument(path: string, mimeType?: string): void {
    try {
      const detectedMimeType = mimeType || this.getMimeType(path);
      console.log(`Opening document with MIME type: ${detectedMimeType}`, path);

      if (window?.electronAPI?.openExternal) {
        // Electron (via preload.js contextBridge)
        window.electronAPI.openExternal(path);
      } else if (cordova.platformId === 'ios') {
        // iOS with proper MIME type handling
        cordova.plugins.fileOpener2.open(
          path,
          detectedMimeType,
          {
            error: (e: any) => this.handleError(e),
            success: () => this.handleSuccess()
          }
        );
      } else {
        // Enhanced web fallback with MIME type support
        const link = document.createElement('a');
        link.href = path;
        link.type = detectedMimeType;
        link.target = '_blank';
        link.click();
      }
    } catch (error) {
      this.handleError(error);
    }
  }
}
