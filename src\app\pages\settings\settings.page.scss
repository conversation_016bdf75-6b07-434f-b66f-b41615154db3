.sync-file-name {
  display: inline-block;
  max-width: 14em; /* Adjust to fit about 9-10 words */
  vertical-align: bottom;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sync-content-align {
  margin-left: 15px;           /* Aligns to text start */
  padding-top: 4px;
  padding-bottom: 8px;
  width: 100%;
}
.accordion-description {
  margin: 4px 0 8px 0;
  font-size: 1rem;
  line-height: 1.32;
}

.accordion-header-row .icon-and-label {
  display: flex;
  align-items: center;
}
.header-icon {
  margin-right: 14px;   /* Space between icon and text */
  font-size: 1.2em;
}

.compact-list {
  margin: 0;
  padding-left: 16px; /* Keep bullet indent */
}

.compact-list li {
  margin: 2px 0;
}
  .custom-modal::part(backdrop) {
  background: rgba(0, 0, 0, 0.6);  
}
 
.custom-modal::part(content) {
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.3);
  transform: scale(1.01);
  border-radius: 12px;
}
.icon-with-badge {
  position: relative;
  display: inline-block;
}

.icon-with-badge .badge {
  position: absolute;
  top: -11px;
  right: -20px;
  --background: var(--ion-color-primary);
  color: white;
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 14px;
  min-width: 18px;
  text-align: center;
  line-height: 1;
  // font-weight:bold;
}
ion-toolbar ion-button {
  min-width: 100px;     
  height: 36px;         
  justify-content: center;
  text-align: center;
  --padding-start: 8px;
  --padding-end: 8px;
}

ion-toolbar ion-button .badge {
  right: -4px;
}

