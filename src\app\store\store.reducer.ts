import { createReducer , on } from "@ngrx/store";
import { increment, decrement, reset, addNotification } from './store.actions';
import * as RigActions from './store.actions';
import { FormsState, ReleaseNotesState, SearchState, STMRState, SyncState } from './app.state';
import { NotificationState, PrefilledState, ProgressState, RigState, TemplateState } from "./app.state";
export interface CounterState{
    counter : number
}


export const initialCountState: CounterState = {
    counter: 0
}

export const counterReducer = createReducer( 
    initialCountState,
    on(increment , state => ({...state, counter: state.counter + 1})),
    on(decrement , state => ({...state, counter: state.counter - 1})),
    on(reset, state => ({...state, counter: 0}))
)


export const initialRigState: RigState = {
  rigData: null,
  error: null,
  loadedFromDb: false,
  loadedFromServer: false,
  prefillData: null,
  prefillNeedsRefresh: false ,
  spConfig: [],
  spConfigLoaded: false
};

export const rigReducer = createReducer(
  initialRigState,
on(RigActions.switchRigSuccess, (state, { rigData }) => ({
  ...state,
  rigData,
  loadedFromServer: true,
  error: null

})),
  on(RigActions.switchRigFailure, (state, { error }) => ({
    ...state,
    error
  })),
  on(RigActions.clearRigState, (state) => ({
    ...state,
    rigData: null,
    error: null
  })),


  on(RigActions.loadRigFromDbSuccess, (state, { rigData }) => ({
    ...state,
    rigData,
    loadedFromDb: true, // ✅ MUST SET THIS
    error: null
  })), 

  on(RigActions.loadRigFromDbFailure, (state, { error }) => ({
    ...state,
    rigData: null,
    loadedFromDb: true, // ✅ still set this to true so the selector emits
    error
  })),


  on(RigActions.loadPrefilledDataSuccess, (state, { prefilledData }) => ({
    ...state,
    prefillData: prefilledData,
    prefillNeedsRefresh: false
  })),

 on(RigActions.triggerPrefillRefresh, state => ({
   ...state,
   prefillNeedsRefresh: true
 })),
 on(RigActions.prefillRefreshed, state => ({
   ...state,
   prefillNeedsRefresh: false
 })),
 on(RigActions.loadSpConfigSuccess, (state, { data }) => ({
    ...state,
    spConfig: data,
    spConfigLoaded: true
  })),
  on(RigActions.loadSpConfigFailure, (state) => ({
    ...state,
    spConfig: [],
    spConfigLoaded: false
  }))
);


const initialNotifState: NotificationState = {
  events: [],
};

export const notificationReducer = createReducer(
  initialNotifState,
  on(addNotification, (state, { notification }) => ({
    ...state,
    events: [...state.events, notification], // ✅ appends new notification
  }))
);

export const initialTemplateState: TemplateState = {
  templates: [],
  error: null,
  loadedFromDb: false,
  loadedFromServer: false,
};


export const templateReducer = createReducer(
  initialTemplateState,

  on(RigActions.loadAllTemplatesDbSuccess, (state, { templates }) => {
    console.log('Reducer updating templates:', templates); // ✅ Add this for debugging
    return {
      ...state,
      templates,             // Add the loaded templates to the state
      loadedFromDb: true,    // Mark as loaded
      error: null,           // Clear any previous errors
    };
  }),
  
  on(RigActions.loadAllTemplatesDbFailure, (state, { error }) => ({
    ...state,
    error,                // Store the error
    loadedFromDb: false,  // Mark as not loaded due to failure
  })),

  on(RigActions.clearTemplateState, (state) => ({
    ...state,
    templates: [],
    error: null,
    loadedFromDb: false,
    loadedFromServer: false,
  })),



);

export const initialState: ProgressState = {
  percentage: 0,
  error: null,
};

export const progressReducer = createReducer(
  initialState,
  on(RigActions.loadProgressBarSuccess, (state, { percentage }) => ({
    ...state,
    percentage,
    error: null
  })),
  on(RigActions.loadProgressBarFailure, (state, { error }) => ({
    ...state,
    error
  }))
);


export const initialPrefilledState: PrefilledState = {
  prefilledData: null,
  loaded: false,
  error: null,
};

export const prefilledReducer = createReducer(
  initialPrefilledState,


  on(RigActions.loadPrefilledDataSuccess, (state, { prefilledData }) => {
  console.log('[Reducer] Loaded prefilled data:', prefilledData);
  return {
    ...state,
    prefilledData,
    loaded: true,
    error: null,
  };
}),
on(RigActions.loadPrefilledDataFailure, (state, { error }) => ({
  ...state,
  error,                // Store the error
  loaded: false,  // Mark as not loaded due to failure
})),


on(RigActions.loadPrefilledData, (state) => ({
  ...state,
  loaded: false,  // Mark as not loaded when the load action is dispatched
})),
);

// SYNC slice
export const initialSyncState: SyncState = {
  isSyncing: false,
  progress: 0,
  currentSyncFile: '',
  graphToken: null,
  siteHeaders: [],
  siteMeta: [],
  baseLocalPath: null,
  error: null,
  initialDataDownloadInProgress: false,
};

export const syncReducer = createReducer(
  initialSyncState,
  on(RigActions.syncSetIsSyncing, (state, { isSyncing }) => ({ ...state, isSyncing })),
  on(RigActions.syncSetProgress, (state, { progress }) => ({ ...state, progress })),
  on(RigActions.syncSetCurrentFile, (state, { currentSyncFile }) => ({ ...state, currentSyncFile })),

  on(RigActions.loadGraphTokenSuccess, (state, { token }) => ({ ...state, graphToken: token, error: null })),
  on(RigActions.loadGraphTokenFailure, (state, { error }) => ({ ...state, error })),

 on(RigActions.loadSharepointSitesSuccess, (state, { data }) => {
  console.log('Reducer putting site headers in state:', data);
  return { ...state, siteHeaders: data as any, error: null };
}),

  on(RigActions.loadSharepointSitesFailure, (state, { error }) => ({ ...state, error })),

  on(RigActions.loadSiteMetaSuccess, (state, { siteMeta }) => ({ ...state, siteMeta, error: null })),
  on(RigActions.loadSiteMetaFailure, (state, { error }) => ({ ...state, error })),

  on(RigActions.updateDeviceSiteMetaFailure, (state, { error }) => ({ ...state, error })),
  on(RigActions.checkServerSiteMetaFailure, (state, { error }) => ({ ...state, error })),

  on(RigActions.rigDocSyncStart, (state) => ({ ...state, isSyncing: true, progress: 0, currentSyncFile: '', error: null })),
  on(RigActions.rigDocSyncEnd, (state) => ({ ...state, isSyncing: false, progress: 0, currentSyncFile: '' })),
  on(RigActions.rigDocSyncError, (state, { error }) => ({ ...state, isSyncing: false, error })),

  // Initial Data Download tracking
  on(RigActions.initialDataDownloadStart, (state) => ({
    ...state,
    initialDataDownloadInProgress: true,
    error: null
  })),
  on(RigActions.initialDataDownloadError, (state, { error }) => ({
    ...state,
    initialDataDownloadInProgress: false,
    error
  })),
  on(RigActions.initialDataDownloadComplete, (state) => ({
    ...state,
    initialDataDownloadInProgress: false
  }))
);


export const initialFormsState: FormsState = {
  forms: [],
  loading: false,
  error: null,

};

export const formsReducer = createReducer(

  initialFormsState,

  on(RigActions.loadAllFormsFromDb, (state) => {

    console.log('[Reducer] loadAllFormsFromDb, state:', state);

    return { ...state, loading: true, error: null, forms: [] }; // reset forms when starting a new load

  }),

  on(RigActions.loadAllFormsDbSuccess, (state, { forms }) => {

    console.log('[Reducer] loadAllFormsDbSuccess, forms:', forms);

    return { ...state, loading: false, forms };

  }),

  on(RigActions.loadAllFormsDbFailure, (state, { error }) => {

    console.log('[Reducer] loadAllFormsDbFailure, error:', error);

    return { ...state, loading: false, error};

  })

);

export const initialSTMRState: STMRState = {
  stmrData: [],
  error: null,
  loadedFromDb: false,
  loadedFromServer: false,
};

export const stmrReducer = createReducer(
  initialSTMRState,

  on(RigActions.loadStmrDataAndTopicsFromDb, (state) => {
    console.log('[STMR Reducer] Loading STMR data - resetting state');
    return {
      ...state,
      loadedFromDb: false,
      error: null,
      stmrData: [],
    };
  }),

  on(RigActions.loadStmrDataAndTopicsFromDbSuccess, (state, { stmrData, stmrTopicData }) => {
    // console.log('[STMR Reducer] STMR data loaded successfully:', stmrData.length, 'headers,', stmrTopicData.length, 'topics');
    // merge topics into each header
    const stmrDataWithTopics = stmrData.map(header => ({
      ...header,
      topics: stmrTopicData.filter(topic => topic.STMR_ID === header.STMR_ID)
    }));
  
    return {
      ...state,
      stmrData: stmrDataWithTopics,
      error: null,
      loadedFromDb: true
    };
  }),
  

  // Failure → clear data and store error
  on(RigActions.loadStmrDataAndTopicsFromDbFailure, (state, { error }) => ({
    ...state,
    stmrData: [],
    stmrTopicData: [],
    error,
    loadedFromDb: false,
  }))
);


export const initialSearchState: SearchState = { query: '' };


export const searchReducer = createReducer(
  initialState,
  on(RigActions.updateSearchQuery, (state, { query }) => ({
    ...state,
    query
  }))
);
export const initialReleaseNotesState: ReleaseNotesState = {
  releaseNotes: [],
  error: null,
  loadedFromDb: false,
};

export const releaseNotesReducer = createReducer(
  initialReleaseNotesState,
 on(RigActions.loadReleaseNotesDbSuccess, (state, { releaseNotes }) => {
  console.log('[Reducer] loadReleaseNotesDbSuccess - Count:', releaseNotes.length);
  return {
    ...state,
    releaseNotes,
    loadedFromDb: true,
    error: null,
  };
}),

  on(RigActions.loadReleaseNotesDbFailure, (state, { error }) => ({
    ...state,
    error,
    loadedFromDb: false,
  }))
);


