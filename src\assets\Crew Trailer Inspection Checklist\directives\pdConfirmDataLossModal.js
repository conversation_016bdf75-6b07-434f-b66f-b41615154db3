angular.module('PDTest')
.directive('pdConfirmDataLossModal', function() {
    var html='    <div id="confirmDataLossModal" class="modal fade" role="dialog" data-backdrop="static">';
    html +='        <div class="modal-dialog">';
    html +='            <div class="modal-content">';
    html +='                <div class="modal-header">';
    html +='                    <h3 class="modal-title">Confirm Data Loss</h3>';
    html +='                </div>';
    html +='                <div class="modal-body">';
    html +='                    <p>Changing the Inspection Type will reset the form.<br><br>All checklist data will be permanently lost.<br><br>Do you want to proceed?</p>';
    html +='                </div>';
    html +='                <div class="modal-footer">';
    html +='                    <button type="button" class="btn btn-primary" data-dismiss="modal"';
    html +='                        ng-click="handleModalOk()">Okay</button>';
    html +='                    <button type="button" class="btn btn-default" data-dismiss="modal" ng-click="handleModalCancel()">Cancel</button>';
    html +='                </div>';
    html +='            </div>';
    html +='        </div>';
    html +='    </div>';

    return {
        restrict: 'EA',
        template: html
    };

});