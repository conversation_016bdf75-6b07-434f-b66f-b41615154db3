angular.module('PDTest')
    .controller('PSCtrl', function ($rootScope, $scope, $filter, $location, $anchorScroll, $parse, CommonFactory, ModelFactory) {

        loadDefaults();
        loadFormDefaults();

        // START: Framework functions

        // Called by loadDefaultValues
        // 1. Allows the container app tp pass list data collected
        //    from SAP/DAR/etc into this form
        // 2. Creates a default form data object using values from
        //    the list object
        // 3. Creates the form section for navigation (if any)
        $scope.loadValues = function (lists) {
            try {
                // any data provided?
                if ($rootScope.form.debug) {
                    // load test data if needed
                    if (!lists) { lists = ModelFactory.testDefaultValues(); }
                } else {
                    if (!lists) { throw new Error('No data provided from loadDefaultValues.'); }
                }

                // lists, default values and logo
                $rootScope.lists = lists;

                // load form data and sections
                $rootScope.data = ModelFactory.default(lists);
                $rootScope.sections = ModelFactory.sections();

                checkAllSignaturesCaptured();
                $scope.checkAllItemsSelected();

                $scope.$apply();
            }
            catch (err) {
                CommonFactory.logError('IndexFormCtrl.loadValues', err);
            }
        };

        // Called by loadPreviousValues
        // Passes a previously created form json object
        // to this form.
        $scope.loadPrevious = function (data) {
            try {
                // any data provided?
                if ($rootScope.form.debug) {
                    // load test data if needed
                    if (!data) { data = ModelFactory.testPreviousValues(); }
                } else {
                    if (!data) { throw new Error('No data provided from loadPreviousValues.'); }
                }

                // data passed in must
                // be an json object and
                // not a string representation
                // Apply to form data model
                $rootScope.data = data;

                // when saving the form, a lot of data required to render the form is removed so that we don't save empty rows
                // when re-loading the same form, we need to re-add the default data in case the form type is switched, so the
                // form can still load (model.js sets this up, then loaded from form json subsequently)
                ensureDefaultData();

                // check state of any required signatures
                checkAllSignaturesCaptured();
                $scope.checkAllItemsSelected();

                // reset form edit state
                $scope.contentForm.$setPristine();
            }
            catch (err) {
                CommonFactory.logError('IndexFormCtrl.loadPrevious', err);
            }
        };

        function ensureDefaultData() {
           if ($rootScope.data.CrewTrailerNewMonthly.length === 0) {
           $rootScope.data.CrewTrailerNewMonthly = [ModelFactory.getCrewTrailerNewDefaultData()];
            }
            else {
              var form_section = $filter('filter')($rootScope.FormSectionData, { section_name: $rootScope.data.CrewTrailerNew.InspType }, true)[0];
             
                // get the line items for the group
                for (var i = 0; i < form_section.line_items.length; i++) {
                    var group = $filter('filter')(form_section.groups, { groupName: form_section.line_items[i].groupName }, true)[0];

                    if (group) {
                        form_section.line_items[i].removed = !$rootScope.data[form_section.table][0][group.prop];
                    }
                }
            }

        };

        // Called by generateJSON
        // Allows the container app to get the forms
        // data as a json object
        // Use angular.toJson to strip out any $$ objects
        // and/or hash keys that Angular creates automatically
        $scope.getDataObject = function () {
            try {
                var json = JSON.parse(angular.toJson($rootScope.data));

                if ($rootScope.form.debug) {
                    console.log('** IndexFormCtrl.getDataObject:');
                    console.dir(json);
                    console.dir(JSON.stringify(json));
                }
                return json;
            }
            catch (err) {
                CommonFactory.logError('IndexFormCtrl.getDataObject', err);
            }
        };

        // Called by helper. js setStatus and sets
        // status of form to either submitted
        // or not
        $scope.updateStatus = function (status) {
            try {
                // any data provided?
                if ($rootScope.form.debug) {
                    // load test data if needed
                    if (!status) { status = ModelFactory.testSetStatus(); }
                } else {
                    if (!status) { throw new Error('No data provided from setStatus.'); }
                }

                if (status.FormStatus) {
                    $rootScope.submitted = (status.FormStatus === 'SUBM' || status.FormStatus === 'SKIP'); }
                if (status.IsSTMR) { $rootScope.isPackage = status.IsSTMR; }
            }
            catch (err) {
                CommonFactory.logError('IndexFormCtrl.updateStatus', err);
            }
        };

        // Tell the container app we want to save
        // 1. Set any audit fields
        // 2. Tell the container app we want to save
        // 3. Reset form (i.e. not dirty)
        $scope.save = function (exit) {
            try {
                // update form audit
                $rootScope.data[$rootScope.form.mainTable].UpdateDate = new Date();
                $rootScope.data[$rootScope.form.mainTable].UpdatedBy = $rootScope.lists.USER_NAME;

                // either save or save and exit
                // ** 2018-Apr-03: RD
                // Save process is dependant on device platform
                CommonFactory.callSaveAction(exit, $rootScope.lists.PLATFORM);

                // inform user and reset form input state
                CommonFactory.showSuccessAlert('Form data saved.');
                $scope.contentForm.$setPristine();
            }
            catch (err) {
                CommonFactory.logError('IndexFormCtrl.save', err);
            }
        };

        // Tell the container app we want to submit this form
        $scope.submit = function () {
            try {
                CommonFactory.callSubmitAction($rootScope.lists.PLATFORM);
            }
            catch (err) {
                CommonFactory.logError('IndexFormCtrl.submit', err);
            }
        };

        // Tell the container app we want to skip this form		
        $scope.skipForm = function () {
            try {
                CommonFactory.callSkipAction($rootScope.lists.PLATFORM);
            } catch (err) {
                CommonFactory.logError('IndexFormCtrl.skip', err);
            }
        };

        $scope.setStyle = function () {
            if ($rootScope.siteStyle === "pdstylenormal") {
                $rootScope.siteStyle = "pdstylecontrast";
            } else {
                $rootScope.siteStyle = "pdstylenormal";
            }
        };

        // Tell the container app that the user
        // has clicked the back button (chevron)
        // but check for unsaved changes
        $scope.back = function () {
            try {
                // check for unsaved changes
                if (($scope.contentForm.$dirty) && (!$rootScope.submitted)) {
                    // warn user
                    var element = angular.element('#confirmModal');
                    element.modal('show');
                } else {
                    // exit as usual
                    CommonFactory.callBackAction($rootScope.lists.PLATFORM);
                }
            }
            catch (err) {
                CommonFactory.logError('IndexFormCtrl.back', err);
            }
        };

        // Tell container app that we want to
        // create an SAP notification
        $scope.notification = function () {
            try {
                CommonFactory.callNotificationAction($rootScope.lists.PLATFORM);
            }
            catch (err) {
                CommonFactory.logError('IndexFormCtrl.notification', err);
            }
        };

        // Called by the Confirm Modal
        // Indicates the user wants to exit
        // without saving changes
        $scope.exitConfirmed = function () {
            try {
                CommonFactory.callBackAction($rootScope.lists.PLATFORM);
            }
            catch (err) {
                CommonFactory.logError('IndexFormCtrl.exitConfirmed', err);
            }
        };

        // Handle print button
        // In ios made, print as usual
        // In windows device mode then let the
        // Unvired client app handle printing
        $scope.print = function () {
            CommonFactory.callPrintAction($rootScope.lists.PLATFORM);
        };

        $scope.gotoSection = function (item) {
            $anchorScroll.yOffset = 50;
            $location.hash(item.value);
            $anchorScroll();
        };

        $scope.setSignatureModal = function (signatureObjectName, signature) {
            try {
                // kepp track of the model property that needs a signature
                $scope.signatureObjectName = signatureObjectName;

                // assign an exisitng signature image to
                // the signature pad control if one was
                // passed in
                if (signature) { $scope.signatureImage = signature; }
            }
            catch (err) {
                CommonFactory.logError('IndexFormCtrl.setSignatureModal', err);
            }
        };

        $scope.gotSignature = function () {
            try {
                // Get the signature image from
                // the signature pad control
                var result = $scope.acceptSignature();

                // Assigns the signatue to the model property
                // that called the signature pad
                var signature = $parse($scope.signatureObjectName);

                if (result.isEmpty) {
                    // signature was cleared
                    signature.assign($scope, undefined);
                } else {
                    // we have a signature
                    signature.assign($scope, result.dataUrl);
                }

                // cleaar signature for next input
                $scope.clearSignature();

                // check we have all signatures
                checkAllSignaturesCaptured();

                // if a signature was entered then put form in edit mode
                $scope.contentForm.$dirty = true;
            }
            catch (err) {
                CommonFactory.logError('IndexFormCtrl.gotSignature', err);
            }
        };

        // if create user exists in the crew list
        // then get their position of possible
        $scope.createdUserChanged = function (username) {
            if (username) {
                var result = $filter('filter')($rootScope.lists.CREW, { USER_NAME: username }, true);
       

                if (result.length !== 0 && CommonFactory.userPositionIsInList(result[0].DESIGNATION)) {
                    $rootScope.data[$rootScope.form.mainTable].Position = result[0].DESIGNATION;
                }
                // if created user exists in crew list but isn't an exact match to model list positions (ie Rig Manager, Rotator vs Rig Manager)
                else if (result.length !== 0 && CommonFactory.userPositionIsInList(result[0].DESIGNATION) == false) {
                    var positionArr = result[0].DESIGNATION.split(',');
                    var posListArr = ModelFactory.positions();

                    $rootScope.data[$rootScope.form.mainTable].Position = CommonFactory.findCommonItem(positionArr, posListArr);

                }

                else {
                    $rootScope.data[$rootScope.form.mainTable].Position = "";
                }
            }

        };

        // CommonFactory.createdUserChanged; -Updated for Shops/Offices
        // if create user exists in the crew list  then get their position if possible
        $scope.createdUserChangedShop = function (username) {
            if (username) {
                var result = $filter('filter')($rootScope.lists.CREW, { USER_NAME: username }, true);

                if (result.length !== 0 && CommonFactory.userPositionIsInListShop(result[0].DESIGNATION)) {
                    $rootScope.data[$rootScope.form.mainTable].Position = result[0].DESIGNATION;
                }

                // if created user exists in crew list but isn't an exact match to model list positions (ie Rig Manager, Rotator vs Rig Manager)
                else if (result.length !== 0 && CommonFactory.userPositionIsInListShop(result[0].DESIGNATION) == false) {
                    var positionArr = result[0].DESIGNATION.split(',');
                    var posListArr = ModelFactory.positionsShop();

                    $rootScope.data[$rootScope.form.mainTable].Position = CommonFactory.findCommonItem(positionArr, posListArr);

                }

                else {
                    $rootScope.data[$rootScope.form.mainTable].Position = "";

                }
            }

        };


        function loadDefaults() {
            // available to all views and controllers
            $rootScope.allsignaturesCollected = false;
            $rootScope.sections = [];
            $rootScope.siteStyle = "pdstylenormal";
            $rootScope.data = undefined;
            $rootScope.lists = undefined;
            $rootScope.submitted = false;
            $rootScope.allowSubmit = true;
            $rootScope.isPackage = false;

            // load data from formData.js file if present
            loadFormData();
        };

        //Uses in the pdFormHeader to differentiate Rig vs Shop and Show/Hide associated Completed By(User)/Position lists
        $scope.rigPatternValid = function (rigNumber) {

            $scope.show = true;

            let rigRegEx = /^\d{3}$/;

            if (rigNumber && rigRegEx.test(rigNumber)) {
                $scope.show = true;
                return true;

            } else {
                $scope.show = false;
                return false;

            }

        };

        // If the server has injected a formData.js
        // file into the project root, then read the
        // data from it into the object model
        function loadFormData() {
            try {
                // form data and sections
                if (typeof formData !== 'undefined') {
                    $rootScope.data = formData;
                    $rootScope.sections = ModelFactory.sections();

                    // since this data is from the server,
                    // it has already been submitted
                    $rootScope.submitted = true;
                }

                // check for logo
                if (typeof companyLogo !== 'undefined') {
                    $rootScope.lists = { LOGO: companyLogo };
                }
            }
            catch (err) {
                CommonFactory.logError('IndexFormCtrl.loadFormData', err);
            }
        }

        // END: Framework functions

        // ---------------------------------------------------------------------------------------
        // START: Form specific functions

        // watch form valid state and all other properties that
        // would affect the ability to submit the form
        $scope.$watchGroup(['contentForm.$valid', 'allsignaturesCollected', 'allItemsCollected'], function (newVal, oldVal) {
            if ($scope.contentForm.$valid && // all required fields must be filled
                $rootScope.allsignaturesCollected && // all required signatures must be entered
                $rootScope.allItemsCollected && // any additional required data?
                !$rootScope.submitted) { // don't update the model if already submitted
                if ($rootScope.data) { $rootScope.data.CanBeSubmitted = true; }
            } else {
                if ($rootScope.data) { $rootScope.data.CanBeSubmitted = false; }
            }
        });

        $scope.resizeTextArea = function ($event) {
            $event.target.style.height = $event.target.scrollHeight + "px";
        };

        // check that all required items
        // have been selected
        $scope.checkAllItemsSelected = function () {
            $scope.contentForm.$dirty = true;
            let inspection_type = $rootScope.data[$rootScope.form.mainTable].InspType;
            if (inspection_type === undefined)
                return;

            // get total required and not removed
            var form_section = $filter('filter')($rootScope.FormSectionData, { section_name: inspection_type }, true)[0];
            var data = $rootScope.data[form_section.table][0];
            var totalRequired = 0;
            var captured = 0;

            var group_visible_lookup = {};
            for (var i = 0; i < form_section.groups.length; i++) {
                let group = form_section.groups[i];
                // removed								// visible
                group_visible_lookup[group.groupName] = !$rootScope.data[form_section.table][0][group.prop];  //removed is the opposite of visible...
            }

            for (var i = 0; i < form_section.line_items.length; i++) {
                let line_item = form_section.line_items[i];

                let needs_validation = true;

                // on reload the the required status as a per-line property needs to get re-setup, it's not persisted so
                // on a reload the original values are taken. since the group visible property matches all the lines in the group,
                // look at the group and set the line required
                line_item.removed = group_visible_lookup[line_item.groupName];

                if (line_item.removed !== false)
                    needs_validation = false;
                if (line_item.ctrl === '')
                    needs_validation = false;

                if (needs_validation === true) {
                    if(line_item.dataType === 1){
                        totalRequired += 1;
                        if (data[line_item.ctrl]) {
                            captured += 1;
                        }
                    }
                   
                }
            }

          
          var allCollected = (totalRequired === captured) && $rootScope.data.CrewTrailerNew.InspType !== undefined && checkAllSignaturesCaptured;
            


            $rootScope.allItemsCollected = allCollected;
        };

        function compareLineItemType(actual, expected) {
            for (let i = 0; i < expected.length; i++) {
                if (actual === expected[i]) return true;
            }
        };

        $scope.getLineItemsForGroup = function (group, inspection_type, types) {
            var form_section = $filter('filter')($rootScope.FormSectionData, { section_name: inspection_type }, true)[0];
            var line_items = $filter('filter')(form_section.line_items, { groupName: group }, true);
            var line_items_by_type = $filter('filter')(line_items, { dataType: types }, compareLineItemType);
            return line_items_by_type;
        }

        $scope.getGroupsForSection = function (section_name) {
            if (section_name === undefined)
                return;

            var form_section = $filter('filter')($rootScope.FormSectionData, { section_name: section_name }, true)[0];
            return form_section.groups;
        };

        $scope.updateGroup = function (group, inspection_type) {
            var form_section = $filter('filter')($rootScope.FormSectionData, { section_name: inspection_type }, true)[0];

            // toggle the button state
            $rootScope.data[form_section.table][0][group.prop] = !$rootScope.data[form_section.table][0][group.prop];

            // get the line items for the group
            var filtered_line_items = $filter('filter')(form_section.line_items, { groupName: group.groupName });

            // set the line_item removed state based on the group visible state
            for (var i = 0; i < filtered_line_items.length; i++) {
                filtered_line_items[i].removed = $rootScope.data[form_section.table][0][group.prop];
            }

            $scope.checkAllItemsSelected();
        };

        $scope.getSectionTable = function (inspection_type) {
            return $filter('filter')($rootScope.FormSectionData, { section_name: inspection_type }, true)[0].table;
        };

        function loadFormDefaults() {
            $rootScope.form = ModelFactory.form();
            $rootScope.allItemsCollected = false;
            $rootScope.FormSectionData = ModelFactory.FormSectionData();
            $rootScope.positions = ModelFactory.positions();
            $rootScope.positionsShop = ModelFactory.positionsShop();
        }

        // Logic to set the flag 'allsignaturesCollected' based
        // on all required signatures.
        // This flag is used to enable/disable the submit button
        // (along with the form valid state)
        function checkAllSignaturesCaptured() {
            var allFilled = true;

            // single fields
          
        //  if (allFilled && $rootScope.data.CrewTrailerNew.SignOffRigManagerSignature === undefined)      
          //      allFilled = false;
          
         // if (allFilled && $rootScope.data.CrewTrailerNew.SignOffCompletedBySignature === undefined)         
          //      allFilled = false;
          

            // fields that are in a list
           //  if (allFilled) {
            
              
        //      for (var i = 0; i < $rootScope.data.CrewTrailerNewDocumentSignoffs.length; i++) {      
         //           if (!allFilled)
         //               break;

               
         //         if ($rootScope.data.CrewTrailerNewDocumentSignoffs[i].CompletedBySignature === undefined)
          //              allFilled = false;
          //      }

            $scope.contentForm.$dirty = true;
            $rootScope.allsignaturesCollected = allFilled;

        };

        
        $scope.setPositionForSecondaryVisitor = function(signoff) {
            var completedBy = signoff.CompletedBy;
            if (completedBy) {
                var crew = $filter('filter')($rootScope.lists.CREW, {
                    USER_NAME: completedBy
                });
                if (crew.length === 1) {
                    signoff.CompletedByPosition = crew[0].DESIGNATION;
                }
            }
        };

        $scope.setSignatureModal = function(signatureObjectName, signature) {
            try {
                // kepp track of the model property that needs a signature
                $scope.signatureObjectName = signatureObjectName;

                // assign an exisitng signature image to
                // the signature pad control if one was
                // passed in
                if (signature) {
                    $scope.signatureImage = signature;
                }
            } catch (err) {
                CommonFactory.logError('IndexFormCtrl.setSignatureModal', err);
            }
        };


        $scope.collectVal = function (val) {
            $rootScope.old_val = val;
        };

        $scope.setVisibleParentSection = function (section_name) {
            // don't show the modal if the inspection type has never been changed
            if ($rootScope.old_val === undefined)
                return;

            $scope.checkAllItemsSelected();
            angular.element('#confirmDataLossModal').modal('show');
        };

        $scope.handleModalCancel = function () {
          $rootScope.data.CrewTrailerNew.InspType = $rootScope.old_val;  
            $scope.checkAllItemsSelected();
        };

        function determineSectionToClear() {
            var types = ModelFactory.InspectTypes();
            if (types.length === 1)
                return undefined;

          var idx = types.indexOf($rootScope.data.CrewTrailerNew.InspType);
            types.splice(idx, 1);
            return types[0];
        };

        $scope.handleModalOk = function () {
            // figure out which section needs to be cleared
            var section_to_clear = determineSectionToClear();
            if (section_to_clear == undefined)
                return;

            // get the section to clear
            var form_section = $filter('filter')($rootScope.FormSectionData, { section_name: section_to_clear }, true)[0];

            // reset all the non-visible groups
            for (let i = 0; i < form_section.groups.length; i++) {
                var group = form_section.groups[i];
                $rootScope.data[form_section.table][0][group.prop] = true;
            }

            // reset all the buttons and fields in the group
            for (let i = 0; i < form_section.line_items.length; i++) {
                var line_item = form_section.line_items[i];
                $rootScope.data[form_section.table][0][line_item.ctrl] = undefined;
                line_item.removed = false;
            }

            // remove any comments, ensuring there is 1 empty row
              $rootScope.data.CrewTrailerNewComments = []
            $scope.addCommentRow();

            // bit of a special case, this row in BOP has multiple values so has to be cleared a bit differently
            if (section_to_clear === 'Monthly / New Location') {
                var bopAcFields = ModelFactory.bopAcFields();
                for (var i = 0; i < bopAcFields.length; i++) {
                    $rootScope.data[form_section.table][0][bopAcFields[i]] = undefined;
                }
            }

            // reset the corrective actions
            $rootScope.data.FormNotification = [];
            $scope.checkAllItemsSelected();
        };

       // $scope.addEmployeeSignOffRow = function() {
           
        //  $rootScope.data.CrewTrailerNewDocumentSignoffs[$rootScope.data.CrewTrailerNewDocumentSignoffs.length] = ModelFactory.newDocumentSignoff();
         //   $scope.contentForm.$dirty = true;
       // };

        //$scope.removeEmployeeSignoffRow = function(rowIdx) {
        //  $rootScope.data.CrewTrailerNewDocumentSignoffs.splice(rowIdx, 1);  
       //     $scope.contentForm.$dirty = true;
      //  }

     


        // END: Form specific functions
        // ---------------------------------------------------------------------------------------

        // add these functions if they are missing!
        $scope.skip = function () {
            var element = angular.element('#skipModal');
            element.modal('show');
        };

        $scope.skipConfirmed = function () {
            $rootScope.submitted = true;
            $rootScope.data.CanBeSubmitted = true;
            $rootScope.data[$rootScope.form.mainTable].SkipDate = new Date();
            $rootScope.data[$rootScope.form.mainTable].SkippedBy = $rootScope.lists.USER_NAME;
            $scope.skipForm();
        };

        $scope.show_tooltip = function (elemId) {
            $('#' + elemId).tooltip('show');
        };

        $scope.dismiss_tooltip = function (elemId) {
            $('#' + elemId).tooltip('hide');
        };

        $scope.getFooterUpdatedBy = function () {
            if ($rootScope.data == undefined || $rootScope.data[$rootScope.form.mainTable] == undefined)
                return;

            var val = '';
            var updateDate = new Date($rootScope.data[$rootScope.form.mainTable].UpdateDate);
            var updatedBy = $rootScope.data[$rootScope.form.mainTable].UpdatedBy;
            var tzAbbr = $rootScope.data[$rootScope.form.mainTable].CreateDateTzAbbr;

            if (updateDate !== undefined && updatedBy !== undefined && updateDate !== '' && updatedBy !== '') {
                var formattedUpdateDate = formatDate(updateDate);
                val = updatedBy + ', ' + formattedUpdateDate + ' ' + tzAbbr;
            }
            return val;
        };

        function formatDate(date) {
            var options = { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit' };
            var formattedDate = date.toLocaleDateString('en-US', options).split('/');
            formattedDate = [formattedDate[1], formattedDate[0], formattedDate[2]].join('/').replace(',', '');
            return formattedDate;
        };

        $scope.getCreatedDateWithTz = function () {
            if ($rootScope.data === undefined)
                return;

            var createDate = new Date($rootScope.data[$rootScope.form.mainTable].CreateDate);
            var tzAbbr = $rootScope.data[$rootScope.form.mainTable].CreateDateTzAbbr;

            //  replicate this format for the date   h:mm:ss a z
            var hours = createDate.getHours();
            var minutes = createDate.getMinutes();
            var seconds = createDate.getSeconds();
            var amPm = '';

            if (hours - 12 < 0) {
                amPm = 'AM';
                hours = hours;
            }
            else {
                amPm = 'PM';
                hours = hours;
            }

            if (hours == 0)
                hours == 12;

            if (hours.toString() == '0')
                hours = '12';
            if (hours.toString().length == 1)
                hours = '0' + hours.toString();
            if (minutes.toString().length == 1)
                minutes = '0' + minutes.toString();
            if (seconds.toString().length == 1)
                seconds = '0' + seconds.toString();

            return hours + ':' + minutes + ':' + seconds + ' ' + amPm + ' ' + tzAbbr;
        };


        /* functions for comment repeater and notifications */

        // used by the comment repeater to add comment rows
        $scope.addCommentRow = function () {
            $rootScope.data.CrewTrailerNewComments.push(ModelFactory.getNewComment());
        };

        // used by the comment row repeater to delete comment rows
        $scope.deleteCommentRow = function (idx) {
            $rootScope.data.CrewTrailerNewComments.splice(idx, 1);
            if ($rootScope.data.CrewTrailerNewComments.length === 0)
                $scope.addCommentRow();
        };

        // used by the comment repeater to create notification text
        $scope.getNotificationText = function (comment) {
            return 'Crew Trailer Inspection: \r\n' + 'Additional Item / Comment: \r\n' + comment;
        };

      // used by the comment repeater to add notifications generated from comments
    $scope.addNotificationRow = function(comment, notification_status, notificationItem) {
		var notification_text = $scope.getNotificationText(comment);
        notificationItem.Reason = notification_text

        $rootScope.data.FormNotification.push(notificationItem);
        $scope.contentForm.$dirty = true;
    };

	// used by the comment repeater to delete notifications that were generated from comments
    $scope.deleteNotificationRow = function(comment) {
		if(comment === '')
			return;

		var notification_text = $scope.getNotificationText(comment);
		//var result = $filter('filter')($rootScope.data.FormNotification, (x) => x.Reason === notification_text );
		var result = $filter('filter')($rootScope.data.FormNotification, function(x) { return x.Reason === notification_text } );
		if (result !== undefined && result.length >= 1) {
			var idx = $rootScope.data.FormNotification.indexOf(result[0]);
			$rootScope.data.FormNotification.splice(idx, 1);
			$scope.contentForm.$dirty = true;
		}
	};

        $scope.getChecklistNotificationText = function (form_title, row_text, user_text, deficiency_corrected) {
            var body_title = form_title;						// the title of the form
            var body_row_text_prefix = 'Description: ';			// static prefix text
            var body_row_text = row_text;						// row text from button click
            var body_user_text_prefix = deficiency_corrected;   // prefix is 'Deficiency' or 'Corrected'
            var body_user_text = user_text;						// text from the user

            var notification_text =
                body_title + '\r\n' +
                body_row_text_prefix + body_row_text + '\r\n' +
                body_user_text_prefix + ': ' + body_user_text;

            // some rows have html formatting in the text, so strip that out before creating the notification
            notification_text = notification_text.replace(/<[^>]*>?/gm, '');
            return notification_text;
        };

	// this method is used by the checklist row repeater to add static line item text plus user text to the notification
	$scope.addNotification = function(form_title, row_text, user_text, deficiency_corrected, new_closed, notificationItem) {
		let notification_text = $scope.getChecklistNotificationText(form_title, row_text, user_text, deficiency_corrected);
		notificationItem.Reason = notification_text;
        $rootScope.data.FormNotification.push(notificationItem);
        $scope.contentForm.$dirty = true;
    };

	// this is used to delete notifications that came from the checklist row repeater
    $scope.deleteNotification = function(form_title, row_text, user_text, deficiency_corrected) {
		//let notification_text = $scope.getChecklistNotificationText(form_title, row_text, user_text, deficiency_corrected);
		var result = $filter('filter')($rootScope.data.FormNotification, function(x) { return x.Reason.indexOf(row_text) !== -1 });
		if (result !== undefined && result.length >= 1) {
			var idx = $rootScope.data.FormNotification.indexOf(result[0]);
			$rootScope.data.FormNotification.splice(idx, 1);
			$scope.contentForm.$dirty = true;
		}
	};

        $scope.handleCommentRowNotification = function(idx, button_toggled_state, notification_status) {
            // get the comment row that is being updated
            var comment = $rootScope.data.CrewTrailerNewComments[idx];
    
            // the same button was aleady selected - toggle the button - delete any notification - enable text field (handled in html)
            if (comment.DeficiencyOrCorrected === button_toggled_state) {
                comment.DeficiencyOrCorrected = '';
                $scope.deleteNotificationRow(comment.Comment);
            }
            // if nothing was selected, or the other button was selected
            else {
                comment.DeficiencyOrCorrected = button_toggled_state;
                $scope.deleteNotificationRow(comment.Comment);
                $rootScope.addNotificationItem = ModelFactory.newNotification()
                    $rootScope.addNotificationItem = $scope.updateWithDefaultValues($rootScope.addNotificationItem)
                    $rootScope.addNotificationItem.Reason = comment.Comment
                    $rootScope.addNotificationItem.Status = notification_status
    
                    $rootScope.notificationData = {
                        addNotificationItem: $rootScope.addNotificationItem,
                        lists: $rootScope.lists,
                        subTitle: 'Additional Item / Comment',
                        hideAdvancedOptions: true,
                        defaultReasonText: comment.Comment,
    
                        notification_status: notification_status,
                        commentsIndex: idx
                    }
                    $('#createNotificationModal').modal({ 'backdrop': 'static', 'keyboard': false })
            }
        };
    
        $scope.updateWithDefaultValues = function(notificationItem) {
            notificationItem.CreatedBy = $rootScope.lists.USER_NAME
            notificationItem.NotificationType = 'S0'
            notificationItem.CodeGroup =  'SW000000',
            notificationItem.Code =  '1.7',
            notificationItem.MainWorkCenter =  $rootScope.lists.WORK_CENTER,
            notificationItem.RequiredStartDate = $scope.incrementDate($rootScope.data[$rootScope.form.mainTable].CreateDate, 1)
            notificationItem.Timezone = $rootScope.lists.TIME_ZONE
            return notificationItem
        };
    
        $scope.incrementDate = function (dateInput,increment) {
            var dateFormatTotime = new Date(dateInput);
            var increasedDate = new Date(dateFormatTotime.getTime() + (increment *86400000));
            return increasedDate;
        };
    
        $scope.populateAddItem = function(section_table, checklist_row_item) {
            for (var i = 0; i < $rootScope.data.FormNotification.length; i++) {
                // found the line item text in one of the notifications, this is one!
                if ($rootScope.data.FormNotification[i].Reason.indexOf(checklist_row_item.label) !== -1) {
                    var button_state = $rootScope.data[section_table][0][checklist_row_item.ctrl];
                    var reason = $rootScope.data.FormNotification[i].Reason;
                    var splits = reason.split(':')
                    $rootScope.addItem = splits[splits.length-1].trim()
    
                    return $rootScope.addItem;
                }
            }
        };

        $scope.setAddItem = function (addItem) {
            $rootScope.addItem = addItem;
        };

        $scope.handleChecklistRowNotification = function (checklist_row_item, section_table, deficiency_or_corrected, notification_status) {
            var row_val = $rootScope.data[section_table][0][checklist_row_item.ctrl];

            if (row_val === undefined || row_val === '') {
                $scope.addNotification($rootScope.form.title, checklist_row_item.label, $rootScope.addItem, deficiency_or_corrected, notification_status);
                $rootScope.data[section_table][0][checklist_row_item.ctrl] = deficiency_or_corrected;
                $scope.checkAllItemsSelected($rootScope.data[$rootScope.form.mainTable].InspType);
            }
            else {
                $scope.deleteNotification($rootScope.form.title, checklist_row_item.label, '', row_val);
                $scope.addNotification($rootScope.form.title, checklist_row_item.label, $rootScope.addItem, deficiency_or_corrected, notification_status);
                $rootScope.data[section_table][0][checklist_row_item.ctrl] = deficiency_or_corrected;
                $scope.checkAllItemsSelected($rootScope.data[$rootScope.form.mainTable].InspType);
            }

            $rootScope.addItem = undefined;
        };

        /* functions for comment repeater and notifications */


        $scope.getRowRepeaterForPrint = function (group, inspection_type, types) {
            // figure out the line items for the selected group
            var line_items = $scope.getLineItemsForGroup(group, inspection_type, types);

            // assuming 3 columns in the table, figure out how many rows are needed to display all the data
            var table_rows = Math.ceil(line_items.length / 3);

            // create a range that is the length of the number of table rows
            var range = []
            for (var i = 0; i < table_rows; i++) {
                range[range.length] = i;
            }

            return range;
        }


        $scope.getLineItemsForRowForPrint = function (group, inspection_type, types, row_num, number_of_items) {
            // get all the line items for this group
            var line_items = $scope.getLineItemsForGroup(group, inspection_type, types);
            var return_me = [];

            // get enough data to fill all the columns you have (2 cols per item - one for the text and the other for the button state)
            for (var i = 0; i < number_of_items; i++) {
                // get the index of an individual line item for the column
                var idx = (row_num * number_of_items) + i;
                // don't exceed the max len of the array
                if (idx <= line_items.length - 1) {
                    // get the item so it can be rendered
                    return_me[return_me.length] = line_items[idx];
                }
                // so the table renders nicely, provide empty line items when the number of items per row is not a multiple of the total row items
                else {
                    return_me[return_me.length] = {
                        groupName: undefined,
                        ctrl: undefined,
                        label: undefined,
                        removed: undefined,
                        dataType: undefined,
                        allowNA: undefined
                    }
                }
            }
            return return_me;
        }

        $scope.updateCommentsAtIndex = function (index, comment) {
            $rootScope.data.CrewTrailerNewComments[index].Comment = comment
        }
    
        $scope.populateNotificationItem = function (section_table, checklist_row_item) {
            for (var i = 0; i < $rootScope.data.FormNotification.length; i++) {
                // found the line item text in one of the notifications, this is one!
                if ($rootScope.data.FormNotification[i].Reason.indexOf(checklist_row_item.label) !== -1) {
                    $rootScope.addNotificationItem = $rootScope.data.FormNotification[i]
    
                    // Avoiding overwriting of pre-formatted description by the users
                    // |$rootScope.defaultReasonText| would be bound to the description field in the UI.
                    var reason = $rootScope.data.FormNotification[i].Reason;
                    var splits = reason.split(':')
                    $rootScope.addNotificationItem.defaultReasonText = splits[splits.length - 1].trim()
                    return $rootScope.data.FormNotification[i];
                }
            }
    
            $rootScope.addNotificationItem = ModelFactory.newNotification()
            $rootScope.addNotificationItem = $scope.updateWithDefaultValues($rootScope.addNotificationItem)
            return $rootScope.addNotificationItem;
        }
    
        $scope.showCreateNotificationPopup = function (section_table, item, deficiency_or_corrected, notification_status) {
    
            var addNotificationItem = $scope.populateNotificationItem(section_table, item);
            addNotificationItem.Status = notification_status
    
            $rootScope.notificationData = {
                addNotificationItem: addNotificationItem,
                lists: $rootScope.lists,
                subTitle: item.label,
                hideAdvancedOptions: true,
                defaultReasonText: addNotificationItem.defaultReasonText,
                notification_status: notification_status,
    
                deficiency_or_corrected: deficiency_or_corrected,
                section_table: section_table,
                item: item
            }
    
            $('#createNotificationModal').modal({ 'backdrop': 'static', 'keyboard': false })
        };
    
        $scope.$on('notificationUpdated', function (event, data) {
            console.log('Notification Updated', JSON.stringify(data.addNotificationItem))
    
            // Handle Comment Row Notification
            if (data.commentsIndex > -1) {
    
                $scope.addNotificationRow(data.defaultReasonText, data.notification_status, $rootScope.addNotificationItem)
                $scope.updateCommentsAtIndex(data.commentsIndex, data.defaultReasonText)
                return;
            }
    
            // Handle Checklist Row Notification
            var row_val = $rootScope.data[data.section_table][0][data.item.ctrl];
    
            if (row_val === undefined || row_val === '') {
                $scope.addNotification($rootScope.form.title, data.item.label, data.defaultReasonText, data.deficiency_or_corrected, data.notification_status, $rootScope.addNotificationItem);
                $rootScope.data[data.section_table][0][data.item.ctrl] = data.deficiency_or_corrected;
                $scope.checkAllItemsSelected($rootScope.data[$rootScope.form.mainTable].InspType);
            }
            else {
                $scope.deleteNotification($rootScope.form.title, data.item.label, '', row_val);
                $scope.addNotification($rootScope.form.title, data.item.label, data.defaultReasonText, data.deficiency_or_corrected, data.notification_status, $rootScope.addNotificationItem);
                $rootScope.data[data.section_table][0][data.item.ctrl] = data.deficiency_or_corrected;
                $scope.checkAllItemsSelected($rootScope.data[$rootScope.form.mainTable].InspType);
            }
        })
    });


