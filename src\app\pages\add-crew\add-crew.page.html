<ion-header class="add-crew-header">
  <ion-toolbar color="primary" class="add-crew-toolbar">
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="closeModal()">
        <ion-icon name="arrow-back" style="color: white;"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title class="add-crew-title">{{ 'Edit Temporary Crew Members' | translate }}</ion-title>
    <ion-buttons slot="end">
      <ion-button
        id="addCrewButton"
        fill="clear"
        (click)="addCrewMember()"
        class="add-button">
        <ion-icon name="add-circle" style="font-size: 22px;"></ion-icon>
      </ion-button>
      <ion-button
        id="saveButton"
        fill="clear"
        (click)="saveCrewMember()"
        class="save-button">
        {{ 'Save' | translate }}
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
  <!-- Removed table header completely -->
</ion-header>

<ion-content class="add-crew-content" [ngClass]="{ contrast: theme === 'contrast' }">
  <!-- Crew Members List -->
  <div class="crew-cards-container" *ngIf="crewMembers().length > 0">
    <ion-card
      *ngFor="let crewMember of crewMembers(); let i = index"
      class="crew-member-card"
      [ngClass]="{
        'has-unsaved-name': crewMember.userNameUnsaved,
        'has-unsaved-designation': crewMember.designationUnsaved,
        'crew-distinct': crewMember.crewIdDistinct
      }">

      <ion-card-content class="crew-card-content">
        <!-- Header with Add Crew label and close button -->
        <div class="crew-header">
          <div class="add-crew-label" [ngClass]="{ 'is-saved': crewMember.saved }">
            Add Crew
          </div>
          <ion-button fill="clear" size="small" class="remove-x-btn" (click)="removeNewEntry(crewMember)">
            <ion-icon name="close-outline"></ion-icon>
          </ion-button>
        </div>

        <!-- Input fields without grid system for cleaner layout -->
        <div class="crew-inputs">
          <!-- Name Input -->
          <ion-input
            type="text"
            maxlength="50"
            placeholder="{{ 'Enter Username' | translate }}"
            [(ngModel)]="crewMember.USER_NAME"
            (ionInput)="onChangeInput($event, 'userName', crewMember)"
            class="crew-input"
            [ngClass]="{'has-unsaved': crewMember.userNameUnsaved}"
            fill="outline">
          </ion-input>

          <!-- Position Input -->
          <ion-input
            type="text"
            maxlength="50"
            placeholder="{{ 'Enter Position / Designation' | translate }}"
            [(ngModel)]="crewMember.DESIGNATION"
            (ionInput)="onChangeInput($event, 'designation', crewMember)"
            class="crew-input"
            [ngClass]="{'has-unsaved': crewMember.designationUnsaved, 'is-saved': crewMember.saved}"
            fill="outline">
          </ion-input>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- No Crew Members Message -->
  <div class="no-crew-message" *ngIf="crewMembers().length === 0">
    <p>{{ 'No Crew Members Available.' | translate }}</p>
  </div>
</ion-content> 