angular.module('PDTest')
.directive('pdFormTitle', function() {
    var html='            <div id="topSection">';
	html +='                <div class="alert alert-danger text-center" ng-if="data[form.mainTable].SkipDate">';
    html +='                    <h4><strong>This form was skipped by {{data[form.mainTable].SkippedBy}} on {{data[form.mainTable].SkipDate|date:\'MMM dd yyyy H:mm\': \'data[form.mainTable].CreateDateTzOffest\'}} {{ data[form.mainTable].CreateDateTzAbbr }} (24hrs) with the following comment:</strong></h4>';
    html +='                    <p>"{{data[form.mainTable].SkipComment}}"</p>';
    html +='                </div>';
	html +='                <div class="navbar-brand">';
    html +='                    <img ng-src="{{lists.LOGO}}"/>';
    html +='                </div>';
    html +='                <div class="form-container-title">';
    html +='                    <span>&nbsp;&nbsp;|&nbsp;&nbsp;{{form.title}}</span>';
    html +='                    <div class="navbar-right"></div>';
    html +='                </div>';
    html +='                <div class="well" ng-if="form.usage">';
    html +='                    <strong>Description of use: </strong><span ng-bind-html="form.usage | trustAsHtml"></span>';
    html +='                </div>';
    html +='            </div>';

    return {
        restrict: 'EA',
        template: html
    };
});