import { Component, NgZone, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonIcon, IonRouterOutlet, MenuController, ModalController, NavController, Platform } from '@ionic/angular/standalone';
import { AlertController } from '@ionic/angular/standalone';
import { IonContent, IonHeader, IonTitle, IonToolbar, IonMenuButton, IonButtons, IonButton , PopoverController, IonSearchbar, IonLabel, IonList, IonListHeader, IonItemDivider, IonThumbnail, IonSpinner, IonProgressBar  } from '@ionic/angular/standalone';
import { IonItem,  } from '@ionic/angular/standalone';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import * as RigActions from 'src/app/store/store.actions';
import { Store } from '@ngrx/store';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { selectAllForms, selectAllReleaseNotes, selectAllTemplates, selectPrefilledData, selectProgressPercentage, selectRigData, selectTemplatesLoadedFromDb } from 'src/app/store/store.selector';
import { BehaviorSubject, combineLatest, distinctUntilChanged, filter, firstValueFrom, map, Observable, startWith, Subscription, switchMap, take, tap } from 'rxjs';
import { RIG_HEADER } from 'src/models/RIG_HEADER';
import { Router, ActivatedRoute } from '@angular/router';
import { DataService } from 'src/app/services/data.service';
import { TEMPLATE_HEADER } from 'src/models/TEMPLATE_HEADER';
import { IonSkeletonText } from '@ionic/angular/standalone';
import { UtilityService } from 'src/app/services/utility.service';
import { AlertService } from 'src/app/services/alert.service';
import { EventsService } from 'src/app/services/events.service';
import { STMRDetailsPage } from '../stmr-details/stmr-details.page';
import { AppConstants } from 'src/app/constants/appConstants';
import { STMR_HEADER } from 'src/models/STMR_HEADER';
import moment from 'moment';
import { CustomDatePipe } from 'src/app/pipes/custom-date.pipe';
import { ReleaseNotesService } from 'src/app/services/release-notes.service';
import { addIcons } from 'ionicons';
import { chatboxOutline } from 'ionicons/icons';
import { STMR_TOPIC } from 'src/models/STMR_TOPIC';
import { STMR_TOPIC_ENTITY } from 'src/models/STMR_TOPIC_ENTITY';


export enum ModeOfOperation {
  CreateForm = 'CreateForm',
  AddFormToTopic = 'AddFormToTopic'
}
addIcons({ 'chatbox-outline': chatboxOutline,
 });
@Component({
  selector: 'app-templates',
  templateUrl: './templates.page.html',
  styleUrls: ['./templates.page.scss'],
  standalone: true,
  imports: [CustomDatePipe, IonSkeletonText, IonProgressBar, IonButtons, IonLabel  ,IonItem , IonThumbnail, IonItemDivider, IonList, IonButton, IonMenuButton,  IonContent, IonHeader, IonTitle, IonToolbar ,CommonModule, FormsModule, TranslateModule , IonSearchbar,IonIcon]

})
export class TemplatesPage implements OnInit {

  mode: ModeOfOperation = ModeOfOperation.CreateForm;
    timestampOfLastTap: number = 0;
    filter = { searchText: '' };
    templates: any[] = []; 
    newTemplates: any[] = [];
    rigData$!: Observable<RIG_HEADER| null>;
    templates$!: Observable<TEMPLATE_HEADER[]| null>;
    progress$!: Observable<number>;
    prefilledData$!: Observable<any>
    skeletonArray = Array(10); // default to 5 placeholders
    newCategories: { CAT_ID: string, CATEGORY_DESC: string }[] = [];   
    isLoading = true; 
    newReleaseNoteAvailable: number = 0;
      private releaseNotesSub?: Subscription;
    rigDataValue: RIG_HEADER | null = null;
    private searchTermSubject = new BehaviorSubject<string>('');
     private inboxSub?: Subscription;
    newInboxItemAvailable: number = 0;
    stmrHeader: STMR_HEADER = new STMR_HEADER();
  
    topicEntityData: STMR_TOPIC_ENTITY = new STMR_TOPIC_ENTITY();
  

  constructor(     
    private routerOutlet: IonRouterOutlet,
    private menuCtrl: MenuController , 
    private translate: TranslateService , 
    public alertController: AlertController,
    private ngZone: NgZone,
    private modalController: ModalController,
    public events: EventsService, 
    private store: Store, 
    private unviredSDK: UnviredCordovaSDK , 
    private dataService: DataService, 
    private utilityService: UtilityService,
    private navCtrl: NavController,
    private alertService: AlertService,
    private popoverController: PopoverController, 
    private router: Router,
    private releaseNotesService: ReleaseNotesService,
    public platform: Platform,
    private activatedRoute: ActivatedRoute,

  ) {
    this.rigData$ = this.store.select(selectRigData);
    this.rigData$.subscribe(rigData => {
      this.rigDataValue = rigData;
    });
   this.routerOutlet.swipeGesture = false;
      this.menuCtrl.swipeGesture(true)
  }

  ngOnInit() {
    // Dispatch action to load templates
   
    // Track the loading state of the templates
    
  
    // Subscribe to the templates data
    // this.templates$ = this.store.select(selectAllTemplates).pipe(
    //   tap(templates => {
    //     console.log('Loaded templates:', templates);
    //   })
    // );
          this.store.select(selectAllReleaseNotes).subscribe(releaseNotes => {
            if (releaseNotes) {
              this.newReleaseNoteAvailable = releaseNotes.filter(note => 
                !note.IS_READ || note.IS_READ.toLowerCase().trim() !== 'true'
              ).length;
            }
    });
     this.inboxSub = this.releaseNotesService.inboxCount$.subscribe(count => {
      this.newInboxItemAvailable = count;
    });
    this.templates$ = this.store.select(selectTemplatesLoadedFromDb).pipe(
      filter(loaded => loaded), // wait until true
      switchMap(() =>
        this.store.select(selectAllTemplates).pipe(
          tap(templates => console.log('Loaded templates:', templates))
        )
      )
    );


            if (this.mode === ModeOfOperation.CreateForm) {
        this.utilityService.currentScreenTitle = this.translate.instant('Templates');
        console.log('Current Screen: Templates');
      }

     this.activatedRoute.queryParams.subscribe(params => {
  const headerRaw = params['stmrHeader'];
  const topicEntityRaw = params['topicEntity'];

  this.stmrHeader = headerRaw ? JSON.parse(headerRaw) : null;
  this.topicEntityData = topicEntityRaw ? JSON.parse(topicEntityRaw) : null;

  console.log("Parsed stmrHeader:", this.stmrHeader);
  console.log("Parsed stmrTopicEntity:", this.topicEntityData);
});


this.activatedRoute.queryParams.subscribe(params => {
  if (params['modeOfOperation']) {
    this.mode = params['modeOfOperation'] as ModeOfOperation;
  } else {
    // fallback if no query param is passed
    this.mode = ModeOfOperation.CreateForm;
  }
});


  


  }

  ionViewWillEnter() {
    // this.dataService.getCompanyHeaderData();
    this.store.dispatch(RigActions.loadAllTemplatesFromDb());
  
   this.prefilledData$ = this.store.select(selectPrefilledData);
   this.progress$ = this.store.select(selectProgressPercentage);

  // Subscribe separately for side effects
  this.progress$
    .pipe(
      tap(progress => console.log('Progress emitted:', progress)),
      filter(progress => Math.round(progress) >= 100),
      tap(() => console.log('100 percent completed')),
      take(1)
    )
    .subscribe(() => {
      console.log('Dispatching loadAllTemplatesFromDb');
      this.store.dispatch(RigActions.loadAllTemplatesFromDb());
    });


  this.store.select(selectTemplatesLoadedFromDb).subscribe(loaded => {
      console.log('Templates loaded from DB:', loaded);
      this.isLoading = !loaded;  // Set loading state based on the `loadedFromDb` flag
    });
  }

  
  groupedTemplates$ = combineLatest([
    this.store.select(selectAllTemplates).pipe(
      tap(templates => console.log('📥 Raw templates from store:', templates))
    ),
    this.searchTermSubject.asObservable().pipe(
      startWith(''),
      tap(term => console.log('🔎 Current search term:', term))
    )
  ]).pipe(
    map(([templates, searchTerm]) => {
      if (!templates || templates.length === 0) {
        console.warn('⚠️ Templates missing → dispatching loadAllTemplatesFromDb');
        this.store.dispatch(RigActions.loadAllTemplatesFromDb());
        return [];
      }
  
      console.log('📊 Template count before filtering:', templates.length);
  
      const term = searchTerm.toLowerCase();
      const filtered = templates.filter((template: TEMPLATE_HEADER) => {
        const match =
          template.NAME?.toLowerCase().includes(term) ||
          template.CATEGORY_DESC?.toLowerCase().includes(term);
  
        if (!match && term) {
          console.log(
            '   🚫 Filtered out:',
            template.NAME,
            'in category:',
            template.CATEGORY_DESC
          );
        }
        return match;
      });
  
      console.log('✅ Filtered templates count:', filtered.length);
  
      const grouped = filtered.reduce((acc, t) => {
        const category = t.CATEGORY_DESC || 'Uncategorized';
        if (!acc[category]) acc[category] = [];
        acc[category].push(t);
        return acc;
      }, {} as { [key: string]: any[] });
  
      console.log('📦 Grouped result:', grouped);
  
      return Object.entries(grouped).map(([category, items]) => ({
        category,
        items
      }));
    }),
    tap(result => console.log('🎨 Final groupedTemplates structure:', result))
  );
  

  

  returnDisplayDate(timestamp: number, format: string = '', fallback: string = '-'): string {
    if (!timestamp) return fallback;  
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: '2-digit',
      year: 'numeric'
    }); // You can make this dynamic with format if needed
  }
  

  onSearchChange(term: string | null | undefined) {
    this.searchTermSubject.next(term?.toLowerCase() ?? '');
  }


 

    retryDownload(event: any , template: TEMPLATE_HEADER){

  }

  hasError(id: number){}

  async createForm(event: any, template: TEMPLATE_HEADER) {
    console.log('create form is called' , this.mode);
    switch (this.mode) {
      case  ModeOfOperation.CreateForm: {
                if (template.IS_DATA_ENH == 'true') {

          if (navigator.onLine) {
            let schedulerInput = await this.dataService.createSchedulerInput(this.rigDataValue, template);
            await this.dataService.makePACallToCreateOnlineForm(schedulerInput);
          } else {
            this.alertService.showAlert(this.translate.instant('No Internet'), this.translate.instant('Please check your internet connection and try again.'));
            return;
          }
        } else {
          this.store.dispatch(RigActions.createForm({ template }));
          this.router.navigate(['/forms']);
                  }
        break;
      }
      case ModeOfOperation.AddFormToTopic: {
        console.log("Add form to topic is called", template);
       let stmrform = await this.dataService.createStmrFormHeader(template, this.topicEntityData)
        // let stmrForms = await this.dataService.getSTMRFormFromDb(this.stmrHeader);
        // console.log('Updated forms:', stmrForms);

        // ✅ navigate back, pass updated info in navigation state
        console.log('stmrform is ' , stmrform)
        this.router.navigate(['/topics'], {
          state: {
            stmrHeader: this.stmrHeader,
            topicEntity: this.topicEntityData,
            stmrForms: stmrform
          }
        });
        break;
      }
    }
  }


  


  
  round(value: number): number {
    return Math.round(value);
  }


  mapCategories(data: any[]) {
    // your logic to map categories
  }

  getDataFromDB() {
    // fetch fresh data from DB
  }
  
 async createSTMR() {
  const timestamp = new Date().getTime();
  if (timestamp - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
    this.unviredSDK.logInfo('TemplatesPage', 'createSTMR()', 'Ignoring double tap');
    return;
  }

  this.timestampOfLastTap = timestamp;

  // Check if initial customization data is downloaded
  const isDownloaded = await this.utilityService.checkIfAllInitDataDownloaded();
  
  // Check if prefill data is available
  let autoFillData;
  try {
    autoFillData = await firstValueFrom(this.prefilledData$);
  } catch (error) {
    console.error('TemplatesPage - Error getting prefill data:', error);
    autoFillData = null;
  }

  // Show alert if customization data is not downloaded OR if no prefill data is available
  if (!isDownloaded || !autoFillData || !autoFillData.USER_ID) {
    let alertMessage = '';
    
    if (!isDownloaded) {
      alertMessage = 'Customization data is being downloaded. You can create a new STMR once this data is downloaded.';
    } else if (!autoFillData || !autoFillData.USER_ID) {
      alertMessage = 'Required prefill data is not available. You can create a new STMR once this data is downloaded .';
    }

    this.alertService.showAlert(
      this.translate.instant('Please wait'),
      this.translate.instant(alertMessage)
    );
    return;
  }

  const alert = await this.alertController.create({
    header: this.translate.instant('Create STMR'),
    message: AppConstants.CREATE_STMR_CONFIRM_MSG,
    buttons: [
      {
        text: this.translate.instant('Cancel'),
        role: 'cancel',
      },
      {
        text: this.translate.instant('Create STMR'),
        handler: async () => {
          try {
            const completeStmrHeader = await this.createNewSTMRObject();
            
            console.log('TemplatesPage - Passing complete STMR to details page:', completeStmrHeader.STMR_ID);
            console.log('autofillData for crew:', autoFillData.CREW);
             // Navigate to STMR details page
            this.router.navigate(['/stmr-details'], {
              state: {
                stmrHeader: completeStmrHeader,
                stmrCrew: autoFillData.CREW ? [...autoFillData.CREW] : [],
                stmrTopicEntity: autoFillData.TOPICS ? [...autoFillData.TOPICS] : [],
                isNewSTMR: true,
              }
            });
            
          } 
          catch (error: any) {
            console.error('TemplatesPage - Error creating STMR:', JSON.stringify(error));
          }
        },
      },
    ],
  });

  await alert.present();

  const isLogoSet = await this.utilityService.checkCompanyLogoStatus();
  if (!isLogoSet) {
    document.dispatchEvent(new CustomEvent(AppConstants.EVENT_COM_LOGO_LOAD));
  }
}



async createNewSTMRObject(): Promise<STMR_HEADER> {
  const stmrHeader = new STMR_HEADER();
  
  let autoFillData;
  try {
    autoFillData = await firstValueFrom(this.prefilledData$);
  } catch (error) {
    console.error('TemplatesPage - Error getting prefill data in createNewSTMRObject:', error);
    throw new Error('Prefill data is not available');
  }

  if (!autoFillData) {
    throw new Error('Prefill data is null or undefined');
  }

  if (!autoFillData.USER_ID) {
    throw new Error('User ID is missing from prefill data');
  }

  const count = await this.dataService.getSTMRsCount();
  const matchAutoFillKeys = ['RIG_NO', 'COMPANY', 'RIG_TYPE', 'RIG_SUB_TYPE'];

  for (let key of matchAutoFillKeys) {
    if (key in stmrHeader && autoFillData[key]) {
      (stmrHeader as any)[key] = autoFillData[key];
    }
  }

  stmrHeader.COMPANY = autoFillData.COMP_CODE || autoFillData.COMPANY || '';
  stmrHeader.STMR_ID = 'New' + this.utilityService.genStmrId(count, autoFillData.RIG_NO);
  stmrHeader.LID = this.utilityService.guid32();

  // Timing
  const currentUnixTime = moment().unix();
  stmrHeader.SHIFT_TIME = currentUnixTime;
  stmrHeader.CRTD_ON = currentUnixTime;
  stmrHeader.CRTD_BY = autoFillData.USER_ID || '';

  // User/operator data 
  stmrHeader.SUBM_BY = autoFillData.USER_ID || '';
  
  if (Array.isArray(autoFillData.OPERATOR) && autoFillData.OPERATOR.length > 1) {
    stmrHeader.OPERATOR = autoFillData.OPERATOR[1]?.NAME || '';
    stmrHeader.WELL_LOC = autoFillData.OPERATOR[1]?.WELL_NAME || '';
  } else if (Array.isArray(autoFillData.OPERATOR) && autoFillData.OPERATOR.length > 0) {
    stmrHeader.OPERATOR = autoFillData.OPERATOR[0]?.NAME || '';
    stmrHeader.WELL_LOC = autoFillData.OPERATOR[0]?.WELL_NAME || '';
  } else {
    stmrHeader.OPERATOR = '';
    stmrHeader.WELL_LOC = '';
  }

  stmrHeader.CHAIRED_BY = autoFillData.CHAIRED_BY || '';
  stmrHeader.ONSITE_SUP = autoFillData.ONSITE_SUP || '';
  stmrHeader.ONSITE_SUP_SIGN = '';

  // Status fields
  stmrHeader.SHIFT = '';
  stmrHeader.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
  stmrHeader.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
  stmrHeader.STMR_STATUS = AppConstants.VAL_FORM_STATUS.INPR;

  // Sync fields
  stmrHeader.LAST_SYNC_USER = autoFillData.USER_ID || '';
  stmrHeader.TIME_ZONE = this.utilityService.getTimezone();

  console.log('TemplatesPage - Complete STMR created with ID:', stmrHeader.STMR_ID);
  console.log('TemplatesPage - Using RIG_TYPE:', autoFillData.RIG_TYPE);
  console.log('TemplatesPage - Using RIG_SUB_TYPE:', autoFillData.RIG_SUB_TYPE);
  
  return stmrHeader;
}
  navigateToReleaseNotes() {
  this.router.navigate(['/release-notes']);
}
  navigateToDataEnhancedForm() {
    this.router.navigate(['/inbox']);
  }

}





