import { Injectable } from '@angular/core';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AppConstants } from 'src/app/constants/appConstants';
import { BehaviorSubject } from 'rxjs';
// import { setReleaseNotesTotal } from '../store/store.actions';
// import { AppState } from '../store/app.state';
// import { Store } from '@ngrx/store';

@Injectable({
  providedIn: 'root',
})

export class ReleaseNotesService {

  // Observables for counts
  private totalCountSubject = new BehaviorSubject<number>(0);
  private unreadCountSubject = new BehaviorSubject<number>(0);
  private inboxCountSubject = new BehaviorSubject<number>(0);

  totalCount$ = this.totalCountSubject.asObservable();
  unreadCount$ = this.unreadCountSubject.asObservable();
  inboxCount$ = this.inboxCountSubject.asObservable();

  constructor(private unviredSdk: UnviredCordovaSDK,
    // private store: Store<AppState>
  ) {}

  /**
   * Updates the counts from DB
   */
async updateCount(): Promise<void> {
  console.log('[ReleaseNotesService] updateCount called');
  try {
    // 1. Get rig info
    const rigInfo: any = await this.unviredSdk.dbSelect(AppConstants.TABLE_RIG_HEADER, {});
    console.log('[ReleaseNotesService] Rig info:', rigInfo);
    const rig = rigInfo?.data?.[0];
    if (!rig) {
      console.log('[ReleaseNotesService] No rig data found');
      this.totalCountSubject.next(0);
      this.unreadCountSubject.next(0);
      return;
    }
    console.log('[ReleaseNotesService] Using rig:', rig.RIG_NO);

    // 2. Subquery for template-specific release notes
    const subquery = `
      SELECT TH.TMPLT_ID FROM TMPLT_HEADER AS TH, TMPLT_VER AS TV, TMPLT_ATTACHMENT AS TA, TMPLT_ASSGN AS TA WHERE TV.STATUS = 'REL' AND TH.IS_ACTIVE = 'true' AND TH.TMPLT_ID = TV.TMPLT_ID AND TA.TAG1 = TV.VER_ID AND TA.TMPLT_ID = TH.TMPLT_ID AND TA.TMPLT_ID IN (SELECT TMPLT_ID FROM TMPLT_ASSGN WHERE ASSGN_TYPE = '${rig.RIG_TYPE}' and VAL1 = '${rig.RIG_SUB_TYPE}') AND TA.TMPLT_ID NOT IN (SELECT TMPLT_ID FROM TMPLT_ASSGN WHERE ASSGN_TYPE = 'COMPANY' AND VAL1 != ${rig.COMP_CODE} EXCEPT SELECT TMPLT_ID FROM TMPLT_ASSGN WHERE ASSGN_TYPE = 'COMPANY' AND VAL1 = ${rig.COMP_CODE}) GROUP BY TH.TMPLT_ID`
    // 3. Main query: total and unread counts
    const query = `
      SELECT 
        COUNT(*) AS TOTAL,
        SUM(CASE WHEN lower(trim(ifnull(IS_READ,''))) != 'true' THEN 1 ELSE 0 END) AS UNREAD
      FROM RELEASE_NOTE_HEADER
      WHERE IS_ACTIVE != 'false'
        AND DESCRIPTION IS NOT NULL
        AND DESCRIPTION != ''
        AND (
          CASE
            WHEN NOTE_TYPE = 'TMPLT'
              THEN TMPLT_ID IN (${subquery})
            ELSE 1
          END
        )
    `;

    // 4. Execute query
    const result: any = await this.unviredSdk.dbExecuteStatement(query);
    if (result?.type === ResultType.success && result.data?.length > 0) {
      const counts = result.data[0];
      this.totalCountSubject.next(counts.TOTAL || 0);
      this.unreadCountSubject.next(counts.UNREAD || 0);
      console.log('[ReleaseNotesService] Counts updated:', counts);
    } else {
      this.totalCountSubject.next(0);
      this.unreadCountSubject.next(0);
      console.warn('[ReleaseNotesService] Failed to get counts:', result);
    }
  } catch (error) {
    console.error('[ReleaseNotesService] Error updating counts:', error);
    this.totalCountSubject.next(0);
    this.unreadCountSubject.next(0);
  }
}

 async loadInboxCount(): Promise<number> {
    try {
      const rigInfo: any = await this.unviredSdk.dbSelect(AppConstants.TABLE_RIG_HEADER, {});
      const rig = rigInfo?.data?.[0];
      if (!rig) {
        this.inboxCountSubject.next(0);
        return 0;
      }

      const subquery = `
  SELECT TH.TMPLT_ID
  FROM TMPLT_HEADER AS TH, TMPLT_VER AS TV, TMPLT_ATTACHMENT AS TA, TMPLT_ASSGN AS TA2
  WHERE TV.STATUS = 'REL'
    AND TH.IS_ACTIVE = 'true'
    AND TH.TMPLT_ID = TV.TMPLT_ID
    AND TA.TAG1 = TV.VER_ID
    AND TA.TAG2 = TH.TMPLT_ID
    AND TA2.TMPLT_ID = TH.TMPLT_ID
    AND TA2.ASSGN_TYPE = '${rig.RIG_TYPE}'
    AND TA2.VAL1 = '${rig.RIG_SUB_TYPE}'
    AND TA2.TMPLT_ID NOT IN (
      SELECT TMPLT_ID 
      FROM TMPLT_ASSGN 
      WHERE ASSGN_TYPE = 'COMPANY' AND VAL1 != ${rig.COMP_CODE}
      EXCEPT
      SELECT TMPLT_ID 
      FROM TMPLT_ASSGN 
      WHERE ASSGN_TYPE = 'COMPANY' AND VAL1 = ${rig.COMP_CODE}
    )
  GROUP BY TH.TMPLT_ID
`;

      const query = `
        SELECT count(*) AS INBOX_COUNT
        FROM FORM_SCHD_ALERT_HEADER
        WHERE RIG_NO = '${rig.RIG_NO}'
          AND TMPLT_ID IN (${subquery})
          AND IS_READ IS NOT 'true'
      `;

      const res: any = await this.unviredSdk.dbExecuteStatement(query);
      console.log('Inbox count query result:', res);
      const count = res.type === ResultType.success ? res.data?.[0]?.INBOX_COUNT || 0 : 0;
      this.inboxCountSubject.next(count);
      return count;

    } catch (e) {
        // this.store.dispatch(setReleaseNotesTotal({ total: 0 }));
      console.error('Error loading inbox count:', e);
      this.inboxCountSubject.next(0);
      return 0;
    }
  }
}

