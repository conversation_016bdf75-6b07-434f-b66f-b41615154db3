# PD-Forms-App-Latest-Ionic
# Sample Check
# 1. Clone the repository
git clone <repository-url>
cd <project-folder>

# 2. Create a new branch (replace 'my-branch' with your branch name)
git checkout -b my-branch

# 3. Push the new branch to remote
git push origin my-branch

# 4. Install dependencies
npm install

# 5. Add Electron platform 

ionic cordova platform add electron

# 6. Run the project in Electron

ionic cordova run electron


## OR for development with live reload:

ionic cordova run electron --livereload
-

# Handling DeepLinks

This app uses as a Deeplink (forms://callback or pdforms://callback) for enabling authentication via external browser window. In Electron app, this is achieved by making specific changes to cdv-electron-main.js file. This file has specific logic related to registering this custom URL scheme in the registry editor and handling callbacks from the browser window. In Electron, when the browser tends to open up the app using deeplinks, it always opens up a new instance of Electron app which needs to be terminated and instead the control would then be passed to the currently running Electron app. 

In iOS, deeplinking is achieved using a plugin named `ionic-plugin-deeplinks` in which we need to specify URL scheme and host. To change the deeplink, the following changes are required:

# Steps to update deeplink:

To update the deeplink scheme (e.g., from `pdforms://callback` to `pdhse://callback`), follow these steps for both Electron and iOS platforms:

### Electron

1. **Update the custom URL scheme registration:**
    - Open `cdv-electron-main.js`.
    - Locate the section where the custom protocol is registered (look for `app.setAsDefaultProtocolClient` or similar).
    - Change all instances of the old scheme (`pdforms`) to the new scheme (`pdhse`).

2. **Handle incoming deeplink events:**
    - Ensure that any logic parsing or handling the deeplink URL in `cdv-electron-main.js` is updated to recognize the new scheme.

3. **Windows registry (if applicable):**
    - If you manually register the protocol in the Windows registry, update the scheme name there as well.

4. **Test the behavior:**
    - After making changes, test opening the app via the new deeplink (`pdhse://callback`) and verify that it is handled correctly by the running Electron instance.

### iOS

1. **Update the plugin configuration:**
    - Open `config.xml` or the relevant configuration file for `ionic-plugin-deeplinks`.
    - Find the `<plugin name="ionic-plugin-deeplinks">` section.
    - Change the `scheme` value from `pdforms` to `pdhse`.

2. **Update the host if needed:**
    - If your deeplink host changes, update the `host` value accordingly.

3. **Update any references in code:**
    - Search your codebase for any hardcoded references to `pdforms://` and update them to `pdhse://`.

4. **Rebuild the app:**
    - Run a clean build for iOS to ensure the new scheme is registered.

5. **Test the deeplink:**
    - Use a device or simulator to verify that `pdhse://callback` opens the app and triggers the expected behavior.

**Note:** Always update documentation and inform your team about the new deeplink scheme to avoid confusion.

