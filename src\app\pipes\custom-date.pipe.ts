import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'customDate',
  standalone: true
})
export class CustomDatePipe implements PipeTransform {
  transform(value: number | string): string {
    if (!value) return '';
    // Convert to milliseconds if value is in seconds
    const timestamp = typeof value === 'string' ? parseInt(value, 10) : value;
    const date = new Date(timestamp < 1e12 ? timestamp * 1000 : timestamp);
    // Format: Jul 22 2025 16.53
    const month = date.toLocaleString('en-US', { month: 'short' });
    const day = date.getDate();
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${month} ${day} ${year} ${hours}:${minutes}`;
  }
}
