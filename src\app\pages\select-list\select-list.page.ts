import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild, ViewChildren, QueryList, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  IonButton,
  IonButtons,
  IonCheckbox,
  IonContent,
  IonHeader,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonRadio,
  IonRadioGroup,
  IonSearchbar,
  IonTitle,
  IonToolbar,
  ModalController,
  IonGrid
} from '@ionic/angular/standalone';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

export interface SelectListItem {
  DisplayString: string;
  object?: any;
  isSelected?: boolean;
  [key: string]: any; // to allow data_id, dynamic selection key, etc.
}

export interface SelectListDetails {
  title?: string;
  searchPlaceHolder?: string;
  selectKey?: string;
  multiSelect?: boolean;
  eventName?: string;
  hideCloseBtn?: boolean;
  searchValue?: string;
}

@Component({
  selector: 'app-select-list',
  standalone: true,
  imports: [
    TranslateModule,
    CommonModule, FormsModule, IonHeader, IonToolbar, IonTitle, IonButtons, IonButton, IonIcon, IonContent, IonSearchbar, IonList, IonItem, IonLabel, IonRadioGroup, IonRadio, IonCheckbox, IonGrid
  ],
  templateUrl: './select-list.page.html',
  styleUrls: ['./select-list.page.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SelectListPage implements OnInit {
  @Input() listData: SelectListItem[] = [];
  @Input() listDetails: SelectListDetails = {};
  @Input() theme: 'normal' | string = 'normal';
  @Output() dismissed = new EventEmitter();

  @ViewChild('searchBar', { static: false }) searchBar?: IonSearchbar;
  @ViewChildren(IonItem, { read: ElementRef }) itemEls!: QueryList<ElementRef>;

  displayListData: SelectListItem[] = [];
  hasError = true;
  searchText: string = ''; // Separate search text variable

  get selectKey(): string {
    return this.listDetails.selectKey || 'isSelected';
  }

  constructor(private modalCtrl: ModalController, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.listDetails = {
      title: this.listDetails?.title ?? 'Select List',
      searchPlaceHolder: this.listDetails?.searchPlaceHolder ?? 'Search List',
      selectKey: this.listDetails?.selectKey || 'isSelected',
      multiSelect: this.listDetails?.multiSelect ?? false,
      hideCloseBtn: this.listDetails?.hideCloseBtn ?? false,
      searchValue: this.listDetails?.searchValue ?? '',
      eventName: this.listDetails?.eventName,
    };

    // Initialize search text with the provided search value
    this.searchText = this.listDetails.searchValue || '';

    this.listData = (this.listData || []).map((item, idx) => {
      const selected = item[this.selectKey] === true;
      return { ...item, data_id: `data-${idx + 1}`, [this.selectKey]: !!selected };
    });

    this.displayListData = [...this.listData];
    this.hasError = this.noDataSelected();
  }

  // Filter by key
  filterList(key: string, searchText: string): void {
    if (!key) return;
    this.searchText = searchText || '';
    const term = this.searchText.trim().toLowerCase();
    if (!term) {
      this.displayListData = [...this.listData];
      return;
    }

    this.displayListData = this.listData.filter(
      (item) => (item[key] || '').toString().toLowerCase().includes(term)
    );
  }

  onItemClick(item: SelectListItem): void {
    this.toggleSelectData(item);
  }

  // Handle checkbox change
  onCheckboxChange(item: SelectListItem, event: any): void {
  event.stopPropagation();
  const checked = !!(event?.detail?.checked);

  if (this.listDetails.multiSelect) {
    item[this.selectKey] = checked;
    const originalIndex = this.listData.findIndex(original => original['data_id'] === item['data_id']);
    if (originalIndex !== -1) {
      this.listData[originalIndex][this.selectKey] = item[this.selectKey];
    }
    this.hasError = this.noDataSelected();
    this.cdr.detectChanges();
    return;
  }

  // single-select: checking selects+closes, unchecking clears+closes
  this.listData.forEach(d => (d[this.selectKey] = false));
  item[this.selectKey] = checked;
  const originalIndex = this.listData.findIndex(original => original['data_id'] === item['data_id']);
  if (originalIndex !== -1) {
    this.listData[originalIndex][this.selectKey] = item[this.selectKey];
  }
  this.hasError = this.noDataSelected();
  this.cdr.detectChanges();

  if (checked) {
    this.publishAndClose(item);
  } else {
    this.publishAndClose({ cleared: true });
  }
}




toggleSelectData(item: SelectListItem): void {
  if (this.listDetails.multiSelect) {
    // Multi-select: toggle only
    item[this.selectKey] = !item[this.selectKey];

    const originalIndex = this.listData.findIndex(original => original['data_id'] === item['data_id']);
    if (originalIndex !== -1) {
      this.listData[originalIndex][this.selectKey] = item[this.selectKey];
    }

    this.hasError = this.noDataSelected();
    this.cdr.detectChanges();
    return;
  }

  // Single-select:
  const currentlySelected = !!item[this.selectKey];

  if (currentlySelected) {
    // User clicked the already-selected item -> clear selection and close
    this.listData.forEach(d => (d[this.selectKey] = false));
    item[this.selectKey] = false;

    this.hasError = this.noDataSelected();
    this.cdr.detectChanges();

    // Publish a clear signal to the parent
    this.publishAndClose({ cleared: true });
    return;
  }

  // Selecting a new single item -> select and immediately close with that item
  this.listData.forEach(d => (d[this.selectKey] = false));
  item[this.selectKey] = true;

  const originalIndex = this.listData.findIndex(original => original['data_id'] === item['data_id']);
  if (originalIndex !== -1) {
    this.listData[originalIndex][this.selectKey] = true;
  }

  this.hasError = false;
  this.cdr.detectChanges();

  // Publish the selected item (display item contains DisplayString and object)
  this.publishAndClose(item);
}

  // Save button (returns all when multi-select; or creates new entry if requested)
  saveSelection(newEntry?: boolean): void {
  if (this.listDetails.multiSelect) {
    this.publishAndClose(this.listData);
    return;
  }

  if (newEntry) {
    const val = this.searchText || '';
    this.publishAndClose({ newEntry: true, value: val.length > 50 ? val.substring(0, 50) : val });
    return;
  }

  const selectedItem = this.listData.find(d => d[this.selectKey]);
  if (selectedItem) {
    this.publishAndClose(selectedItem);
  } else {
    // Treat Done with no selection as a cancel (no-op), not a clear
    this.close();
  }
}
   clearSearch(): void {
    this.searchText = '';
    this.displayListData = [...this.listData];
  }

  // Close without data
  close(): void {
    if (this.modalCtrl) {
      this.modalCtrl.dismiss(null, 'cancel');
    }
    this.dismissed.emit(null);
  }

  /** Helpers */
  private publishAndClose(data: any): void {
    if (this.modalCtrl) {
      this.modalCtrl.dismiss(data ?? null, 'ok');
    }
    this.dismissed.emit(data ?? null);
  }

  private noDataSelected(): boolean {
    const key = this.selectKey;
    for (let i = 0; i < this.listData.length; i++) {
      if (this.listData[i][key]) return false;
    }
    return true;
  }
}
