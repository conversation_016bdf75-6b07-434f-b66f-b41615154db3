<ion-header>
  <ion-toolbar color="primary">
    <ion-title>{{ listDetails.title || ('Select List' | translate) }}</ion-title>

    <ion-buttons slot="start">
      <ion-button
        (click)="close()"
        *ngIf="listDetails.multiSelect && !listDetails.hideCloseBtn">
        {{ 'Close' | translate }}
      </ion-button>
    </ion-buttons>

    <ion-buttons slot="end">
      <ion-button
        (click)="saveSelection()"
        *ngIf="listDetails.multiSelect">
        {{ 'Save' | translate }}
      </ion-button>
      <ion-button
        (click)="saveSelection()"
        *ngIf="!listDetails.multiSelect">
        {{ 'Done' | translate }}
      </ion-button>
    </ion-buttons>
  </ion-toolbar>

  <ion-toolbar class="search-conatiner">
    <ion-searchbar
      #searchBar
      class="search-toolbar"
      [placeholder]="listDetails.searchPlaceHolder || ('Search List' | translate)"
      [(ngModel)]="searchText"
      (ionInput)="filterList('DisplayString', searchText || '')"
      (ionCancel)="clearSearch()">
    </ion-searchbar>
  </ion-toolbar>
</ion-header>

<ion-content [class.contrast]="theme === 'contrast'">
  <ion-list>
    <ion-item
      button
      detail="false"
      *ngFor="let data of displayListData; let i = index"
      [attr.id]="data['data_id']"
      (click)="toggleSelectData(data)"
      [class.has-error]="hasError"
      [class.item-selected]="data[selectKey]">

      <ion-checkbox
        slot="start"
        class="checkbox-themed"
        [attr.id]="'data-selection-element-' + (i + 1)"
        [checked]="data[selectKey]"
        (ionChange)="onCheckboxChange(data, $event)"
        (click)="$event.stopPropagation()">
      </ion-checkbox>

      <ion-label>{{ data.DisplayString || "-" }}</ion-label>
    </ion-item>
  </ion-list>
</ion-content>

