import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from '@ngrx/effects';
import * as StoreActions from './store.actions';
import * as RigActions from './store.actions';
import { DataService } from '../services/data.service';
import { catchError, map, delay ,mergeMap, of, from, withLatestFrom, filter, switchMap, tap, EMPTY, forkJoin, distinctUntilChanged, exhaustMap, take } from 'rxjs';
import { RIG_HEADER } from 'src/models/RIG_HEADER';
import { UtilityService } from "../services/utility.service";
import { selectIsSyncing, selectRigData, selectRigState, } from "./store.selector";
import { Store } from "@ngrx/store";
import { COMPANY_HEADER } from "src/models/COMPANY_HEADER";
import { CREW_HEADER } from "src/models/CREW_HEADER";
import { OPERATOR_HEADER } from "src/models/OPERATOR_HEADER";
import { UnviredCordovaSDK, RequestType } from "@awesome-cordova-plugins/unvired-cordova-sdk/ngx";
import { AppConstants } from '../constants/appConstants';
import { SynclogicService } from '../services/synclogic.service';
import { FORM_HEADER } from "src/models/FORM_HEADER";
import { MASTER_DATA_HEADER } from "src/models/MASTER_DATA_HEADER";

@Injectable()
export class StoreEffects{

    constructor(private actions$: Actions, private unviredSdk: UnviredCordovaSDK, private dataService: DataService , private formDataService: DataService,
       private store: Store , private utilityService: UtilityService, private synclogicService: SynclogicService){}


    incrementAfterDelay$ = createEffect( () =>
       this.actions$.pipe(
            ofType(StoreActions.incrementAsync),
            delay(1000),
            map(() => StoreActions.increment())
        )
      )


      switchRig$ = createEffect(() =>
        this.actions$.pipe(
      ofType(RigActions.switchRig),
tap(action => console.log('>>> switchRig caught in effect', action)),
          mergeMap(({ rigId, deviceId }) =>
            from(this.dataService.getRigHeader(rigId, deviceId)).pipe(
              mergeMap((response) => {
                if (
                  response.InfoMessage &&
                  Array.isArray(response.InfoMessage) &&
                  response.InfoMessage.some((msg: any) => msg.category === "FAILURE")
                ) {
                  const errorMsg = response.InfoMessage.map((msg: any) => msg.message).join(', ');
                  return of(RigActions.switchRigFailure({ error: errorMsg }));
                }
      
                const rigHeader = response.RIG?.[0]?.RIG_HEADER;
      
                return from([
                  RigActions.switchRigSuccess({ rigData: rigHeader }),
                  RigActions.loadRigFromDb() // ✅ dispatch DB fetch
                ]);
              }),
              catchError(error => {
                console.error('API error', error);
                return of(RigActions.switchRigFailure({ error }));
              })
            )
          )
        )
      );
      
      


loadRigFromDb$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.loadRigFromDb),
    mergeMap(() =>
      from(this.dataService.getRigHeaderFromDB()).pipe(
        map((result) => {
          if (result) {
            console.log('[Store-Fixes][Effects] Loaded rig data from DB:', JSON.stringify(result, null, 2));
            return RigActions.loadRigFromDbSuccess({ rigData: result });
          } else {
            console.log('[Store-Fixes][Effects] No rig data found in DB')
            throw new Error('No rig data found in DB');
          }
        }),
        catchError(error => {
          console.error('[Effect] DB Load Error:', error);
          return of(RigActions.loadRigFromDbFailure({ error }));
        })
      )
    )
  )
);


loadAllTemplatesFromDb$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.loadAllTemplatesFromDb),
    withLatestFrom(this.store.select(selectRigData), this.store),
    switchMap(([_, rigData, state]) => {
      if (!rigData || !rigData.RIG_NO) {
        console.warn('⚠️ [Store-Fixes] RigData is null. App state snapshot:', state);
        this.store.dispatch(RigActions.loadRigFromDb());
        // return EMPTY so the effect ends here
        return EMPTY;
      }

      const { COMP_CODE: company, RIG_NO: rigNo, RIG_TYPE: rigType, RIG_SUB_TYPE: rigSubType } = rigData;
      console.log('📦 [Store-Fixes] Calling DB with:', { company, rigNo, rigType, rigSubType });

      return from(this.dataService.getAllTemplatesFromDB(company, rigNo, rigType, rigSubType)).pipe(
        map(result => {
          console.log('✅ [Store-Fixes] Loaded templates from DB:', result);
          return RigActions.loadAllTemplatesDbSuccess({ templates: result });
        }),
        catchError(error => {
          console.error('❌ [Store-Fixes] [Effect] Template DB Load Error:', error);
          return of(RigActions.loadAllTemplatesDbFailure({ error }));
        })
      );
    })
  )
);





loadProgressBar$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.loadProgressBar),
    switchMap(() =>
      from(this.dataService.getProgressBarPercentage()).pipe( 
        map((percentage) =>
          RigActions.loadProgressBarSuccess({ percentage })
        ),
        catchError((error) =>
          of(RigActions.loadProgressBarFailure({ error }))
        )
      )
    )
  )
);

loadPrefilledData$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.loadPrefilledData),
    withLatestFrom(this.store.select(selectRigState)),
    switchMap(([_, rigState]) => {
      if (!rigState?.rigData) {
        console.warn('⚠️ rigData is null, skipping loadPrefilledData');
        return EMPTY; // exit effect early
      }

      return forkJoin({
        RIG_TYPE: of(rigState.rigData.RIG_TYPE).pipe(catchError(() => of(undefined))),
        RIG_SUB_TYPE: of(rigState.rigData.RIG_SUB_TYPE).pipe(catchError(() => of(undefined))),
        RIG_NO: of(rigState.rigData.RIG_NO).pipe(catchError(() => of(undefined))),
        COMP_CODE: of(rigState.rigData.COMP_CODE).pipe(catchError(() => of(undefined))),
        WORK_CENTER: of(rigState.rigData.WORK_CENTER).pipe(catchError(() => of(undefined))),
        TIME_ZONE: of(this.utilityService.getTimeZone()),
        RIG_MGR: from(this.dataService.getRigManagerFromDb(rigState.rigData)).pipe(
          map(data => data),
          catchError(() => of([]))
        ),
        COMPANY: from(this.dataService.getCompanyHeaderData()).pipe(
          map(data => data?.DESCR ?? undefined),
          catchError(() => of(undefined))
        ),
        LOGO: from(this.dataService.getCompanyHeaderData()).pipe(
          map(data => data?.LOGO ?? undefined),
          catchError(() => of(undefined))
        ),
        USER_ID: from(this.unviredSdk.userSettings()).pipe(
          map(settings => settings.data?.USER_ID ?? ''),
          catchError(() => of(''))
        ),
        USER_NAME: from(this.unviredSdk.userSettings()).pipe(
          map(settings =>
            `${(settings.data as any)?.FIRST_NAME?.trim() ?? ''} ${(settings.data as any)?.LAST_NAME?.trim() ?? ''}`.trim()
          ),
          catchError(() => of(''))
        ),
        OPERATOR: from(this.dataService.getOperatorHeaderData()).pipe(catchError(() => of([]))),
        CREW: from(this.dataService.getCrewHeaderData()).pipe(catchError(() => of([]))),
        NOTIFICATION_TYPE: from(this.dataService.getNotifTypeHeaderData()).pipe(catchError(() => of([]))),
        NOTIFICATION_PRIORITY: from(this.dataService.getPriorityHeaderData()).pipe(catchError(() => of([]))),
        NOTIFICATION_CODE_GROUP: from(this.dataService.getCodeGroupHeaderData()).pipe(catchError(() => of([]))),
        NOTIFICATION_CODE: from(this.dataService.getCodeHeaderData()).pipe(catchError(() => of([]))),
        NOTIFICATION_WORK_CENTER: from(this.dataService.getWorkCenterHeaderData()).pipe(catchError(() => of([]))),        

        MASTER_DATA: from(this.dataService.getMasterDataFromDB()).pipe(
          map(grouped => ({ ...grouped })),
          catchError(err => {
            console.error('MASTER_DATA error:', err);
            return of({});
          })
        )
      }).pipe(
        map(prefilledData => {
          console.log('✅ [Store-Fixes] Loaded Prefilled Data:', prefilledData);
          return RigActions.loadPrefilledDataSuccess({ prefilledData });
        }),
        catchError(error => {
          console.error('❌ [Store-Fixes][Effect] Prefilled Data Load Error:', error);
          return of(RigActions.loadPrefilledDataFailure({ error }));
        })
      );
    })
  )
);



  loadGraphToken$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RigActions.loadGraphToken),
      switchMap(() =>
        from(this.unviredSdk.syncForeground(RequestType.QUERY as any, {}, '', AppConstants.PA_GET_GRAPH_API_TOKEN, false) as any).pipe(
          map((result: any) => {
            const token = typeof result?.data === 'string' ? result.data : result?.data?.token;
            if (!token) throw new Error('No token');
            return RigActions.loadGraphTokenSuccess({ token });
          }),
          catchError((error) => of(RigActions.loadGraphTokenFailure({ error })))
        )
      )
    )
  );

loadSharepointSites$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.loadSharepointSites),
    switchMap(() =>
      from(this.dataService.getSharepointSitesFromDB()).pipe(
        map((result: any[]) => {
          console.log('✅ Loaded site headers:', result);
          return RigActions.loadSharepointSitesSuccess({ data: result });
        }),
        catchError((error) =>
          of(RigActions.loadSharepointSitesFailure({ error }))
        )
      )
    )
  )
);

  loadSpConfig$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RigActions.loadSpConfig),
      switchMap(() =>
        from(this.dataService.getSpConfigFromDB()).pipe(
          map((data) => RigActions.loadSpConfigSuccess({ data })),
          catchError((error) => of(RigActions.loadSpConfigFailure({ error })))
        )
      )
    )
  );

  loadSiteMeta$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RigActions.loadSiteMeta),
      switchMap(() =>
        from(this.unviredSdk.syncForeground(RequestType.QUERY as any, {}, '', AppConstants.PA_SP_SITE_META_GET, false) as any).pipe(
          map((res: any) => {
            const siteMetaArray = typeof res?.data === 'string' ? JSON.parse(res.data).SP_SITE_META : res?.data?.SP_SITE_META;
            return RigActions.loadSiteMetaSuccess({ siteMeta: Array.isArray(siteMetaArray) ? siteMetaArray : [] });
          }),
          catchError((error) => of(RigActions.loadSiteMetaFailure({ error })))
        )
      )
    )
  );

  updateDeviceSiteMeta$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RigActions.updateDeviceSiteMeta),
      switchMap(({ documents }) => {
        const lid = this.unviredSdk.guid();
        const updatePayload = { status: 'complete', timestamp: new Date().toISOString(), LID: lid, documents };
        return from(
          this.unviredSdk.syncBackground(RequestType.RQST as any, updatePayload, '', AppConstants.PA_SP_DEVICE_SITE_META_MODIFY, '', '', false) as any
        ).pipe(
          map(() => RigActions.updateDeviceSiteMetaSuccess()),
          catchError((error) => of(RigActions.updateDeviceSiteMetaFailure({ error })))
        );
      })
    )
  );

  checkServerSiteMeta$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RigActions.checkServerSiteMeta),
      switchMap(() =>
        from(this.unviredSdk.syncForeground(RequestType.QUERY as any, {}, '', AppConstants.PA_SP_SITE_META_GET, false) as any).pipe(
          map((raw) => RigActions.checkServerSiteMetaSuccess({ raw })),
          catchError((error) => of(RigActions.checkServerSiteMetaFailure({ error })))
        )
      )
    )
  );

autoStartSyncAfterInitData$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.initialDataDownloadComplete),
    map(() => {
      return RigActions.loadSharepointSites();
    })
  )
);

loadFormsFromDb$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.loadAllFormsFromDb),
    tap(() => console.log('[Store-Fixes][Effect] loadAllFormsFromDb triggered')),
    mergeMap(() =>
      from(this.dataService.getAllFormsFromTheDB()).pipe(
        tap(forms => console.log('[Store-Fixes][Effect] DB returned forms:', forms)),
        map(forms => RigActions.loadAllFormsDbSuccess({ forms })),
        catchError(error => {
          console.error('[Store-Fixes][Effect] DB load failed:', error);
          return of(RigActions.loadAllFormsDbFailure({ error }));
        })
      )
    )
  )
);

createForm$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.createForm),
    tap(() => console.log('[Effect] createForm triggered')),
    mergeMap(({ template }) => {
      // console.log('template in createform effect is ' , template);
      return from(this.dataService.createFormFromTemplateAndSaveToDB(template)).pipe(
        tap(result => console.log('[Effect] Form created:', result)),
        map(result => RigActions.loadAllFormsFromDb()),
        catchError(error => {
          console.error('[Effect] Form creation failed:', error.error);
          return of(RigActions.loadAllFormsDbFailure({ error }));
        })
      );
    })
  )
);


autoStartSyncAfterSharepointSites$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.loadSharepointSitesSuccess),
    tap(action => console.log('[Effect] loadSharepointSitesSuccess emitted with:', action.data)),
    filter(action => Array.isArray(action.data) && action.data.length > 0),
     withLatestFrom(this.store.select(selectIsSyncing)),
    filter(([action, isSyncing]) => !isSyncing),
    take(1),
    map(([action]) => {
      console.log('[Effect] Triggering rigDocSyncStart');
      return RigActions.rigDocSyncStart({ triggeredByUser: false });
    })
  )
);

rigDocSyncStart$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.rigDocSyncStart),
    tap(({ triggeredByUser }) => {
      try {
        console.log(`[Effects] rigDocSyncStart effect triggered - triggeredByUser: ${triggeredByUser}`);
        this.synclogicService.onRigDocSync(triggeredByUser);
      } catch (error) {
        console.error('[Effects] Error starting Rig Doc Sync:', error);
        this.store.dispatch(RigActions.rigDocSyncError({ error }));
      }
    })
  ),
  { dispatch: false }
);
loadStmrDataAndTopicsFromDb$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.loadStmrDataAndTopicsFromDb),
    mergeMap(() =>
    
      from(this.dataService.getStmrHeaderData()).pipe(
        mergeMap(stmrData =>
          from(this.dataService.getStmrTopicData()).pipe(
            tap(stmrTopicData => console.log('STMR Topic  & Data:', stmrTopicData , stmrData)),
            map(stmrTopicData =>
            
              RigActions.loadStmrDataAndTopicsFromDbSuccess({ stmrData, stmrTopicData })
            )
          )
        ),
        catchError(error => of(RigActions.loadStmrDataAndTopicsFromDbFailure({ error })))
      )
    )
  )
);


insertFormDataIntoDB$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.insertFormData),
    mergeMap(({ matchingForm, result }) =>
      from(this.formDataService.insertFormDataInDB(matchingForm, result)).pipe(
        map(() => RigActions.insertFormDataSuccess()),
        catchError((error) => of(RigActions.formDataFailure({ error })))
      )
    )
  )
);

updateFormDataInDB$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.updateFormData),
    mergeMap(({ matchingForm, result }) =>
      from(this.formDataService.updateFormDataInDB(matchingForm, result)).pipe(
        map(() => RigActions.updateFormDataSuccess()),
        catchError((error) => of(RigActions.formDataFailure({ error })))
      )
    )
  )
);

saveFormDataToServer$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.saveFormDataToServer),
    mergeMap(({ matchingForm }) =>
      from(this.formDataService.submitFormToServer(matchingForm)).pipe(
        map(() => RigActions.updateFormDataSuccess()),
        catchError((error) => of(RigActions.formDataFailure({ error })))
      )
    )
  )
); 

addFormToTopic$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RigActions.addFormToTopic),
      tap(() => console.log('[Effect] addFormToTopic triggered')),
      mergeMap(({ tmpltHeader, stmrTopicEntity }) => {
        return from(this.dataService.createStmrFormHeader(tmpltHeader, stmrTopicEntity)).pipe(
          tap(result => console.log('[Effect] Form added to topic:', result)),
          // map(() => RigActions.loadAllTopicsFromDb()), // ✅ after adding, reload topics
          map(() => RigActions.loadStmrDataAndTopicsFromDb()), // ✅ after adding, reload topics
          catchError(error => {
            console.error('[Effect] Form addition to topic failed:', error.error);
            return of(RigActions.loadStmrDataAndTopicsFromDbFailure({ error }));
          })
        );
      })
    )
  );

  loadReleaseNotesFromDb$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.loadReleaseNotesFromDb),
    tap(() => console.log('[Effects] loadReleaseNotesFromDb - Action triggered')),
    switchMap(() =>
      from(this.dataService.getReleaseNotesFromDB()).pipe(
        map(result => {
          console.log('[Effects] loadReleaseNotesFromDb - Success, count:', result.length);
          return RigActions.loadReleaseNotesDbSuccess({ releaseNotes: result });
        }),
        catchError(error => {
          console.error('[Effects] loadReleaseNotesFromDb - Error:', error);
          return of(RigActions.loadReleaseNotesDbFailure({ error }));
        })
      )
    )
  )
);



}