import { Injectable, NgZone } from '@angular/core';
import { <PERSON><PERSON><PERSON>ontroller, PopoverController } from '@ionic/angular/standalone';
import { IonButton, IonInput, IonItem, ModalController, IonRouterOutlet, MenuController } from '@ionic/angular/standalone';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx'
import { Platform } from '@ionic/angular/standalone'
import { File } from '@awesome-cordova-plugins/file/ngx';
import { selectAllForms, selectPrefilledData } from '../store/store.selector';
import { Store } from '@ngrx/store';
import { FORM_HEADER } from 'src/models/FORM_HEADER';
import { DataService } from './data.service';
import { BusyIndicatorService } from './busy-indicator.service';
import * as RigActions from '../store/store.actions';
import { AppConstants } from '../constants/appConstants';
import { UtilityService } from './utility.service';
import { ExternalLinkService } from './external-link.service';
declare var cordova: any;
declare let window: any;
@Injectable({
  providedIn: 'root'
})
export class FormListDisplayService {


 matchingForm: FORM_HEADER = new FORM_HEADER();
 formSelected: boolean = false;
 formInitalLoad:boolean = false;

   constructor(
        private menuCtrl: MenuController, private unviredSDK: UnviredCordovaSDK,
        private platform: Platform, private alertController: AlertController,private file: File,
        private dataService: DataService,
        private store: Store,
        private zone: NgZone,
        private utilityService: UtilityService,
        private externalLinkService: ExternalLinkService,
        private busyIndicatorService: BusyIndicatorService
    ) { 

        this.menuCtrl.swipeGesture(true)
        this.setupMessageListener()
    }

  /**
   * Handle form button clicks detected from iframe
   */

  async createEmbeddedIframe(htmlFileFolderPath: string, formHeader: FORM_HEADER) {
    this.formInitalLoad = true;
    console.log('htmlFileFolderPath', htmlFileFolderPath);
    try {
      console.log('Creating embedded iframe...');
  
      // Remove any existing iframe
      const existingIframe = document.getElementById('embedded-iframe');
      if (existingIframe) {
        existingIframe.remove();
      }
  
      // Create iframe container
      const iframeContainer = document.createElement('div');
      iframeContainer.id = 'iframe-container';
      iframeContainer.style.cssText = `
        position: fixed;
        top: 0px;
        left: 0;
        right: 0;
        bottom: 0;
        background: white;
        z-index: 10;   /* ✅ keep low so Ionic overlays (z ~ 10000) appear above */
      
      `;

  
      // Create iframe element
      const iframeElement = document.createElement('iframe') as any;
      iframeElement.id = 'embedded-iframe';
      iframeElement.style.cssText = `
        width: 100%;
        height: 100%;
        border: none;
        padding-top: env(safe-area-inset-top);
        padding-right: env(safe-area-inset-right);
        padding-left: env(safe-area-inset-left);
        padding-bottom: env(safe-area-inset-bottom);
        background: linear-gradient(to bottom, #429538 0%, #429538 50%, #F5F5F5 50%, #F5F5F5 100%);
      `;
  
      // ✅ Platform-specific handling
      const convertedUrl = (window as any).Ionic.WebView.convertFileSrc(htmlFileFolderPath);
      iframeElement.src = convertedUrl;
      console.log('Loading File: ', convertedUrl);
  
      iframeElement.onload = () => {
        console.log('✅ Iframe loaded successfully');

        // Give some time for the iframe content to fully initialize
        setTimeout(async () => {
            this.store.select(selectPrefilledData).subscribe((data: any) => {
              if (iframeElement.contentWindow && iframeElement.contentWindow.postMessage) {
                try {
                  iframeElement.contentWindow.postMessage({
                    type: 'load-default-request',
                    payload: data,
                    timestamp: Date.now()
                  }, '*');
    
                  this.store.select(selectAllForms).subscribe((forms: FORM_HEADER[]) => {
                    console.log('Forms: ', forms);
                   this.matchingForm = forms.find(form => form.FORM_ID === formHeader.FORM_ID) ?? new FORM_HEADER();

                    console.log('Matching Form: ', this.matchingForm);
                    console.log('form status ' , this.matchingForm?.FORM_STATUS)
                    let status = {
                      "FormStatus": this.matchingForm?.FORM_STATUS,
                      "IsSTMR": AppConstants.BOOL_FALSE
                    }
    
                      if(this.formInitalLoad){
                        this.formInitalLoad = false;
                    if (this.matchingForm && iframeElement && iframeElement.contentWindow) {
                      
                      try {
                        if (this.matchingForm.DATA != null ) {
                          let parsedData = JSON.parse(this.matchingForm.DATA)
                          if (Object.keys(parsedData).length > 0) {
                          iframeElement.contentWindow.postMessage({
                            type: 'load-previous-values-request',
                              payload: parsedData,
                            timestamp: Date.now()
                          }, '*');
                        }
                    
                        }
                        iframeElement.contentWindow.postMessage({
                          type: 'set-status-request',
                          payload: status,
                          timestamp: Date.now()
                        }, '*');
                      } catch (err: any) {
                        alert('Error in loadPreviousValues: ' + err.message);
                      }
                    }
                  }
                  });
                } catch (err: any) {
                  alert('Error in loadDefaultValues: ' + err.message);
                }
              }
            });
            
          
     
          await this.busyIndicatorService.hideBusyIndicator();

          }, 1000);
        
      };
  
      // Add elements to container

      iframeContainer.appendChild(iframeElement);
  
      // Add container to page
      document.body.appendChild(iframeContainer);
  
      console.log('Embedded iframe created successfully');

      await this.showAlert('Success', 'Form opened successfully.');
    } catch (error) {
      console.error('Error creating embedded iframe:', error);
      throw error;
    }
  }
  

async setupMessageListener() {
  // Listen for messages from iframe
  window.addEventListener('message', async (event: MessageEvent) => {
    console.log('📨 Received message from iframe:', event);
    
    if (event.data?.type === 'generate-json-response') {
      console.log('📊 JSON data received:', event.data.payload);
      this.handleJsonResponse(event.data.payload);
    } else if (event.data?.type === 'form-ready') {
      console.log('✅ Form is ready in iframe');
      this.onFormReady();
    }

    switch (event.data?.type) {
      case 'generate-json-response':
        this.handleJsonResponse(event.data.payload);
        break;
      case 'form-ready':
        this.onFormReady();
        break;
      case 'saveButtonClicked':
        let parsed = JSON.parse(event.data.result);
        let formId = parsed.ReferenceNo;   // or whatever the key is
        console.log('Form ID:', formId);
        
        await this.dataService.updateOrInsertFormHeaderInDB(this.matchingForm?.FORM_ID, this.matchingForm?.FORM_STATUS);
              await this.dataService.checkIfFormDataExists(this.matchingForm , event.data.result);
        this.handleSaveButtonClick(false);
        break;
      case 'saveAndExitButtonClicked':
        console.log('Save and exit button clicked in form' , this.matchingForm);
        await this.dataService.updateOrInsertFormHeaderInDB(this.matchingForm?.FORM_ID, this.matchingForm?.FORM_STATUS);
        await this.dataService.checkIfFormDataExists(this.matchingForm , event.data.result);
        this.store.dispatch(RigActions.saveFormDataToServer({matchingForm: this.matchingForm}));
        this.handleSaveButtonClick(true);
        break;
      case 'backButtonClicked':
        this.zone.run(() => this.handleBackButtonClick());
        break;

      case 'printButtonClicked':
        await this.handlePrintButtonClick();
        break;

      case 'submitButtonClicked':
       let formActionData = await this.dataService.createFormActionForSubmit(this.matchingForm);
       if(formActionData){
        console.log('Form action data is ' , formActionData);
       await this.dataService.insertFormActionInDB(formActionData);
      }
        await this.dataService.updateOrInsertFormHeaderInDB(this.matchingForm?.FORM_ID, 'SUBM');
        await this.dataService.checkIfFormDataExists(this.matchingForm , event.data.result);
        this.store.dispatch(RigActions.saveFormDataToServer({matchingForm: this.matchingForm}));
        this.handleSubmitButtonClick();
        break;

    }

  });
}

private handleJsonResponse(jsonData: any) {
  console.log('Processing JSON response:', jsonData);
  // Handle the JSON data from the form
  // You can save it, display it, or send it to your store
}

private onFormReady() {
  // Form is ready, send any prefill data
  // TODO: 
  console.log('FORM IS READY. TIME TO CALL FUNCTIONS')
}

  

  /**
   * Handle back button click from form
   */
  private async handleBackButtonClick() {
    console.log('⬅️ Back button clicked in form');
    this.formSelected = false;
    this.closeIframe();
  
  }

  /**
   * Handle save button click from form
   */
  private async handleSaveButtonClick(exit: boolean) {
    console.log(`💾 Save button clicked (exit: ${exit})`);
    
    await this.showAlert('Save Action', 
      exit ? 'Form saved and will exit' : 'Form saved successfully'
    );
    
    if (exit) {

      setTimeout(() => {
        this.closeIframe();
      }, 2000);
    }
  }

  /**
   * Handle submit button click from form
   */
  private async handleSubmitButtonClick() {
    console.log('📤 Submit button clicked in form');
    
    await this.showAlert('Submit Action', 'Form submitted successfully');
    
    setTimeout(() => {
      this.closeIframe();
    }, 2000);
  }

  /**
   * Handle skip button click from form
   */
  private async handleSkipButtonClick() {
    console.log('⏭️ Skip button clicked in form');
    
    // const shouldSkip = await this.showConfirmDialog(
    //   'Skip Form', 
    //   'Are you sure you want to skip this form?'
    // );
    
    // if (shouldSkip) {
    //   await this.showAlert('Skip Action', 'Form skipped');
    //   this.closeIframe();
    // }
  }

  /**
   * Handle print button click from form
   */
  private async handlePrintButtonClick() {
     console.log('🖨️ Print button clicked in form');
  
  if (cordova.platformId === 'ios') {
    try {
      if (!this.matchingForm?.FORM_ID) {
        await this.showAlert('Print Failed', 'No form ID available for printing.');
        return;
      }
      const url = await this.utilityService.getFormDisplayUrl(this.matchingForm.FORM_ID, this.matchingForm.VER_ID);
      this.externalLinkService.openFormtoPrint(url);
    } catch (error) {
      console.error('Error printing form:', error);
      await this.showAlert('Print Failed', 'Unable to print form. Please try again.');
    }
  } else {
    console.log('Print action triggered - handled by form');
  }
  }

  /**
   * Close the iframe
   */
  private closeIframe() {
    console.log('🔒 Closing iframe...');
    this.formSelected = false;
    const iframeContainer = document.getElementById('iframe-container');
    if (iframeContainer) {
      // Clear any URL monitoring intervals
      const iframe = document.getElementById('embedded-iframe') as any;
      if (iframe && iframe.urlMonitor) {
        clearInterval(iframe.urlMonitor);
      }
      
      iframeContainer.remove();
      console.log('✅ Iframe closed');
    }
  }

  /**
   * Generate JSON from the iframe form
   */
  async generateJson() {
    console.log('🔍 Generating JSON from iframe...');
    
    try {
      // Check if iframe is open
      const iframe = document.getElementById('embedded-iframe') as any;
      
      if (!iframe) {
        await this.showAlert('No Form', 'Please open a form first to generate JSON.');
        return;
      }
      
      console.log('📤 Calling generateJSON function in iframe...');
      
      // Try to call generateJSON function directly in iframe
      try {
        if (iframe.contentWindow && iframe.contentWindow.generateJSON) {
          const jsonResult = iframe.contentWindow.generateJSON();
          console.log('📊 JSON generated:', jsonResult);
          
          // Format the JSON for better display
          let formattedJson = jsonResult;
          try {
            const parsed = JSON.parse(jsonResult);
            formattedJson = JSON.stringify(parsed, null, 2);
          } catch (e) {
            console.log('Result is not valid JSON, using as is');
          }
          
          await this.showAlert('JSON Generated', `Form data:\n\n${formattedJson}`);
          
        } else {
          throw new Error('generateJSON function not available in iframe');
        }
      } catch (error) {
        console.error('❌ Error calling generateJSON:', error);
        await this.showAlert('Error', 'Could not generate JSON. The form may not be fully loaded or the generateJSON function is not available.');
      }
      
    } catch (error) {
      console.error('❌ Error generating JSON:', error);
      await this.showAlert('Error', `Failed to generate JSON: ${error}`);
    }
  }


  
  private async showAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK']
    });
    // await alert.present();
  }




 setStatuspostMessage(iframeElement: any,status: string) {
    try{
      iframeElement.contentWindow.postMessage({
            type: 'set-status-request',
            payload: status,
            timestamp: Date.now()
        }, '*')
    }
    catch(err: any) {
        alert('Error in setStatus: ' + err.message);
    }
}


 loadDefaultValuespostMessage(iframeElement: any ,lists: any) {
  // we expect the lists object to be 
  // passed in from the container app
  // This object contains data from
  // - SAP/DAR
  // - Current environment
  // - Company logo as base64
  try{
    iframeElement.contentWindow.postMessage({
          type: 'load-default-request',
          payload: lists,
          timestamp: Date.now()
      }, '*')
  }
  catch(err: any) {
      alert('Error in loadDefaultValues: ' + err.message);
  }
}


 loadPreviousValuespostMessage(iframeElement: any ,data: any) {
  // we expect a forms data json object
  try{
    iframeElement.contentWindow.postMessage({
          type: 'load-previous-values-request',
          payload: data,
          timestamp: Date.now()
      }, '*')
  }
  catch(err: any) {
      alert('Error in loadPreviousValues: ' + err.message);
  }
}


  
}
