angular.module('PDTest')
.directive('pdFooter', function() {
    function getHTML(model) {
        // Job ID, Rig #, Manager
        var html='<br>';
        html +='<div class="row">';
        html +='    <div class="col-sm-10 small"><div ng-if="'+model+'.CreatedBy">Created by: {{'+model+'.CreatedBy}}, {{'+model+'.CreateDate|date: \'MMM dd yyyy H:mm\': \''+model+'.CreateDateTzOffset\'}} {{' +model+'.CreateDateTzAbbr}}  (24hrs)</div></div>';
        html +='    <div class="col-sm-10 text-right small">{{form.title}}</div>';
        html +='</div>';
        html +='<div class="row">';
        html +='    <div class="col-sm-10 small"><div ng-if="'+model+'.UpdateDate">Updated by: <span ng-if="'+model+'.UpdateDate !== undefined"> {{'+model+'.UpdatedBy}}, {{'+model+'.UpdateDate|date: \'MMM dd yyyy H:mm\': \''+model+'.CreateDateTzOffset\'}} {{' +model+'.CreateDateTzAbbr}}  (24hrs) </span></div></div>';
        html +='    <div class="col-sm-10 text-right small">Form version: {{form.version}}, {{form.releaseDate|date: \'MMM dd yyyy\'}}</div>';
        html +='</div>';
        return html;
    }

    return {
        restrict: 'EA',
        template: function(elem, attr){
            return getHTML(attr.model);
         },
    };
});