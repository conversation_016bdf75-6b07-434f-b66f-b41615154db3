import { Injectable } from "@angular/core";

@Injectable()
export class AppConstants {

    // We update this release number when there is a new feature or a bug fix.
    // For incremental builds, we only update the build number.
    static RELEASE_NUMBER = "0.0.2";
    static RELEASE_DATE = "@@RELEASE_DATE@@";
    static BUILD_NUMBER = "@@BUILD_NUMBER@@";

    static APPLICATION_NAME = "FORMS";
    static COMPANY_NAME = "PD";
    static UMP_URL = "https://ump.pd.com:8443/UMP";
    static DOMAIN = "PD";
    static LOGIN_TYPE_ADS = "ADS";
    static LOGIN_ADS_PORT = "MS_AD1_PORT";
    
    static REFRESH_TIMEOUT = 5 * 60 * 1000;  // Minutes * Seconds * milliseconds
    static RESET_RIG_SAVEPOINT_NAME = "resetRigData";
    static DOUBLE_TAP_IGNORE_DURATION = 2000;  // Ignore double taps within this duration.

    // CTA Local Path
    static SETTING_CTA_LOCAL_PATH = "CTA_LOCAL_PATH"
    static SETTING_HSE_LOCAL_PATH = "HSE_LOCAL_PATH"
    static SETTING_ACTUAL_RIG_DOC_PATH = "ACTUAL_RIG_DOC_PATH"
    static SP_LOCAL_ROOT = "SP_LOCAL_ROOT"
    static SETTING_RUN_MODE = "RUN_MODE"
    static SETTING_MODE_TEST_AUTOMATION = "TEST_AUTOMATION"
    static SETTING_MODE_PRODUCTION_RUN = "PRODUCTION_RUN"
    
    static SETTING_TEST_SIGNATURE = "data:image/png;base64,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"
  

    static LOGIN_UMP_URLS = [
        {
            name: "Production",
            value: "https://ump.pd.com:8443/UMP"
        },
        {
            name: "Quality",
            value: "https://umpqa.pd.com:8443/UMP"
        },
        {
            name: "Development",
            value: "https://umpdev.pd.com:8443/UMP"
        },
        // {
        //     name: "Development New",
        //     value: "https://umpdevnew.pd.com:8443/UMP"
        // },
        {
            name: "Quality New",
            value: "https://umpqanew.pd.com:8443/UMP"
        },
        {
            name: "Production New",
            value: "https://umpnew.pd.com:8443/UMP"
        }
    ]

    static IN_APP_BROWSER_LISTENER = {
        SUBMIT_FORM: "submitButtonClicked",
        BACK: "backButtonClicked",
        SAVE_FORM: "saveButtonClicked"
    }
    // CONSTANT VALUES
    static VAL_CTA_TOPIC_ID = "TOPIC_02";
    static VAL_HSE_TOPIC_ID = "TOPIC_12";
    static VAL_FORM_STATUS = {
        "OPEN": "OPEN",
        "INPR": "INPR",
        "SUBM": "SUBM",
        "CAN_BE_SUBM": "CAN_BE_SUBM",
        "SKIP": "SKIP"
    };
    static VAL_FORM_STATUS_DESCR = {
        "OPEN": {
            descr: "Created",
            color: "open"
        },
        "INPR": {
            descr: "In Progress",
            color: "inpr"
        },
        "SUBM": {
            descr: "Completed",
            color: "secondary"
        },
        "CAN_BE_SUBM": {
            descr: "In Progress",
            color: "inpr"
        },
        "QUEUED": {
            descr: "Queued",
            color: "inpr"
        },
        "SENT": {
            descr: "Sent",
            color: "primary-green"
        },
        "ERROR": {
            descr: "Error",
            color: "primary-red"
        },
        "SENDING": {
            descr: "Sending ...",
            color: "pending"
        },
        "SKIP": {
            descr: "Skipped",
            color: "secondary"
        }
    };
    static VAL_TMPLT_STATUS = {
        "INIT": "INIT",
        "REL": "REL",
        "ARCH": "ARCH"
    };
    static TIME_DATE_FORMAT = "YYYY-MM-DD HH:mm:ss";

    // PA CONSTANTS
    static PA_FORMS_CUSTOMIZING_GET = "FORMS_PA_CUSTOMIZING_GET_V2";
    static PA_FORMS_CATEGORY_GET = "FORMS_PA_CATEGORY_GET";
    static PA_FORMS_TEMPLATE_GET = "FORMS_PA_TEMPLATE_GET";
    static PA_FORMS_CTA_GET = "FORMS_PA_CTA_GET";
    static PA_FORMS_FORM_GET = "FORMS_PA_FORM_GET";
    static PA_FORMS_STMR_SUBMIT = "FORMS_PA_STMR_SUBMIT";
    static FORMS_PA_FORM_SUBMIT = "FORMS_PA_FORM_SUBMIT";
    static FORMS_PA_RIG_GET = "FORMS_PA_RIG_GET_V2";
    static FORMS_PA_HISTORY_GET = "FORMS_PA_HISTORY_GET";
    static FORMS_PA_HSE_STANDARD_GET = "FORMS_PA_HSE_STANDARD_GET";
    static FORMS_PA_CREW_GET = "FORMS_PA_CREW_GET_V2";
    static FORMS_PA_TEMPLATE_DOWNLOAD = "FORMS_PA_TEMPLATE_DOWNLOAD";
    static PA_RELEASE_NOTE_GET = "FORMS_PA_RELEASE_NOTE_GET"; // Get release notes
    static PA_RELEASE_NOTE_MODIFY = "FORMS_PA_RELEASE_NOTE_MODIFY"; // Modify Release Note
    static PA_FORM_DISPLAY="FORMS_PA_FORM_DISPLAY";
    static PA_RELEASE_NOTE_PUBLISH = "FORMS_PA_RELEASE_NOTE_PUBLISH"; // Publish release note
    static PA_FORM_CREATE_AND_COPY = "FORMS_PA_FORM_CREATE_AND_COPY"; // Create form and copy    
    static PA_FORM_ALERT_GET = "FORMS_PA_FORM_ALERT_GET"; // Get open form alerts
    static PA_SP_SITE_META_GET = "FORMS_PA_SP_SITE_META_GET"; // Get site meta data
    static PA_SP_DEVICE_SITE_META_MODIFY = "FORMS_PA_SP_DEVICE_SITE_META_MODIFY"; // Modify device state
    static PA_GET_GRAPH_API_TOKEN="FORMS_PA_GET_GRAPH_API_TOKEN" //get access token 
    // TABLE CONSTANTS
    static TABLE_FORM_HEADER = "FORM_HEADER";
    static TABLE_CATEGORY_HEADER = "CATEGORY_HEADER";
    static TABLE_TEMPLATE_HEADER = "TMPLT_HEADER";
    static TABLE_TEMPLATE_VERSION = "TMPLT_VER";
    static TABLE_TEMPLATE_ASSIGN = "TMPLT_ASSGN";
    static TABLE_TOPIC_HEADER = "TOPIC_HEADER";
    static TABLE_CREW_HEADER = "CREW_HEADER";
    static TABLE_CTA_HEADER = "CTA_HEADER";
    static TABLE_CTA_DOC = "CTA_DOC";
    static TABLE_CTA_TMPLT = "CTA_TMPLT";
    static TABLE_RIG_HEADER = "RIG_HEADER";
    static TABLE_COMPANY_HEADER = "COMPANY_HEADER";
    static TABLE_STMR_FORM = "STMR_FORM";
    static TABLE_TMPLT_ATTACHMENT = "TMPLT_ATTACHMENT";
    static TABLE_FORM_DATA = "FORM_DATA";
    static TABLE_STMR_FORM_DATA = "STMR_FORM_DATA";
    static TABLE_STMR_CREW = "STMR_CREW";
    static TABLE_STMR_TOPIC = "STMR_TOPIC";
    static TABLE_STMR_HEADER = "STMR_HEADER";
    static TABLE_HSE_STANDARD_HEADER = "HSE_STANDARD_HEADER"
    static TABLE_HSE_STANDARD_DOC = "HSE_STANDARD_DOC"
    static TABLE_NOTIF_HEADER = "NOTIF_HEADER";
    static TABLE_CTA_ASSIGNMENT = "CTA_ASSGN";
    static TABLE_SETTING_HEADER = "SETTING_HEADER";
    static TABLE_OPERATOR_HEADER = "OPERATOR_HEADER";
    static TABLE_FORM_ACTION = "FORM_ACTION";
    static TABLE_STMR_ACTION = "STMR_ACTION";
    static TABLE_RIG_MANAGER = "RIG_MANAGER";
    static TABLE_RELEASE_NOTE = "RELEASE_NOTE_HEADER";
	static TABLE_FORM_SCHD_ALERT = "FORM_SCHD_ALERT_HEADER";
	static TABLE_FORM_SCHD_LOG_V = "FORM_SCHD_LOG_V_HEADER";
    static TABLE_C_NOTIF_TYPE_HEADER = "C_NOTIF_TYPE_HEADER";
    static TABLE_C_CODE_GROUP_HEADER = "C_CODE_GROUP_HEADER";
    static TABLE_C_CODE_HEADER = "C_CODE_HEADER";
    static TABLE_C_PRIORITY_HEADER = "C_PRIORITY_HEADER";
    static TABLE_C_RIG_WRK_CNTR_HEADER = "C_RIG_WRK_CNTR_HEADER";
    static TABLE_SP_SITE_META_HEADER = "SP_SITE_META_HEADER";
    static TABLE_SP_SITE_DOC = "SP_SITE_DOC";
    static TABLE_SP_SITE_LIB = "SP_SITE_LIB";
    static TABLE_SP_CONFIG_HEADER = "SP_CONFIG_HEADER";
    static TABLE_SP_SITE_HEADER = "SP_SITE_HEADER";
    static TABLE_SP_DEVICE_FOLDER_HEADER = "SP_DEVICE_FOLDER_HEADER";
    static TABLE_MASTER_DATA_HEADER = "MASTER_DATA_HEADER";

    // DATABASE CONSTANTS
    static FORM_HEADER = "FORM_HEADER";
    static TOPIC_NAME = "TOPIC_NAME";
    static STMR_HEADER = "STMR_HEADER";
    static STMR_CREW = "STMR_CREW";
    static STMR_TOPIC = "STMR_TOPIC";
    static STMR_FORM = "STMR_FORM";
    static STMR_FORM_DATA = "STMR_FORM_DATA";
    static TMPLT_HEADER = "TMPLT_HEADER";
    static VER_NO = "VER_NO";
    static VER_ID = "VER_ID";
    static CRTD_BY = "CRTD_BY";
    static CRTD_ON = "CRTD_ON";
    static TMPLT_VER = "TMPLT_VER";

    // KEY NAMES
    static KEY_ERROR = "Error";
    static KEY_TYPE = "type"
    static KEY_COUNT = "count";
    static KEY_DELETE = "delete";

    // VALUES
    static VAL_FORM = "form";
    static VAL_TEMPLATE = "template";
    static VAL_NOTIF = "notification";

    // BOOL VALUES
    static BOOL_TRUE = true;
    static BOOL_FALSE = false;

    // EVENTS
    static EVENT_CREATE_STMR_SUCCESSS = "createStmrSuccess";
    static EVENT_FORMS_DEMAND_LOAD = "loadFormsOnDemand";
    static EVENT_FORMS_LOAD_FROM_SERVER = "FormsLoadFromServer";
    static EVENT_TMPLT_DEMAND_LOAD = "loadTmpltOnDemand";
    static EVENT_STMR_FORM_STATUS_CHANGE = "stmrFormStatusChange";
    static EVENT_STMR_DEMAND_LOAD = "loadOnlyStmrOnDemand";
    static EVENT_STMR_TOPIC_CHANGE = "stmrTopicChange";
    static EVENT_NOTIFY_DATA_CHANGE = "notifyDataChange";
    static EVENT_COM_LOGO_LOAD = "comLogoLoad";
    static EVENT_REQUEST_QUEUED = "RequestQueued";
    static EVENT_DATA_CLEAR_UPDATE = "dataClearedUpdate";
    static EVENT_PREFILL_CHANGE = "prefillDataChange";
    static EVENT_HOME_PAGE_LOAD = "loadHomePageData";
    static EVENT_REFRESH_DATA = "refreshData";
    static EVENT_STMR_HEADER_UPDATE = "stmrHeaderUpdate";
    static EVENT_LOAD_ALL_DATA_FROM_SERVER = "downloadAllDataFromServer"
    static EVENT_UPDATE_STMR_SAVEPOINT = "updateSTMRSavepoint";
    static INVALID_TOKEN_ERROR_MESSAGE = 'Application Exception'

    // MESSAGES
    static CLEAR_DATA_CONFIRM_MSG =  "Are you sure you want to delete all data from this application?\n\n" +
  "All PDF files and documents downloaded will be cleared. Please close any open PDF files and then tap on 'Clear Data'.\n\n" +
  "You will have to re-authenticate in order to use the application.";
    static LOGOUT_MSG = "Clearing data...";
    static DELETE_FORMS_MSG = "Are you sure you want to delete?";
    static CREATE_STMR_CONFIRM_MSG = "Are you sure you want to create a new STMR ?";
    static SAVE_TOPIC_MSG = "Please save the topic to fill the form.";
    static NEW_TEMPLATES_DOWNLOADED_MSG = "New Template(s) are downloaded.";
    static COULD_NOT_SEND_REQUEST = "Could not send the request";
    static REQUEST_IN_PROCESS = "Please wait until all the data is downloaded.";
    static ASYNC_RQST_PLACED = "A request to download all data has been placed. Please wait...";
    static CONN_LOST_MSG = "Connection lost!";
    static VALIDATE_RIG_ERR_MSG = "Please enter a valid number.";
    static CHANGE_RIG_WARN_MSG = "<br/> Changing site will clear all un-submitted data. This cannot be undone.";
    static RIGNO_TO_BE_DIFFERENT_MSG = "Site Number should not be same as old Site Number.";
    static READING_FORM_DATA_ERR_MSG = "There was an error reading data. Please contact your administrator.";
    static FORM_IS_IN_SENT_MSG = "Data reconciliation is in progress for this form. Editing of any data is not allowed during this time. Please try after some time.";
    static STMR_IS_IN_SENT_MSG = "Data reconciliation is in progress for this STMR. Editing of any data is not allowed during this time. Please try after some time.";
    static RIG_REG_EXP = /^\+?\d+$/;
    static RIG_REG_EXP_V2 = /^[a-zA-Z0-9_]*$/
    static INVALID_RIG_ERR_MSG = "Please enter valid Site number.";
    static DOC_SYNC_BUSY_MSG = "Syncing documents...";

    // NETWORK CONNECTION TYPE
    static NETWORK_CONNECTION_TYPE_NONE = "none";

    // OBJECT STATUS CONSTANTS
    static OBJECT_STATUS = {
        GLOBAL: 0,
        ADD: 1,
        MODIFY: 2,
        DELETE: 3
    }

    // SYNC STATUS CONSTANTS
    static SYNC_STATUS = {
        NONE: 0,
        QUEUED: 1,
        SENT: 2,
        ERROR: 3
    }

    // NOTIFICATION LISTNER TYPE
    static NOTIF_STATUS = {
        DATA_SEND: 0,
        DATA_CHANGED: 1,
        DATA_RECEIVED: 2,
        APP_RESET: 3,
        ATTACHMENT_DOWNLOADED: 4,
        ATTACHMENT_DOWNLOAD_ERROR: 5,
        INBOX_PROCESSING_COMPLETE: 6,
        ATTACHMENT_WAITING_DOWNLOAD: 7,
        NOTIFY_INFO_MESSAGE: 8,
        NOTIFY_SERVER_ERROR: 9,
        ATTACHMENT_DOWNLOAD_COMPLETE: 10,
        NOTIFY_SENT_ITEM_CHANGE: 11,
        JWT_TOKEN_EXPIRED: 12
    }

    // ACTION CODE VALUES
    static ACTION_CODE = {
        COMPLETE: "COMPLETE",
        SKIP: "SKIP"
    }

    static VAL_ONLINE_CONN_STATUS = {
        "CONNECTED": {
            descr: "Connected",
            color: "connected"
        },
        "CONNECTING": {
            descr: "Connecting...",
            color: "connecting"
        },
        "NOT_CONNECTED": {
            descr: "Not Connected",
            color: "not-connected"
        }
    }
  static DELETE_FORMS_DAYS: number = 1;

    // Datasender Lock Statuses
    static LOCK_STATUS = {
        NOT_IN_QUEUE: 0,
        SENDING: 1,
        LOCKED: 2
    }

    static DS_MENU_COUNT = "MENU_COUNT"
    static LAST_SAVED_STATE = "LAST_SAVED_STATE"
}
