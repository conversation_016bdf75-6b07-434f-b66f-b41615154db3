/* Toolbar */
.custom-toolbar {
  --background: var(--ion-color-primary);
  --color: #fff;
}
.normal-case {
    text-transform: none !important;
  }

.header-b, .section-header {
  background: #4a4a4a;
  color: #fff;
  border-radius: 0; /* sharp edges */
  padding:0 12px;
  font-weight: bold;
  height:45px !important;
  display: flex;
  align-items: center; 
}

// ion-list {
//   margin: 12px;
// }

/* Item layout */
ion-item {
  --padding-start: 12px;
  --inner-padding-end: 12px;
}

ion-thumbnail {
  width: 48px;
  height: 48px;
}


.bwrap {
  font-weight: 500;
  font-size: 14px;
}

.meta-row {
  color: #6b7280; /* gray-500 */
  font-size: 13px;
}

/* Unread star overlay positions if used */
.overlap-star-comment {
  position: absolute;
  left: 30px;
  transform: translateY(-50%);
  color: #f59e0b; /* amber */
  font-size: 16px;
}

.overlap-wifi-comment {
  position: absolute;
  left: -8px;
  transform: translateY(-50%);
  color: #f59e0b;
  font-size: 16px;
}

.no-items {
  color: #6b7280;
}

.group-header {
  --background: #4a4a4a;
  color: white;
  font-weight: bold;
}

.group-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

