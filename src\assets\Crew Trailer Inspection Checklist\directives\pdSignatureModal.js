angular.module('PDTest')
.directive('pdSignatureModal', function() {
    var html='    <div id="signatureModal" class="modal fade" role="dialog">';
    html +='        <div class="modal-dialog">';
    html +='            <div class="modal-content">';
    html +='                <div class="modal-header">';
    html +='                    <button type="button" class="close" data-dismiss="modal">&times;</button>';
    html +='                    <h4 class="modal-title">Please sign below</h4>';
    html +='                </div>';
    html +='                <div class="modal-body">';
    html +='                    <div class="signcontainer">';
    html +='                        <signature-pad accept="acceptSignature" clear="clearSignature" height="220" width="568" dataurl="signatureImage"></signature-pad>';
    html +='                    </div>';
    html +='                </div>';
    html +='                <div class="modal-footer">';
    html +='                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>';
    html +='                    <button type="button" class="btn btn-danger"';
    html +='                        ng-hide="submitted"';
    html +='                        ng-click="clearSignature()">Clear</button>';
    html +='                    <button type="button" class="btn btn-success" data-dismiss="modal"';
    html +='                    ng-hide="submitted"';
    html +='                    ng-click="gotSignature()">Accept</button>';
    html +='                </div>';
    html +='            </div>';
    html +='        </div>';
    html +='    </div>';

    return {
        restrict: 'EA',
        template: html
    };

});