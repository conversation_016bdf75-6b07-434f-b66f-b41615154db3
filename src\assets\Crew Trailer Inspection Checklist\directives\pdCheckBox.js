angular.module('PDTest')
.directive('pdCheckBox', function() {
    function getHTML(model, caption, yes, no) {
        var html='<div class="vertical-align">';
        html +='<span class="icon-selected"';
        html +='    ng-click="submitted ? null: ('+model+' === '+yes+') ? '+model+' = '+no+' : '+model+' = '+yes+';contentForm.$dirty = true">';
        html +='    <svg ng-if="'+model+'==='+no+'" class="icon icon-ion-android-checkbox-outline-blank"><use xlink:href="#icon-ion-android-checkbox-outline-blank"></use></svg>';
        html +='    <svg ng-if="'+model+'==='+yes+'" class="icon icon-ion-android-checkbox"><use xlink:href="#icon-ion-android-checkbox"></use></svg>';
        html +='</span>&nbsp;&nbsp;'+caption;
        html +='</div>';

        return html;
    }

    return {
        restrict: 'EA',
        template: function(elem, attr){
            var yes = 'true';
            var no = 'false';

            if(attr.yes) { yes = attr.yes; }
            if(attr.no) { no = attr.no; }
            
            return getHTML(attr.model, attr.caption, yes, no);
         },
    };

});