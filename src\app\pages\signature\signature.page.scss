.crew-info {
  flex-direction: column !important;
  align-items: flex-start !important;
  padding: 8px 12px;

  .crew-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .crew-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    word-break: break-word; // handles long names
  }

  .crew-pos {
    font-size: 0.9rem;
    margin: 4px 0 0;
    color: #666;
    word-break: break-word;
  }
}

.sign-container {
  border: 1px solid rgb(89, 89, 89);
  margin: 10px auto;
  padding: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 100%;
  min-height: 200px;        // ensure minimum height for Electron
  box-sizing: border-box;
  background-color: #fff;   // ensure white background for signature
}

canvas {
  display: block;           // remove any inline styling issues
  touch-action: none;       // prevent page scroll when drawing
  border: none;
  background-color: transparent;
  cursor: default;        // better cursor for drawing
}

.button-header {
 --color: #fff !important;
  font-weight: 500;
  font-size: 0.9rem;
  line-height: 1;
}
