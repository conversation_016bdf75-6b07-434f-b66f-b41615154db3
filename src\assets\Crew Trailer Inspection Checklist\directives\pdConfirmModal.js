angular.module('PDTest')
.directive('pdConfirmModal', function() {
    var html='    <div id="confirmModal" class="modal fade" role="dialog">';
    html +='        <div class="modal-dialog">';
    html +='            <div class="modal-content">';
    html +='                <div class="modal-header">';
    html +='                    <button type="button" class="close" data-dismiss="modal">&times;</button>';
    html +='                    <h4 class="modal-title">Unsaved changes</h4>';
    html +='                </div>';
    html +='                <div class="modal-body">';
    html +='                    <p>You have unsaved changes.  Are you sure you want to exit this form?</p>';
    html +='                </div>';
    html +='                <div class="modal-footer">';
    html +='                    <button type="button" class="btn btn-primary" data-dismiss="modal"';
    html +='                        ng-click="exitConfirmed()">Okay</button>';
    html +='                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>';
    html +='                </div>';
    html +='            </div>';
    html +='        </div>';
    html +='    </div>';

    return {
        restrict: 'EA',
        template: html
    };

});