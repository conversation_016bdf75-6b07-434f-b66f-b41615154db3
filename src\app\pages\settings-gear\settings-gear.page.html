<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-button style="color: white" (click)="close()" >
        <ion-icon name="arrow-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>
      Settings
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <ion-card >
    <div *ngIf="offline" style="background-color: red; color: black; text-align: center;">No Internet
      Connectivity</div>
    <div *ngIf="!offline && server" style="background-color: green; color: white; text-align: center;">Connected
    </div>
    <ion-progress-bar *ngIf="processbar" type="indeterminate"></ion-progress-bar>

    <ion-item lines="none">
      <ion-buttons slot="start">
        <ion-button (click)="connect()" [ngClass]="connection ? 'connectionsuccess' : 'connectionerror'">
          <fa-icon class="icon-style" icon="bolt" class="fa-lg" slot="icon-only"></fa-icon>
        </ion-button>
      </ion-buttons>
      <ion-label>
         <p style="color:black; cursor: pointer; font-size: smaller;">&nbsp;{{dataService.selectedServer}}&nbsp;</p>
        <!-- <h4 style="padding-bottom:8px">&nbsp;{{userDetails.username}}</h4> we do not get this in the userdetails result so commented out -->
        <p>&nbsp;{{userDetails.userid}}</p>
        <p>&nbsp;{{userDetails.url}}</p>
      </ion-label>
    </ion-item>
  </ion-card>

  <ion-card *ngIf="device.platform != 'browser'">
    <ion-item lines="none">
      <ion-buttons slot="start">
        <ion-button (click)="sync()">
          <fa-icon class="icon-style" icon="sync-alt" class="fa-lg" style="color: #3D9140"
            slot="icon-only"></fa-icon>
        </ion-button>
      </ion-buttons>
      <ion-label>
        <h4 style="padding-bottom:8px">Sync Status</h4>
        <p>{{syncStatus}}</p>
      </ion-label>
      <ion-buttons>
        <ion-button (click)="dataSender()" *ngIf="dataSenderFlag">
          <fa-icon class="icon-style" icon="cloud-upload-alt" class="fa-lg" style="color: #3D9140"
            slot="icon-only"></fa-icon>
        </ion-button>
        <ion-button (click)="getMessage()" *ngIf="getMessageFlag">
          <fa-icon class="icon-style" icon="cloud-download-alt" class="fa-lg" style="color: #3D9140"
            slot="icon-only"></fa-icon>
        </ion-button>
      </ion-buttons>
      <img *ngIf="loadingGifFlag" style="height:44px;width:44px;" src="assets/load.gif">
    </ion-item>
  </ion-card>

  <ion-card>
    <ion-item lines="none">
      <ion-buttons slot="start">
        <ion-button (click)="info()" [ngClass]="infoConnection ? 'connectionsuccess' : 'connectionerror'">
          <fa-icon class="icon-style" icon="circle-info" class="fa-lg" slot="icon-only"></fa-icon>
        </ion-button>
      </ion-buttons>
      <ion-label>
        <h4 style="padding-bottom:8px">Errors, Warnings and Info</h4>
        <p>Errors: {{infoErrorCount}} &nbsp;&nbsp; Others: {{infoOtherCount}} </p>
      </ion-label>
    </ion-item>
  </ion-card>


  <ion-card>
    <ion-item lines="none" class="logs-item">
      <ion-buttons slot="start">
        <ion-button (click)="logs($event)">
          <fa-icon class="icon-style" icon="file-lines" class="fa-lg" style="color:rgb(133, 135, 138)" slot="icon-only">
          </fa-icon>
        </ion-button>
      </ion-buttons>
      <ion-label>
        <h4 style="padding-bottom:4px">&nbsp;Logs</h4>
       <div class="log-level-row">
        <ion-select [(ngModel)]="loglevel"
                    (ionChange)="setLogLevel($event)"
                    interface="popover"
                    class="log-select">
          <ion-select-option *ngFor="let level of logLevel"
                             [value]="level.value">
            {{ level.viewValue }}
          </ion-select-option>
        </ion-select>
      <ion-buttons>
        <ion-button (click)="upload($event)">
          <fa-icon class="icon-style" icon="cloud-upload-alt" class="fa-lg" style="color: #3D9140"
            slot="icon-only"></fa-icon>
        </ion-button>
        <ion-button (click)="email($event)">
          <fa-icon class="icon-style" icon="envelope" class="icon-style" class="fa-lg"
            style="color:rgba(224, 131, 9, 0.952)" slot="icon-only"></fa-icon>
        </ion-button>
      </ion-buttons>
</div>
</ion-label>
    </ion-item>
  </ion-card>

  <ion-card>
    <ion-item lines="none">
      <ion-buttons slot="start">
        <ion-button (click)="pushNotification()">
          <fa-icon class="icon-style" icon="bell" class="fa-lg" style="color: #3D9140" slot="icon-only">
          </fa-icon>
        </ion-button>
      </ion-buttons>
      <ion-label>
        <h4 style="padding-bottom:8px">Push Notification</h4>
        <p>Tap on the icon to test notification</p>
      </ion-label>
    </ion-item>
  </ion-card>

  <ion-card>
    <ion-item lines="none">
      <ion-buttons slot="start">
        <ion-button (click)="trash()">
          <fa-icon class="icon-style" icon="trash-alt" class="fa-lg" style="color:rgba(179, 43, 43, 0.863)"
            slot="icon-only"></fa-icon>
        </ion-button>
      </ion-buttons>
      <ion-label>
        <h4 style="padding-bottom:8px">Advanced</h4>
        <p>Tap on the icon to reset the app </p>
      </ion-label>
    </ion-item>
  </ion-card>
</ion-content>
<div style="position: absolute; bottom: 20px; width: 100%">
  <div style="text-align: center;color:black;">
    <span style="font-size: 13px;">{{version }} - {{release}}</span>
  </div>
</div>