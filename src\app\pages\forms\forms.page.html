<ion-header *ngIf="!formsDisplayService.formSelected">
  <ion-toolbar color="primary" class="custom-toolbar">
    <ion-buttons slot="start">
      <ion-menu-button autoHide="false" class="white-icon"></ion-menu-button>
    </ion-buttons>
    <ion-title class="left-title">Forms</ion-title>
    <!-- <ion-button slot="end" (click)="getForms()">Refresh forms</ion-button>
  

    <ion-button *ngIf="selectionMode && (selectedForms.length > 0 || selectedSTMRs.length > 0)" slot="end" (click)="deleteSelectedItems()">Delete</ion-button>
    <ion-button slot="end" (click)="selectFormToDelete()">Select</ion-button> -->

        <ion-buttons slot="end">
        
        
          <ion-button fill="solid" color="primary" class="header-btn" (click)="getForms()">
            <i class="fa-solid fa-rotate-right header-icon" color="white" *ngIf="isIOS"></i>
            <span class="button-text" *ngIf="isElectron">{{ 'Refresh Forms' | translate }}</span>
          </ion-button>
          <ion-button (click)="navigateToReleaseNotes()" *ngIf="newReleaseNoteAvailable > 0" class="icon-with-badge">
            <ion-icon name="chatbox-outline"></ion-icon>
            <ion-badge class="badge">{{ newReleaseNoteAvailable }}</ion-badge>
          </ion-button>
      
          <!-- Inbox text button -->
          <ion-button class="btn-save-lower" (click)="navigateToDataEnhancedForm()" *ngIf="newInboxItemAvailable">
            <span *ngIf="newInboxItemAvailable == 1">
              {{ newInboxItemAvailable }} {{ 'New item in inbox' | translate }}
            </span>
            <span *ngIf="newInboxItemAvailable > 1">
              {{ newInboxItemAvailable }} {{ 'New items in inbox' | translate }}
            </span>
          </ion-button>
        
        
          <ion-button fill="solid" color="primary" class="header-btn"
            *ngIf="selectionMode && (selectedForms.length > 0 || selectedSTMRs.length > 0)" slot="end"
            (click)="deleteSelectedItems()">
            <i class="fa-solid fa-trash" color="white" *ngIf="isIOS"></i>
            <span class="button-text"*ngIf="isElectron">{{ 'Delete' | translate }}</span>
          </ion-button>
        
        
          <ion-button fill="solid" color="primary" class="header-btn" (click)="selectFormToDelete()">
            <i class="fa-solid fa-check" color="white" *ngIf="isIOS"></i>
            <span class="button-text" *ngIf="isElectron">{{ 'Select' | translate }}</span>
          </ion-button>
        </ion-buttons>
  </ion-toolbar>

    <ion-toolbar color="primary" class="custom-toolbar" *ngIf="!formsDisplayService.formSelected">
     <ion-item>
   <ion-searchbar  color="light" placeholder="Filter" [value]="(searchQuery$ | async) ?? ''" (ionInput)="onSearchChange($event.detail.value)" ></ion-searchbar>
   <ion-button fill="clear" (click)="toggleGrid()">
    <ion-icon [name]="gridVisible ? 'chevron-up-outline' : 'chevron-down-outline'"></ion-icon>
  </ion-button>
   </ion-item>

  <ion-item lines="none" *ngIf="gridVisible">
  <ion-grid>
    <ion-row>
      <ion-col>
        <ion-toggle (ionChange)="onHideCompletedToggle($event)">Hide Completed Forms</ion-toggle>
      </ion-col>

      <ion-col >
        <ion-toggle (ionChange)="onCategoryToggle($event)">Category</ion-toggle>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-item>

  
  
    </ion-toolbar>

    
    <ng-container *ngIf="notificationService.dataDownloadPercentage > 0 && notificationService.dataDownloadPercentage < 100">
   
      <div *ngIf="notificationService.dataDownloadPercentage > 0" class="progress-container">
      <p style="font-weight: bold; font-size: 16px; margin-top: 8px; text-align: center; color: black;">  Communicating with server ({{ notificationService.dataDownloadPercentage | number:'1.0-0' }}%)</p>
    <ion-progress-bar [value]="notificationService.dataDownloadPercentage/100" color="primary" style="width: 100%; height: 6px; margin: 0 auto;"></ion-progress-bar>
    <p style="color: red; text-align: center; font-weight: bold;"> Please wait for progress bar to complete before closing the app or switching sites</p>
        </div>
  </ng-container>
</ion-header>

<ion-content *ngIf="!formsDisplayService.formSelected">
    <div ></div>
  <ng-container>
    <!-- If category toggle ON -->

    <ng-container *ngIf="categoryToggle">
      <ng-container *ngIf="groupedForms.length > 0">
        <ion-list *ngFor="let group of groupedForms">
          <ion-item-divider class="group-header">
            <ion-label>
              <div class="group-header-content">
                <span>{{ group.category }}</span>
                <span style="margin-left: 5px; margin-bottom: 2px;">({{ group.items.length }})</span>
              </div>
            </ion-label>
          </ion-item-divider>
          <ion-item *ngFor="let form of group.items" (click)="!selectionMode && onFormSelect(form)" button>
            <ion-checkbox *ngIf="selectionMode" slot="start" (ionChange)="onFormCheckboxChange(form, $event.detail.checked)"></ion-checkbox>
            <ion-thumbnail slot="start" class="thumbnail-tight">
              <div class="icon-wrapper">
                <i class="fas fa-file-alt base-icon"></i>
              </div>
            </ion-thumbnail>
            <ion-label>
              <h2>{{ form.TEMPLATE_DESC }}</h2>
              <p>{{ form.FORM_ID }}</p>
              <p><strong>Created On:</strong> {{ form.CRTD_ON | customDate }}</p>
              <p><strong>Last Updated:</strong> {{ form.LAST_SYNC_TIME | customDate }}</p>
              <p>by {{ form.LAST_SYNC_USER }}</p>
            <div *ngIf="form.SYNC_STATUS == 3 && formErrorMessage?.length">
            <p *ngFor="let msg of formErrorMessage" style="color: red;">
              {{ msg }}
            </p>
            </div>
            </ion-label>
            <ion-buttons slot="end">
            <ion-button *ngIf="form.SYNC_STATUS != 3  &&  form.FORM_STATUS == 'INPR'" color="warning" fill="solid" shape="round"  style="--ion-color-contrast: #fff;">
              {{ 'In Progress' | translate }}
            </ion-button>
            <ion-button *ngIf="form.SYNC_STATUS != 3  &&  form.FORM_STATUS == 'SUBM'" color="primary" fill="solid" shape="round" style="--color: white;">
              {{ 'Completed' | translate }}
            </ion-button>
            <ion-button *ngIf="form.SYNC_STATUS != 3  && form.FORM_STATUS == 'SKIP'" color="medium" fill="solid" shape="round" style="--color: white;">
              {{ 'Skipped' | translate }}
            </ion-button>
            <ion-button *ngIf="form.SYNC_STATUS != 3  &&  form.FORM_STATUS == 'OPEN'" color="medium" fill="solid" shape="round" style="--color: white;">
              {{ 'Created' | translate }}
            </ion-button>
            <ion-button *ngIf="form.SYNC_STATUS != 3  &&  form.FORM_STATUS == 'QUEUED'" color="pending" fill="solid" shape="round" style="--color: white;">
              {{ 'Queued' | translate }}
            </ion-button>

            <ion-button *ngIf="form.SYNC_STATUS != 3  &&  form.FORM_STATUS == 'SENT'" color="primary-green" fill="solid" shape="round" style="--color: white;">
              {{ 'Sent' | translate }}
            </ion-button>
            <ion-button *ngIf="form.SYNC_STATUS == 3 " color="danger" fill="solid" shape="round" style="--color: white;" (click)="getInfoMessages('FORM_HEADER' , form.LID) ; $event.stopPropagation()">
              {{ 'Error' | translate }}
            </ion-button>
            <ion-button *ngIf="form.SYNC_STATUS != 3  &&  form.FORM_STATUS == 'SENDING'" color="pending" fill="solid" shape="round" style="--color: white;">
              {{ 'Sending...' | translate }}
            </ion-button>
            <ion-button *ngIf="form.SYNC_STATUS != 3  && form.FORM_STATUS == 'CAN_BE_SUBM'" color="inpr" fill="solid" shape="round"  style="--ion-color-contrast: #fff;">
              {{ 'In Progress' | translate }}
            </ion-button>
    

          </ion-buttons>
          </ion-item>
        </ion-list>
        <ion-button *ngIf="selectionMode && selectedForms.length > 0" expand="full" color="danger" (click)="deleteSelectedItems()">Delete Selected</ion-button>
        <div *ngIf="groupedForms.length === 0" style="text-align:center; color:gray; margin-top:2em;">
          No forms found.
        </div>
      </ng-container>
    </ng-container>

    <!-- If category toggle OFF -->
     <ng-container>
      
     </ng-container>
    <ng-container *ngIf="!categoryToggle ">
      <ion-list>
                 <ion-item-divider class="group-header">
            <ion-label>
              <div class="group-header-content">
                <span>Forms </span>
                <span style="margin-left: 5px; margin-bottom: 2px;" *ngIf="plainList &&  plainList.length != 0">({{ plainList.length }})</span>
              </div>
            </ion-label>
          </ion-item-divider>
        <ion-item *ngFor="let form of plainList" (click)="!selectionMode && onFormSelect(form)" button>
          <ion-checkbox *ngIf="selectionMode" slot="start" (ionChange)="onFormCheckboxChange(form, $event.detail.checked)"></ion-checkbox>
          <ion-thumbnail slot="start" class="thumbnail-tight">
            <div class="icon-wrapper">
              <i class="fas fa-file-alt base-icon"></i>
            </div>
          </ion-thumbnail>
          <ion-label>
            <h2>{{ form.TEMPLATE_DESC }}</h2>
            <p>{{ form.FORM_ID }}</p>
            <p><strong>Created On:</strong> {{ form.CRTD_ON | customDate }}</p>
            <p><strong>Last Updated:</strong> {{ form.LAST_SYNC_TIME | customDate }}</p>
            <p>by {{ form.LAST_SYNC_USER }}</p>
              <div *ngIf="form.SYNC_STATUS == 3 && formErrorMessage?.length">
            <p *ngFor="let msg of formErrorMessage" style="color: red;">
              {{ msg }}
            </p>
            </div>
          </ion-label>
          <ion-buttons slot="end">
            <ion-button *ngIf="form.SYNC_STATUS != 3  && form.FORM_STATUS == 'INPR'" color="warning" fill="solid" shape="round"  style="--ion-color-contrast: #fff;">
              {{ 'In progress' | translate }}
            </ion-button>
            <ion-button *ngIf="form.SYNC_STATUS != 3  && form.FORM_STATUS == 'SUBM'" color="primary" fill="solid" shape="round" style="--color: white;">
              {{ 'Completed' | translate }}
            </ion-button>
            <ion-button *ngIf="form.SYNC_STATUS != 3  && form.FORM_STATUS == 'SKIP'" color="medium" fill="solid" shape="round" style="--color: white;">
              {{ 'Skipped' | translate }}
            </ion-button>
            <ion-button *ngIf="form.SYNC_STATUS != 3  && form.FORM_STATUS == 'OPEN'" color="medium" fill="solid" shape="round" style="--color: white;">
              {{ 'Created' | translate }}
            </ion-button>
            <ion-button *ngIf="form.SYNC_STATUS != 3  && form.FORM_STATUS == 'QUEUED'" color="pending" fill="solid" shape="round" style="--color: white;">
              {{ 'Queued' | translate }}
            </ion-button>

            <ion-button *ngIf="form.SYNC_STATUS != 3  && form.FORM_STATUS == 'SENT'" color="primary-green" fill="solid" shape="round" style="--color: white;">
              {{ 'Sent' | translate }}
            </ion-button>
            <ion-button *ngIf="form.SYNC_STATUS == 3" color="danger" fill="solid" shape="round" style="--color: white;" (click)="getInfoMessages('FORM_HEADER' , form.LID) ; $event.stopPropagation()">
              {{ 'Error' | translate }}
            </ion-button>
            <ion-button *ngIf="form.SYNC_STATUS != 3  && form.FORM_STATUS == 'SENDING'" color="pending" fill="solid" shape="round" style="--color: white;">
              {{ 'Sending...' | translate }}
            </ion-button>
            <ion-button *ngIf="form.SYNC_STATUS != 3 && form.FORM_STATUS == 'CAN_BE_SUBM'" color="inpr" fill="solid" shape="round"  style="--ion-color-contrast: #fff;">
              {{ 'In Progress' | translate }}
            </ion-button>
    

          </ion-buttons>
        </ion-item>
      </ion-list>
      <div *ngIf="plainList &&  plainList.length === 0" style="text-align:center; color:gray; margin-top:2em;">
        No forms found.
      </div>
    </ng-container>
  </ng-container>


  <ng-container *ngIf="filteredStmrWithTopics as stmrDataFromDb">
  <ion-list>
    <ion-item-divider class="group-header">
      <ion-label>
        <div class="group-header-content">
             <span>STMR </span>
                <span style="margin-left: 5px; margin-bottom: 2px;" *ngIf="stmrDataFromDb &&stmrDataFromDb.length > 0">({{ stmrDataFromDb.length }})</span>
        </div>
      </ion-label>
    </ion-item-divider>

      <ion-item *ngFor="let stmr of stmrDataFromDb" (click)="!selectionMode && onSTMRSelect(stmr)" button>
        <ion-checkbox *ngIf="selectionMode" slot="start" (ionChange)="onSTMRCheckboxChange(stmr, $event.detail.checked)"></ion-checkbox>
        <ion-thumbnail slot="start" class="thumbnail-tight">
          <div class="icon-wrapper">
            <i class="fas fa-file-alt base-icon"></i>
          </div>
        </ion-thumbnail>
        <ion-label>
          <h2>{{ displayStmrId(stmr) }}</h2>
          <ng-container *ngFor="let topic of stmr.topics">
            <p>{{ topic.TOPIC_NAME }}</p>
            <p>{{ topic.TOPIC_NOTE }}</p>
          </ng-container>
          <p><strong>Created On:</strong> {{ stmr.CRTD_ON | customDate }}</p>
          <p><strong>Last Updated:</strong> {{ stmr.LAST_SYNC_TIME | customDate }}</p>
          <p>by {{ stmr.LAST_SYNC_USER }}</p>
          <div *ngIf="stmr.SYNC_STATUS == 3 && stmrErrorMessage?.length">
          <p  *ngFor="let msg of stmrErrorMessage" style="color: red;">
            {{ msg }}
          </p>
         </div>
        </ion-label>  
       
        <ion-buttons slot="end">
          <ion-button *ngIf="stmr.SYNC_STATUS != 3 && stmr.STMR_STATUS == 'INPR'" color="warning" fill="solid" shape="round"  style="--ion-color-contrast: #fff;">
            {{ 'In progress' | translate }}
          </ion-button>
          <ion-button *ngIf="stmr.SYNC_STATUS != 3 && stmr.STMR_STATUS == 'SUBM'" color="primary" fill="solid" shape="round" style="--color: white;">
            {{ 'Completed' | translate }}
          </ion-button>
          <ion-button *ngIf="stmr.SYNC_STATUS != 3 && stmr.STMR_STATUS == 'SKIP'" color="medium" fill="solid" shape="round" style="--color: white;">
            {{ 'Skipped' | translate }}
          </ion-button>
          <ion-button *ngIf="stmr.SYNC_STATUS != 3 && stmr.STMR_STATUS == 'OPEN'" color="medium" fill="solid" shape="round" style="--color: white;">
            {{ 'Created' | translate }}
          </ion-button>
          <ion-button *ngIf="stmr.SYNC_STATUS != 3 && stmr.STMR_STATUS == 'QUEUED'" color="pending" fill="solid" shape="round" style="--color: white;">
            {{ 'Queued' | translate }}
          </ion-button>

          <ion-button *ngIf="stmr.SYNC_STATUS != 3 && stmr.STMR_STATUS == 'SENT'" color="primary-green" fill="solid" shape="round" style="--color: white;">
            {{ 'Sent' | translate }}
          </ion-button>
          <ion-button *ngIf="stmr.SYNC_STATUS == 3" color="danger" fill="solid" shape="round" style="--color: white;" (click)="getInfoMessages('STMR_HEADER' , stmr.LID) ; $event.stopPropagation()">
            {{ 'Error' | translate }}
          </ion-button>
          <ion-button *ngIf="stmr.SYNC_STATUS != 3 && stmr.STMR_STATUS == 'SENDING'" color="pending" fill="solid" shape="round" style="--color: white;">
            {{ 'Sending...' | translate }}
          </ion-button>
          <ion-button *ngIf="stmr.SYNC_STATUS != 3 &&stmr.STMR_STATUS == 'CAN_BE_SUBM'" color="inpr" fill="solid" shape="round"  style="--ion-color-contrast: #fff;">
            {{ 'In Progress' | translate }}
          </ion-button>
  

        </ion-buttons> 
      </ion-item>
    </ion-list>
    <!-- <ion-button *ngIf="selectionMode && selectedSTMRs.length > 0" expand="full" color="danger" (click)="deleteSelectedItems()">Delete Selected</ion-button> -->
    <div *ngIf="stmrDataFromDb?.length === 0" style="text-align:center; color:gray; margin-top:2em;">
      No STMR found.
    </div>
  </ng-container>
</ion-content>
