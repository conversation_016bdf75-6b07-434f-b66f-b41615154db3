import { Injectable } from '@angular/core';
import { AlertService } from './alert.service';
import { AppConstants } from '../constants/appConstants';
import { STMR_ACTION } from 'src/models/STMR_ACTION';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';

@Injectable({
  providedIn: 'root'
})
export class SubmitSTMRService {

  constructor(
    private alertService: AlertService,
    private unviredSDK: UnviredCordovaSDK
  ) {}

  /**
   * Inserts a COMPLETE action into STMR_ACTION table for each form under the provided STMR header.
   */
  async insertFormAction(
    stmrHeader: any,
    stmrForms: any[]
  ): Promise<void> {
    const totalForms = stmrForms?.length || 0;
    if (totalForms === 0) {
      return; // Nothing to insert
    }

    const copyStmrForms = JSON.parse(JSON.stringify(stmrForms));

    for (const form of copyStmrForms) {
      const stmrActionObj: STMR_ACTION = {
        STMR_ID: stmrHeader.STMR_ID,
        FORM_ID: form.FORM_ID,
        ACTION_CODE: AppConstants.ACTION_CODE.COMPLETE,
        P_MODE: form.P_MODE,
        FID: stmrHeader.LID,
        OBJECT_STATUS: AppConstants.OBJECT_STATUS.ADD
      } as STMR_ACTION;

      await this.actualDbInsert(stmrActionObj);
    }
  }

  /**
   * Inserts one record into STMR_ACTION table.
   */
  private async actualDbInsert(stmrActionObj: STMR_ACTION): Promise<void> {
    try {
      const result = await this.unviredSDK.dbInsertOrUpdate(
        AppConstants.TABLE_STMR_ACTION,
        stmrActionObj,
        AppConstants.BOOL_FALSE
      );

      if (result.type === ResultType.success) {
        console.log('[SubmitSTMRService] STMR ACTION inserted successfully.');
      } else {
        console.error('[SubmitSTMRService] Failed to insert STMR ACTION.');
        this.alertService.showAlert(
          'Error',
          'STMR Action could not be created! Aborting STMR submission.'
        );
      }
    } catch (error) {
      console.error('[SubmitSTMRService] Insert error:', error);
      this.alertService.showAlert(
        'Error',
        'An unexpected error occurred while inserting STMR Action.'
      );
    }
  }

  /**
   * Returns STMR FORMS based on STMR_ID (and optional topic number).
   */
  async returnSTMRForms(
    stmrId: string,
    topicNo?: string
  ): Promise<any[] | undefined> {
    const whereClause: any = { STMR_ID: stmrId || '' };
    if (topicNo) {
      whereClause.TOPIC_NO = topicNo;
    }

    try {
      const result = await this.unviredSDK.dbSelect(
        AppConstants.TABLE_STMR_FORM,
        whereClause
      );

      if (result.type === ResultType.success) {
        if (result.data && result.data.length > 0) {
          console.log('[SubmitSTMRService] Returning STMR FORMS for', stmrId);
          return result.data;
        } else {
          console.log('[SubmitSTMRService] No STMR FORMS found for', stmrId);
          return undefined;
        }
      } else {
        console.error(
          '[SubmitSTMRService] Error fetching STMR FORMS:',
          result.message || result.error
        );
        return undefined;
      }
    } catch (error) {
      console.error(
        '[SubmitSTMRService] Exception fetching STMR FORMS:',
        error
      );
      return undefined;
    }
  }
}
