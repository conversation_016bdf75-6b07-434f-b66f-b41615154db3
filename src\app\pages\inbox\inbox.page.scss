.custom-toolbar {
  --background: var(--ion-color-primary);
  --color: white;
}

.white-icon {
  --color: white;
  --background: transparent;
  
  &:hover {
    --background: rgba(255, 255, 255, 0.1);
  }
}

.left-title {
  text-align: left;
  font-weight: bold;
}

// Generate JSON button specific styling
ion-button[slot="end"] {
  --color: white;
  --background: transparent;
  --border-color: rgba(255, 255, 255, 0.3);
  --border-width: 1px;
  --border-style: solid;
  --border-radius: 20px;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  
  &:hover {
    --background: rgba(255, 255, 255, 0.1);
    --border-color: rgba(255, 255, 255, 0.5);
  }
  
  &:active {
    --background: rgba(255, 255, 255, 0.2);
  }
}
