import { Inject, Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Platform } from '@ionic/angular';
import * as uuid from 'uuid';
import { BehaviorSubject, from, Observable } from 'rxjs';
import { File } from '@awesome-cordova-plugins/file/ngx'
import { Store } from '@ngrx/store';
import { firstValueFrom } from 'rxjs';
import { AppState } from '../store/app.state';
import { selectRigData, selectTemplatesLoadedFromDb, selectAllTemplates } from '../store/store.selector';
import { ResultType, UnviredCordovaSDK, RequestType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx'; 
import { AppConstants } from '../constants/appConstants';
import { SETTING_HEADER } from 'src/models/SETTING_HEADER';
import moment from 'moment-timezone';
import { TOPIC_HEADER } from 'src/models/TOPIC_HEADER';
import { CTA_HEADER } from 'src/models/CTA_HEADER';
import { HSE_STANDARD_HEADER } from 'src/models/HSE_STANDARD_HEADER';
import { TMPLT_VER } from 'src/models/TMPLT_VER';
import { unzipSync } from 'fflate';
import { AlertService } from './alert.service';

declare var cordova: any;

@Injectable({
  providedIn: 'root'
})
export class UtilityService {
   
  private currentRunModeValue?: String;
  private langSubject = new BehaviorSubject<string>(localStorage.getItem('lang') || 'en');
  lang$ = this.langSubject.asObservable();
    currentScreenTitle: string = '';

  constructor(
    private translate: TranslateService,
    private unviredSdk: UnviredCordovaSDK,
    private store: Store<AppState>,
    private file: File,
    private alertService: AlertService,
    private platform: Platform) {
    this.setLanguage(this.langSubject.value); // initialize
  }

  changeLanguage(lang: string) {
    localStorage.setItem('lang', lang);
    this.setLanguage(lang);
    this.langSubject.next(lang);
  }

  private setLanguage(lang: string) {
    this.translate.use(lang);
    document.dir = lang === 'ar' ? 'rtl' : 'ltr';
  }

  getCurrentLang(): Observable<string> {
    return this.lang$;
  }

callGetMessages(): Observable<any> {
  return new Observable((observer) => {
    try {
      const response = this.unviredSdk.getMessages();
      observer.next(response);
      observer.complete();
    } catch (error) {
      observer.error(error);
    }
  });
}
public guid32() {
		let guid = uuid.v4();
		let guid32 = guid.replace(/-/g, "");
		return guid32;
	}

public returnDisplayDate(time: any, fromFormat?: string, toFormat?: string): string {
  try {
    if (!time) return '';
    toFormat = toFormat || 'MMM DD YYYY';
    // Support Unix seconds explicitly when fromFormat === 'X'
    if (fromFormat === 'X') {
      return moment.unix(Number(time) || 0).local().format(toFormat);
    }
    if (fromFormat) {
      return moment.utc(time, fromFormat).local().format(toFormat);
    }
    return moment.utc(time).local().format(toFormat);
  } catch (e) {
    this.unviredSdk.logError("utilityService", "returnDisplayDate", "Invalid Date format");
    return '';
  }

}
getTimeZone(){
const timezone = moment.tz.guess();
return timezone
}
/**
   * Returns new STMR ID in format: 'STMRYYYYMMDDRIGHHmmss000001'
   * @param currIndex Number of current STMRs (for incrementing index)
   * @param rigNo Rig number to embed in ID
   */
  public genStmrId(currIndex: number = 0, rigNo: number | string = 0): string {
    const time = moment();

    const datePart = time.format('YYYYMMDD');
    const timePart = time.format('HHmmss');
    const paddedRigNo = this.addPositiveZeros(rigNo.toString(), 3);
    const paddedIndex = this.addPositiveZeros((currIndex + 1).toString(), 6);

    return `STMR${datePart}${paddedRigNo}${timePart}${paddedIndex}`;
  }

  /**
   * Pads a number with leading zeros to reach the desired length.
   * @param value Number or string to pad
   * @param length Desired total length after padding
   */
  public addPositiveZeros(value: string | number, length: number): string {
    return value.toString().padStart(length, '0');
  }

  private isCompanyLogoSet: boolean = AppConstants.BOOL_FALSE;

  /**
   * Returns Promise<boolean> indicating whether company logo is set.
   */
  public checkCompanyLogoStatus(): Promise<boolean> {
    return Promise.resolve(this.isCompanyLogoSet);
  }
  /**
   * Returns true if Rig data and Templates have been fully loaded from DB.
   */
  public async checkIfAllInitDataDownloaded(): Promise<boolean> {
    const rigLoaded = !!(await firstValueFrom(this.store.select(selectRigData)));
    const rigData = await firstValueFrom(this.store.select(selectRigData));
    const templatesLoaded = !!(await firstValueFrom(this.store.select(selectTemplatesLoadedFromDb)));
    const templates = await firstValueFrom(this.store.select(selectAllTemplates));

    const isValidRig = rigLoaded && !!rigData?.RIG_NO;
    const isValidTemplates = templatesLoaded && Array.isArray(templates) && templates.length > 0;

    return isValidRig && isValidTemplates;
  }

/**
 * Returns Status description object based on sync status
 * @param syncStatus sync status code (0: NONE, 1: QUEUED, 2: SENT, 3: ERROR)
 */
getSyncStatusObj(syncStatus?: number): { descr?: string; color?: string } {
  let statusDescObj: { descr?: string; color?: string } = {};
  // SYNC_STATUS can be NONE: 0, QUEUED: 1, SENT: 2, ERROR: 3
  // If NONE: 0 do not display send {}
  switch (syncStatus) {
    case 1:
      statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.QUEUED;
      break;
    case 2:
      statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.SENT;
      break;
    case 3:
      statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.ERROR;
      break;
    default:
      break;
  }
  return statusDescObj;
}

  // Helpers used by TopicsPage
  public sortArray(array: any, sortKey: string) {
		if (array && array.length && sortKey) {
			array = array.sort((oldVal: any, newVal: any): number => {
				if (!oldVal[sortKey] || !newVal[sortKey]) {
					return 0
				}
				if (oldVal[sortKey].toLowerCase() > newVal[sortKey].toLowerCase()) return 1;
				if (oldVal[sortKey].toLowerCase() < newVal[sortKey].toLowerCase()) return -1;
				return 0;
			})
			return array;
		} else return [];
	}

  public getTopicName(topics: TOPIC_HEADER[], topicId: string) {
		for (let i = 0, iLen = topics.length; i < iLen; i++) {
			if (topics[i].TOPIC_ID == topicId)
				return topics[i].TOPIC_NAME;
		}
		return "";
	}

  public getCTADesc(ctas: CTA_HEADER[], ctaId: string) {
		for (let i = 0, iLen = ctas.length; i < iLen; i++) {
			if (ctas[i].CTA_ID == ctaId)
				return ctas[i].DESCR;
		}
		return "";
	}

  public getHSEDesc(hses: HSE_STANDARD_HEADER[], ctaId: string) {
		for (let i = 0, iLen = hses.length; i < iLen; i++) {
			if (hses[i].STD_ID == ctaId)
				return hses[i].DESCR;
		}
		return "";
	}

  public getReleaseVersionDet(
  tmpltVer: any[],
  keys: string[]
): { [key: string]: any } {
  const result: { [key: string]: any } = {};

  if (keys.length > 0 && tmpltVer?.length) {
    // Find the first template with REL status
    const releasedVer = tmpltVer.find(
      ver => ver?.STATUS === AppConstants.VAL_TMPLT_STATUS.REL
    );

    if (releasedVer) {
      for (const key of keys) {
        result[key] = releasedVer[key];
      }
    }
  }

  return result;
}




  /**
   * Get local timezone identifier
   */
  public getTimezone(): string {
		let timezone = moment.tz.guess()
		return timezone
	}

 public async getCTALocation(): Promise<string> {
  try {
    const result = await this.unviredSdk.dbSelect(
      AppConstants.TABLE_SETTING_HEADER,
      { NAME: AppConstants.SETTING_CTA_LOCAL_PATH }
    );

    if (result.data && result.data.length > 0) {
      let settingsHeader: SETTING_HEADER = result.data[0];
      settingsHeader.VALUE = settingsHeader.VALUE.replace(/\\\\/g, '\\');
      return settingsHeader.VALUE;
    } else {
      this.unviredSdk.logError(
        "Utility",
        "getCTALocation",
        `Result Set is empty for Setting: ${AppConstants.SETTING_CTA_LOCAL_PATH}`
      );
      throw new Error(
        `Result Set is empty for Setting: ${AppConstants.SETTING_CTA_LOCAL_PATH}`
      );
    }
  } catch (error: any) {
    this.unviredSdk.logError(
      "Utility",
      "getCTALocation",
      `Error While retrieving CTA Local Path: ${error}`
    );
    throw new Error(`Error While retrieving CTA Local Path: ${error}`);
  }
}


  public async getHSELocation(): Promise<string> {
  try {
    const result = await this.unviredSdk.dbSelect(
      AppConstants.TABLE_SETTING_HEADER,
      { NAME: AppConstants.SETTING_HSE_LOCAL_PATH }
    );

    if (result.data && result.data.length > 0) {
      let settingsHeader: SETTING_HEADER = result.data[0];
      settingsHeader.VALUE = settingsHeader.VALUE.replace(/\\\\/g, '\\');
      return settingsHeader.VALUE;
    } else {
      this.unviredSdk.logError(
        "Utility",
        "getHSELocation",
        `Result Set is empty for Setting: ${AppConstants.SETTING_HSE_LOCAL_PATH}`
      );
      throw new Error(
        `Result Set is empty for Setting: ${AppConstants.SETTING_HSE_LOCAL_PATH}`
      );
    }
  } catch (error: any) {
    this.unviredSdk.logError(
      "Utility",
      "getHSELocation",
      `Error While retrieving HSE Local Path: ${error}`
    );
    throw new Error(`Error While retrieving HSE Local Path: ${error}`);
  }
}


  /**
   * Returns Status description object based on form status
   */
  public getFormStatusObj(status: any) {
    let statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.OPEN;
    switch (status) {
      case AppConstants.VAL_FORM_STATUS.OPEN:
      default:
        statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.OPEN;
        break;
      case AppConstants.VAL_FORM_STATUS.INPR:
        statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.INPR;
        break;
      case AppConstants.VAL_FORM_STATUS.SUBM:
        statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.SUBM;
        break;
      case AppConstants.VAL_FORM_STATUS.CAN_BE_SUBM:
        statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.CAN_BE_SUBM;
        break;
      case AppConstants.VAL_FORM_STATUS.SKIP:
        statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.SKIP;
        break;
    }
    return statusDescObj;
  }

  /**
   * Check if app is running in test automation mode (cached)
   */
  public async isAppRunningInTestAutomationMode(): Promise<boolean> {
  try {
    // Return immediately if we already know the value
    if (this.currentRunModeValue === AppConstants.SETTING_MODE_TEST_AUTOMATION) {
      return true;
    } else if (this.currentRunModeValue === AppConstants.SETTING_MODE_PRODUCTION_RUN) {
      return false;
    }

    // If undefined, fetch from DB
    const result = await this.unviredSdk.dbSelect(
      AppConstants.TABLE_SETTING_HEADER,
      { NAME: AppConstants.SETTING_RUN_MODE }
    );

    if (result.data && result.data.length > 0) {
      const settingsHeader: SETTING_HEADER = result.data[0];
      this.currentRunModeValue = settingsHeader.VALUE;
    } else {
      this.unviredSdk.logInfo(
        "Utility",
        "getRunMode",
        `Result Set is empty for Setting: ${AppConstants.SETTING_RUN_MODE}`
      );
      // Default to production run if not set
      this.currentRunModeValue = AppConstants.SETTING_MODE_PRODUCTION_RUN;
    }

    return this.currentRunModeValue === AppConstants.SETTING_MODE_TEST_AUTOMATION;
  } catch (error: any) {
    this.unviredSdk.logError(
      "Utility",
      "getRunMode",
      `Error While retrieving Run Mode: ${error}`
    );
    throw new Error(`Error While retrieving Run Mode: ${error}`);
  }
}


  /**
   * Delete persisted app state file, if present
   */
  public async deleteAppState(): Promise<void> {
    try {
      const basePath: string = this.file.dataDirectory;
      await this.file.removeFile(basePath, 'lastSavedForm.json');
    } catch {
      // ignore
    }
  }

  /**
   * Insert or update crew members list in database
   */
public async insertOrUpdateCrewList(crewList: any[]): Promise<void> {
  return new Promise((resolve, reject) => {
    console.log('[UtilityService] insertOrUpdateCrewList called with:', crewList.length, 'crew members');
    if (crewList.length === 0) {
      return resolve();
    }    
    const crewHeader = crewList[0];
    console.log(`[UtilityService] Processing crew member:`, crewHeader.USER_NAME);
    this.unviredSdk.dbInsertOrUpdate(
      AppConstants.TABLE_CREW_HEADER, 
      crewHeader, 
      true        
    ).then(result => {
      if (result.type === ResultType.success) {
        this.unviredSdk.logInfo(
          'UtilityService', 
          'insertOrUpdateCrewList', 
          `Inserted Crew Header: ${crewHeader.USER_NAME}`
        );
        crewList.splice(0, 1);
        return resolve(this.insertOrUpdateCrewList(crewList));
      } else {
        this.unviredSdk.logError(
          'UtilityService', 
          'insertOrUpdateCrewList', 
          `Error while Inserting Crew Header: ${JSON.stringify(crewHeader, null, 2)} Error: ${JSON.stringify(result, null, 2)}`
        );
        this.alertService.showAlert("Error", JSON.stringify(result.error));
        crewList.splice(0, 1);
        return resolve(this.insertOrUpdateCrewList(crewList));
      }
    }).catch(error => {
      console.error('[UtilityService] Error:', error);
      reject(error);
    });
  });
}


  private ensureArrayBuffer(buffer: ArrayBufferLike): ArrayBuffer {
  return buffer as ArrayBuffer;
}

public getAttachmentAsArrayBuffer(path: string): Promise<ArrayBuffer> {
  console.log('getAttachmentAsArrayBuffer called with path: ', path);
    return new Promise(async (resolve, reject) => {
      const directory = path.substring(0, path.lastIndexOf('/'));
      const filename = path.substring(path.lastIndexOf('/') + 1);
      try {
        const buffer = await this.file.readAsArrayBuffer(directory, filename);
        resolve(buffer);
      } catch (error) {
        this.unviredSdk.logError("UtilityService", "getAttachment", `Error reading file from ${path}: ${JSON.stringify(error)}`);
        reject(error);
      }
    });
  }


  async unzipTheFileAndWriteFileContent(attachmentAsArrayBufferReceived: ArrayBuffer, folderName: string, pathResult: string) {
    console.log('unzipTheFileAndWriteFileContent called with folderName: ', folderName);
  try {

    const exists = await this.checkDirectoryExists(pathResult, folderName);
    console.log('Folder exists before unzip?', exists);
    if (!exists) {
      await this.createDirectoryIfNeeded(pathResult, folderName);
    }

    // Convert the received buffer to a safe ArrayBuffer
    const safeBuffer = this.ensureArrayBuffer(attachmentAsArrayBufferReceived);
    const uint8 = new Uint8Array(safeBuffer);
    
    // Rest of your unzip logic
    const files = unzipSync(uint8);
    
    for (const [filename, content] of Object.entries(files)) {
      // Skip directory entries
      if (filename.endsWith('/')) continue;

      // Ensure parent directories exist for nested files
      const lastSlash = filename.lastIndexOf('/');
      if (lastSlash > -1) {
        const parentDir = filename.substring(0, lastSlash);
        try {
          await this.file.createDir(pathResult + '/' + folderName, parentDir, true);
        } catch (dirErr) {
          // Ignore if already exists
        }
      }

      // Convert the content buffer before writing
      const safeContentBuffer = this.ensureArrayBuffer(content.buffer);
      
      // Write file
      try {
        await this.file.writeFile(pathResult + '/' + folderName, filename, safeContentBuffer, { replace: true });
        console.log(`File written: ${filename}`);
        this.unviredSdk.logInfo('UtilityService', 'unzipTheFileAndWriteFile', `File '${filename}' written in directory '${folderName}'.`);
      } catch (writeError) {
        this.unviredSdk.logError('UtilityService', 'unzipTheFileAndWriteFile', `Error writing file '${filename}'`);
      }
    }
  } catch (error) {
    this.unviredSdk.logError('UtilityService', 'unzipTheFileAndWriteFile', `Error processing file: ${error}`);
    throw error;
  }
}

 private async createDirectoryIfNeeded(attachmentPath: string, folderName: string): Promise<void> {
      try {
        // First try to detect if we're in Electron environment
        if ((window as any).require) {
          try {
            // Use Node.js fs for Electron
            const fs = (window as any).require('fs');
            const path = (window as any).require('path');
            const fullPath = path.join(attachmentPath, folderName);

            if (!fs.existsSync(fullPath)) {
              fs.mkdirSync(fullPath, { recursive: true });
              console.log(`Electron: Directory created: ${fullPath}`);
            } else {
              console.log(`Electron: Directory already exists: ${fullPath}`);
            }
            return;
          } catch (electronError) {
            console.log('Electron directory creation failed, falling back to Cordova:', electronError);
          }
        }

        // Fallback to Cordova File plugin for mobile platforms or if Electron fails
        try {
          await this.file.createDir(attachmentPath, folderName, false);
          console.log(`Cordova: Directory created: ${attachmentPath}${folderName}`);
        } catch (cordovaError: any) {
          console.log('Cordova directory creation failed:', cordovaError);
          // Don't throw here, as the directory might already exist
          // The error code 12 typically means "directory already exists"
          if (cordovaError.code !== 12) {
            throw cordovaError;
          } else {
            console.log('Directory likely already exists (error code 12)');
          }
        }
      } catch (error) {
        console.error('Error creating directory:', error);
        throw error;
      }
    }

     private async checkDirectoryExists(attachmentPath: string, folderName: string): Promise<boolean> {
      try {
        // First try to detect if we're in Electron environment
        if ((window as any).require) {
          try {
            // Use Node.js fs for Electron
            const fs = (window as any).require('fs');
            const path = (window as any).require('path');
            const fullPath = path.join(attachmentPath, folderName);
            const exists = fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory();
            console.log(`Electron directory check: ${fullPath} exists: ${exists}`);
            return exists;
          } catch (electronError) {
            console.log('Electron fs check failed, falling back to Cordova:', electronError);
          }
        }

        // Fallback to Cordova File plugin for mobile platforms or if Electron fails
        try {
          await this.file.checkDir(attachmentPath, folderName);
          return true;
        } catch (cordovaError: any) {
          // If Cordova also fails, try a different approach
          console.log('Cordova directory check failed:', cordovaError);

          // Try to list the parent directory to see if our folder exists
          try {
            const entries = await this.file.listDir(attachmentPath, '');
            const folderExists = entries.some(entry => entry.name === folderName && entry.isDirectory);
            console.log(`Directory listing check: ${folderName} exists: ${folderExists}`);
            return folderExists;
          } catch (listError) {
            console.log('Directory listing also failed:', listError);
            return false;
          }
        }
      } catch (error) {
        console.log('All directory check methods failed:', error);
        return false;
      }
    }

/**
 * Gets the complete document path for CTA or HSE files
 * @param docType - Type of document ('CTA' or 'HSE')
 * @param relativePath - The relative path within the document library
 * @returns Promise<string> - Full path to the document
 */
public async getCompleteDocPath(isCTA: boolean, ctaHseId: string, docId: string): Promise<string> {
  try {
    // Get ACTUAL_RIG_DOC_PATH from settings
    const relativePath = await this.getDocumentFileName(isCTA, ctaHseId, docId);
    if (!relativePath) {
      throw new Error('Relative path is empty');
    }
    const settingsResult = await this.unviredSdk.dbSelect(
      AppConstants.TABLE_SETTING_HEADER,
      { NAME: AppConstants.SETTING_ACTUAL_RIG_DOC_PATH }
    );
    
    if (!settingsResult?.data?.[0]?.VALUE) {
      throw new Error('ACTUAL_RIG_DOC_PATH not found in settings');
    }
    const actualRigDocPath = settingsResult.data[0].VALUE;

    // Get SP_LOCAL_ROOT from config
    const spConfigResult = await this.unviredSdk.dbSelect(
      AppConstants.TABLE_SP_CONFIG_HEADER,
      {NAME: AppConstants.SP_LOCAL_ROOT}
    );
    
    if (!spConfigResult?.data?.[0]?.VALUE) {
      throw new Error('SP_LOCAL_ROOT not found in config');
    }
    const spLocalRoot = spConfigResult.data[0].VALUE;
    console.log('SP_LOCAL_ROOT:', spLocalRoot);

    // Get document library location based on type
    const localPath = isCTA 
      ? await this.getCTALocation()
      : await this.getHSELocation();

    console.log('Local path:', localPath);

    // Calculate document library path relative to SP root
    const documentLibrary = localPath.replace(spLocalRoot, '');
    console.log('Actual rig doc path:', actualRigDocPath);
    console.log('Document library:', documentLibrary);
    console.log('Relative path:', relativePath);

    // Build the complete path
    let fullPath = this.joinPaths(
      actualRigDocPath,
      documentLibrary,
      relativePath
    );

    // Normalize path separators for the platform
    fullPath = this.normalizePath(fullPath);

    this.unviredSdk.logInfo(
      'UtilityService',
      'getDocumentPath',
      `Generated path: ${fullPath}`
    );

    return fullPath;

  } catch (error) {
    this.unviredSdk.logError(
      'UtilityService',
      'getDocumentPath',
      `Error generating document path: ${error}`
    );
    throw error;
  }
}

/**
 * Joins path segments, handling different separators
 */
private joinPaths(...paths: string[]): string {
  return paths
    .map(path => path.replace(/^[\/\\]+|[\/\\]+$/g, '')) // Remove leading/trailing slashes
    .filter(Boolean) // Remove empty segments
    .join('/');
}

/**
 * Normalizes path separators for the current platform
 */
private normalizePath(path: string): string {
  if ((window as any).require) {
    // Electron/Windows path
    if (/^[A-Za-z]:\\/.test(path)) {
      return path
        .replace(/\//g, '\\') // Convert forward slashes to backslashes
        .replace(/\\+/g, '\\'); // Remove duplicate backslashes
    }
  }

  // iOS/Unix path handling
  let normalizedPath = path
    .replace(/\\/g, '/') // Convert backslashes to forward slashes
    .replace(/\/+/g, '/') // Remove duplicate forward slashes
    .replace(/^\/private\/var\/mobile\//, '') // Remove iOS private path prefix if present
    .replace(/^\/var\/mobile\//, ''); // Remove iOS path prefix if present

  // Handle iOS file protocol
  if (cordova.platformId == 'ios') {
    // If path doesn't start with file:// and isn't a relative path, add file://
    if (!normalizedPath.startsWith('file:///')) {
      normalizedPath = normalizedPath.replace('file:/', '/');
      normalizedPath = 'file://' + normalizedPath;
    }
  }

  return normalizedPath;
}

/**
 * Gets the FILE_NAME from CTA_DOC or HSE_DOC table based on document type
 * @param isCTA - True for CTA documents, False for HSE documents
 * @param docId - CTA_ID for CTA docs or STD_ID for HSE docs
 * @returns Promise<string> - The FILE_NAME from the database
 */
public async getDocumentFileName(isCTA: boolean, ctaHseId: string, docId: string): Promise<string> {
  try {
    let query: string;
    if (isCTA) {
      query = `SELECT FILE_NAME FROM CTA_DOC WHERE CTA_ID = '${ctaHseId}' AND DOC_ID = '${docId}'`;
    } else {
      query = `SELECT FILE_NAME FROM HSE_STANDARD_DOC WHERE STD_ID = '${ctaHseId}' AND DOC_ID = '${docId}'`;
    }

    const result = await this.unviredSdk.dbExecuteStatement(query);

    if (result.type !== ResultType.success || !result.data?.[0]) {
      throw new Error(`No document found with ID: ${docId}`);
    }

    const fileName = result.data[0].FILE_NAME;
    
    this.unviredSdk.logInfo(
      'UtilityService',
      'getDocumentFileName',
      `Retrieved filename: ${fileName}`
    );

    return fileName;

  } catch (error) {
    this.unviredSdk.logError(
      'UtilityService',
      'getDocumentFileName',
      `Error getting filename: ${error}`
    );
    throw error;
  }
}


// Reusable PA call for form/STMR display URL
public async getFormDisplayUrl(formId: string, versionId: string = ''): Promise<string> {
  if (!formId || !formId.trim()) {
    throw new Error('Invalid FORM_ID'); // [2]
  }
  let inputFormContext: any;

  if (versionId && versionId.trim()) {
    inputFormContext = { FORM_ID: formId, VER_ID: versionId }; // [2]
  } else {
    inputFormContext = { FORM_ID: formId }; // [2]
  }

  const inputObject = {
    INPUT_FORM_CONTEXT: [{ INPUT_FORM_CONTEXT_HEADER: inputFormContext }],
  }; // [2]
  const result: any = await this.unviredSdk.syncForeground(
    RequestType.PULL as any,
    '',
    inputObject,
    AppConstants.PA_FORM_DISPLAY,
    false
  ); // [2]
  if (result?.type === ResultType.success && result?.data) {
    const url = typeof result.data === 'string' ? result.data : (result.data.url || ''); // [2]
    if (!url) throw new Error('Please sync the Form/STMR before printing'); // [2]
    return url; // [2]
  }
  throw new Error(result?.message || 'Unable to generate print URL'); // [2]
}
}