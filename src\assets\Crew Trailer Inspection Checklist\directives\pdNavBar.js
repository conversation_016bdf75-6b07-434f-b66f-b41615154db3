angular.module('PDTest')
.directive('pdNavBar', function() {
    var html='    <nav class="navbar navbar-default navbar-fixed-top" id="mainnav">';
    html +='        <div class="navbar-back">';
    html +='            <span ng-click="back()" class="btn-clear"><svg class="icon icon-ion-chevron-left"><use xlink:href="#icon-ion-chevron-left"></use></svg></span>';
    html +='        </div>';
    html +='        <div class="navbar-title hidden-xs">';
    html +='            <strong>{{form.title}} </strong>';
    html +='        </div>';
    html +='        <div class="pull-right">';
    html +='            <div class="btn-group">';
    html +='                <button type="button" class="btn btn-outline navbar-btn dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">';
    html +='                    <svg class="icon icon-ion-gear-a"><use xlink:href="#icon-ion-gear-a"></use></svg>';
    html +='                </button>';
    html +='                <ul class="dropdown-menu dropdown-menu-right">';
    html +='                    <li><a href="javascript:void(0);" ng-click="print()">Print</a></li>';
    html +='                    <li ng-if="sections.length !==0" role="separator" class="divider"></li>';
    html +='                    <li><a href="javascript:void(0);"';
    html +='                        ng-repeat="item in sections"';
    html +='                        ng-click="gotoSection(item)">{{item.name}}</a></li>';
    html +='                </ul>';
    html +='            </div>';
    html +='            <button class="btn btn-default navbar-btn"';
    html +='                ng-hide="submitted"';
    html +='                ng-click="save(true)"';
    html +='                ng-disabled="!contentForm.$dirty">';
    html +='                <svg class="icon icon-ion-chevron-left"><use xlink:href="#icon-ion-chevron-left"></use></svg>';
    html +='                <span class="hidden-xs">Save and Exit</span>';
    html +='            </button>';
    html +='            <button class="btn btn-default navbar-btn"';
    html +='                ng-hide="submitted"';
    html +='                ng-click="save(false)"';
    html +='                ng-disabled="!contentForm.$dirty">';
    html +='                <svg class="icon icon-ion-checkmark"><use xlink:href="#icon-ion-checkmark"></use></svg>';
    html +='                <span class="hidden-xs">Save</span>';
    html +='            </button>';
    html +='            <button class="btn btn-danger navbar-btn"';
    html +='                ng-show="isPackage && !submitted"';
    html +='                ng-click="skip()"';
    html +='                ng-disabled="data.CanBeSubmitted">';
    html +='                <svg class="icon icon-ion-android-remove-circle"><use xlink:href="#icon-ion-android-remove-circle"></use></svg>';
    html +='                <span class="hidden-xs">Skip</span>';
    html +='            </button>';
    html +='            <button class="btn btn-warning navbar-btn"';
    html +='                ng-show="allowSubmit && !submitted"';
    html +='                ng-click="submit()"';
    html +='                ng-disabled="!data.CanBeSubmitted">';
    html +='                <svg class="icon icon-ion-ios-paperplane"><use xlink:href="#icon-ion-ios-paperplane"></use></svg>';
    html +='                <span class="hidden-xs">Send</span>';
    html +='            </button>';
    html +='        </div>';
    html +='    </nav>';

    return {
        restrict: 'EA',
        template: html
    };

});