// Container application communication proxy
function setStatus(status) {
    try{
        angular.element(document.getElementById('formApp')).scope().updateStatus(status);
    }
    catch(err) {
        alert('Error in setStatus: ' + err.message);
    }
}

function loadDefaultValues(lists) {
    // we expect the lists object to be 
    // passed in from the container app
    // This object contains data from
    // - SAP/DAR
    // - Current environment
    // - Company logo as base64
    try{
        angular.element(document.getElementById('formApp')).scope().loadValues(lists);
    }
    catch(err) {
        alert('Error in loadDefaultValues: ' + err.message);
    }
}

function loadPreviousValues(data) {
    // we expect a forms data json object
    try{
        angular.element(document.getElementById('formApp')).scope().loadPrevious(data);
    }
    catch(err) {
        alert('Error in loadPreviousValues: ' + err.message);
    }
}

function generateJSON() {
    try{
        var data = {};
        data = angular.element(document.getElementById('formApp')).scope().getDataObject();    
        // console.log('** generateJSON:');
        // console.dir(data);
        // console.dir(JSON.stringify(data));
        return JSON.stringify(data);
    }
    catch(err) {
        alert('Error in generateJSON: ' + err.message);
    }
}

