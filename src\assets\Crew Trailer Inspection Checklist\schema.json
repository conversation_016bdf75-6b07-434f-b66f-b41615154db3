{"PMIR": {"PMIRReference": "", "RigNumber": "", "RigManager": "", "CompanyID": "", "CompanyName": "", "CompletedDate": "", "CompletedBy": "", "CompByPosition": "", "Location": "", "Inspector1": "", "Inspector2": "", "Inspector3": "", "Operator": "", "InspType": "", "Accumulator": "", "Precharge": "", "Before": "", "After": "", "AfterFunctStack": "", "PartFunctionTests": "", "NitrogenBottles": "", "BackupNitBottles": "", "BOPLabeled": "", "BOPComment": "", "Oil": "", "OilComment": "", "PrechargePump": "", "SiteManager": "", "SMSignoff": "", "SMCrownSignoff": "", "FlarePit": "", "OpenPit": "", "LeaseSigns": "", "LeaseSite": "", "WellboreFlame": "", "Distance": "", "Distance2": "", "FlareLine": "", "SmokingRules": "", "FlareTankGround": "", "FlareTankIgnition": "", "RemoteIgnition": "", "EscapeAnchor": "", "EscapePath": "", "ClimbAssist": "", "LinesFlagged": "", "PRCatwalk": "", "PRCWElectrical": "", "PRCWEstop": "", "PRVdoor": "", "PRRacks": "", "PRRackEnds": "", "PRRackSpacer": "", "PRDerrickStand": "", "PRRackSecureCW": "", "PRLoadRatings": "", "PRSupportPads": "", "GenGroundRods": "", "GenElectical": "", "GenSockets": "", "GenSwitches": "", "GenGauges": "", "GenLockouts": "", "GenExhaust": "", "GenGuards": "", "GenVoltageSign": "", "GenLELs": "", "GenGasShutoffs": "", "GenESP": "", "GenGround": "", "ACCN2Labeled": "", "ACCControls": "", "ACCReservoir": "", "ACCN2Precharge": "", "ACCN2Secure": "", "ACCGrounded": "", "THFaceShield": "", "THToolRest": "", "THHoseCrimper": "", "THPressureWasher": "", "THAirComp": "", "THCTLCupboard": "", "THGrounded": "", "MPPopValve": "", "MPPVChart": "", "MPPVCovers": "", "MPPVLines": "", "MPGuards": "", "MPHoses": "", "MPLockouts": "", "MPExhaust": "", "MTWalkwayCovered": "", "MTBarricades": "", "MTGuards": "", "MTStoppers": "", "MTVolIND": "", "MTSuctionIND": "", "MTMudVan": "", "MTSafetyLine": "", "MTWalkwayClear": "", "MTPPE": "", "MTRescueEquip": "", "MTWinch": "", "MTElectrical": "", "MTFlooring": "", "MTValveHandles": "", "MTHydrlRams": "", "MTAgitator": "", "MTEyeWash": "", "MTChemBarrel": "", "SUBGrounded": "", "SUBPins": "", "SUBHeatLight": "", "SUBMatting": "", "SUBCellar": "", "SUBSling": "", "SUBHPLCables": "", "SUBDropsInsp": "", "SUBDropsSecured": "", "SUBOil": "", "SUBBOPHandler": "", "SUBBOPCables": "", "SUBLMS": "", "SUBBOPSlings": "", "SUBFallProtection": "", "DRKDrillLine": "", "DRKLadder": "", "DRKLineCondition": "", "DRKBuggy": "", "DRKLanyards": "", "DRKMonkeyBoard": "", "DRKMBLines": "", "DRKMBWinch": "", "DRKBodyHarness": "", "DRKCrownBlock": "", "DRKCrownSheave": "", "DRKLighting": "", "DRKLifeLine": "", "DRKCantilever": "", "DRKSlopedLine": "", "DRKFallArrest": "", "DRKAnchors": "", "DRKHandRails": "", "DRKDropsInsp": "", "DRKDropsSecured": "", "DRKLMS": "", "DRKAviationLight": "", "DRSubcylinders": "", "RFOpening": "", "RFHandrails": "", "RFPullLine": "", "RFChain": "", "RFCathead": "", "RFHeadache": "", "RFWinch": "", "RFWeightIND": "", "RFKellyHose": "", "RFCirculatingHose": "", "RFStandPipe": "", "RFTongs": "", "RFTorqueGauge": "", "RFClamps": "", "RFFloorRacking": "", "RFRotaryTableMat": "", "RFDogCollar": "", "RFSlipElevator": "", "RFLadder": "", "RFRemoteConEquip": "", "RFDangerZones": "", "RFDangerZoneSignage": "", "RFVdoor": "", "RFDriveChain": "", "RFHorn": "", "RFNonSkidFloor": "", "RFBOP": "", "RFMakeupTongLines": "", "RFMakeupBreakOutCyl": "", "DWBrake": "", "DWBrakeLinkage": "", "DWBrakeCalipers": "", "DWBrakeAdj": "", "DWBrakeTest": "", "DWBrakeWaterLines": "", "DWManifold": "", "DWExhaustWater": "", "DWGuards": "", "DWLockouts": "", "DWQuickConnect": "", "DWCrownSaver": "", "DWBoardSaver": "", "DWBypassAlarm": "", "DHFirstAidKit": "", "DHTetheredTools": "", "DHFaceShield": "", "DHBodyHarness": "", "DHHandTools": "", "DHStabbingValve": "", "DHEyeWash": "", "DHHearing": "", "DHSafetyLine": "", "DHLicense": "", "DHHoleFill": "", "DHXOSubs": "", "DHTestPlugs": "", "DHWellCharts": "", "DHMACPCharts": "", "DHSoundLayout": "", "DHQHSE": "", "DHTargetZero": "", "DHBlindRam": "", "DHGrounded": "", "PACracks": "", "PACaution": "", "PALockout": "", "PAArmDie": "", "PAArmOpening": "", "PASafetyPins": "", "PAJawPins": "", "PAPicker": "", "PASlider": "", "PACheckValves": "", "PAFallArrest": "", "TDLockout": "", "TDHingePins": "", "TDBolts": "", "TDFluids": "", "TDTiltAlarms": "", "TDInspection": "", "TDEstop": "", "TDTravelLimits": "", "SAFaultLights": "", "SALockouts": "", "SALockoutProc": "", "SALighting": "", "SAGauges": "", "SAAirConditioner": "", "SADoorFilters": "", "SABays": "", "SAFloorMat": "", "SAGrounded": "", "SABlowers": "", "SAConsole": "", "SASCRShutdown": "", "SAEngineShutdown": "", "SABrake": "", "SAPlugBoard": "", "SAPLC": "", "SAFireExt": "", "GNLHandrails": "", "GNLStairs": "", "GNLEntrances": "", "GNLLighting": "", "GNLHousekeeping": "", "GNLHazAssess": "", "GNLGONOGauge": "", "GNLEstop": "", "GNLThreadProtect": "", "GNLHPU": "", "GNPressureWasher": "", "GNAirComp": "", "WSTSoundLayout2": "", "WSTEmergShower2": "", "SEFireExt": "", "SEFireExtCheck": "", "SEGasDetection": "", "SERescueKit": "", "SESignage": "", "SERoadSign": "", "SETDG": "", "SEOSHA": "", "SEBOPClosed": "", "SEManInDRK": "", "SENoGoZoneSignage": "", "ENSpillKit": "", "ENPumpsLeakFree": "", "ENFuelOilLeakFree": "", "ENValvesLeakFree": "", "ENFuelShutdown": "", "ENDitches": "", "ENHazMatStickers": "", "ENHazWaste": "", "ENDripTrays": "", "ENSPCC": "", "BLRCertification": "", "BLRLeakFree": "", "BLRWaterPump": "", "BLRSiteGlass": "", "BLRExhaust": "", "BLRMuffler": "", "BLRChemPot": "", "BLREyewashPPE": "", "BLRLockout": "", "BLRAntiSkidFlr": "", "BLRAutoMix": "", "BLRGuard": "", "BLRContainers": "", "BLREstop": "", "BLRGasDetection": "", "BLRGrounded": "", "BOPIceMud": "", "BOPHeating": "", "BOPSecured": "", "BOPHCRValve": "", "BOPKillLine": "", "BOPChokeKillValve": "", "BOPHosesChoke": "", "BOPCellars": "", "BOPDrills": "", "BOPPressureTests": "", "BOPCatchTrays": "", "BOPAnnularValve": "", "BOPLockingWheels": "", "BOPTargetT": "", "MSMACP": "", "MSValveHandles": "", "MSGauges": "", "MSFlareLines": "", "MSFDegasserLines": "", "MSGasBusterLines": "", "MSHeating": "", "MSHousekeeping": "", "MSGrounded": "", "WSTSmokeDetector": "", "WSTCODetector": "", "WSTFirstAidKit": "", "WSTBloodBorneKit": "", "WSTSoundLayout": "", "WSTEmergShower": "", "WSTTargetZero": "", "WSTGrounded": "", "CPSmokeDetector": "", "CPCODetector": "", "CPFirstAidKit": "", "CPBloodBorneKit": "", "CPSoundLayout": "", "CPEmergShower": "", "CPGrounded": "", "OTTPGrounded": "", "OTTPGuarded": "", "OTTPEquipCertified": "", "Comments": ""}}