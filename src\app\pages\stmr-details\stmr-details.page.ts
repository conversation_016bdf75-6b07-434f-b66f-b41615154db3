import { Component, OnInit, NgZone, Input, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DataService } from 'src/app/services/data.service';
import { BusyIndicatorService } from 'src/app/services/busy-indicator.service';
import { UnviredCordovaSDK, ResultType, RequestType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { UtilityService } from 'src/app/services/utility.service';
import * as RigActions from 'src/app/store/store.actions';
import { PrintSTMRFormService } from 'src/app/services/printStmr.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  IonGrid,
  IonRow,
  IonCol,
  IonLabel,
  IonInput,
  IonButton,
  IonIcon,
  IonCard,
  IonCardHeader,
  IonCardContent,
  IonCardTitle,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButtons,
  IonContent,
  IonItem,
  IonRadio,
  IonRadioGroup,
  IonThumbnail,
  ModalController,
  AlertController,
  IonSearchbar,
  ToastController,
  PopoverController,
  IonRippleEffect
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { AppConstants } from 'src/app/constants/appConstants';
import { SelectListPage } from '../select-list/select-list.page';
import { TopicsPage } from '../topics/topics.page';
import {
  personCircleOutline,
  sendOutline,
  chevronDownOutline,
  arrowBackOutline,
  cogOutline,
  trashOutline,
  personAddOutline,
  closeOutline,
  addCircleOutline
} from 'ionicons/icons';
import { STMR_ACTION } from 'src/models/STMR_ACTION';
import { SignaturePage } from '../signature/signature.page';
import { STMR_TOPIC } from 'src/models/STMR_TOPIC';
import { STMR_HSE_TOPIC } from 'src/models/STMR_HSE_TOPIC';
import { STMR_CTA_TOPIC } from 'src/models/STMR_CTA_TOPIC';
import { CTA_HEADER } from 'src/models/CTA_HEADER';
import { STMR_FORM } from 'src/models/STMR_FORM';
import { HSE_STANDARD_HEADER } from 'src/models/HSE_STANDARD_HEADER';
import { StmrOptionsPopoverPage } from '../stmr-options-popover/stmr-options-popover.page';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { filter, take } from 'rxjs/operators';
import { selectPrefilledData } from 'src/app/store/store.selector';
import { CrewPage } from '../crew/crew.page';
import { CREW_HEADER } from 'src/models/CREW_HEADER';
import { STMR_HEADER } from 'src/models/STMR_HEADER';
import { STMR_CREW } from 'src/models/STMR_CREW';
import { TEMPLATE_HEADER } from 'src/models/TEMPLATE_HEADER';
import { AlertService } from 'src/app/services/alert.service';
import { STMR_TOPIC_ENTITY } from 'src/models/STMR_TOPIC_ENTITY';
import { STMR_FORM_DATA } from 'src/models/STMR_FORM_DATA';
import moment from 'moment';
import { EventsService } from 'src/app/services/events.service';
import { Observable, Subscription } from 'rxjs';
import { Platform } from '@ionic/angular/standalone';
addIcons({
  'person-circle': personCircleOutline,
  'send-outline': sendOutline,
  'chevron-down-outline': chevronDownOutline,
  'arrow-back-outline': arrowBackOutline,
  'cog-outline': cogOutline,
  'trash-outline': trashOutline,
  'person-add-outline': personAddOutline,
  'close-outline': closeOutline,
  'add-circle-outline': addCircleOutline
});

@Component({
  selector: 'app-stmr-details',
  templateUrl: './stmr-details.page.html',
  styleUrls: ['./stmr-details.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IonGrid,
    IonRow,
    IonCol,
    IonLabel,
    IonInput,
    IonButton,
    IonIcon,
    IonCard,
    IonCardHeader,
    IonCardContent,
    IonCardTitle,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonContent,
    IonItem,
    IonRadio,
    IonRadioGroup,
    IonThumbnail,
    SelectListPage,
    IonSearchbar,
    IonRippleEffect,
  ]
})
export class STMRDetailsPage implements OnInit {
  @Input() stmrHeader: STMR_HEADER = new STMR_HEADER(); 
  @Input() stmrCrew: any[] = [];                         
  @Input() stmrTopicEntity: any[] = [];                  
  @Input() isNewSTMR: boolean = false;  
  @HostListener('ionChange', ['$event'])
  @HostListener('input', ['$event'])
  @HostListener('change', ['$event'])                 
  
  prefilledData$!: Observable<any>;
  isSTMRComplete: boolean = false;
  styleTheme: string = '';
  isLoading: boolean = false;
  loadingMessage: string = '';
  isSTMRUpdated = false;
  timestampOfLastTap: number = 0;
  isPageActive = true;
  prefillData: any;
  autoFillData: any = {};
  stmrActions: STMR_ACTION[] = [];
  linkedForms: any;
  templates: TEMPLATE_HEADER[] = [];
  isPopOverOpen: boolean = AppConstants.BOOL_FALSE;

  constructor(
    private modalController: ModalController,
    private alertController: AlertController,
    private translate: TranslateService,
    private ngZone: NgZone,
    private dataService: DataService,
    private unviredSDK: UnviredCordovaSDK,
    private busyIndicatorService: BusyIndicatorService,
    private toastController: ToastController,
    private UtilityService: UtilityService,
    private popoverCtrl: PopoverController,
    private printSTMRForm: PrintSTMRFormService,
    private router: Router,
    private alertService: AlertService,
    private busyIndicator: BusyIndicatorService,
    private store: Store<any>,
    private eventsService: EventsService,
    private activatedRoute: ActivatedRoute
  ) {
   }
  
  
  async ngOnInit(): Promise<void> {
  this.prefilledData$ = this.store.select(selectPrefilledData);
  console.log('STMRDetails - Starting ngOnInit...');
  
  // FIRST: Get router state data to initialize properties
  const navigation = this.router.getCurrentNavigation();
  const routerState = navigation?.extras?.state || history.state;
  console.log('Router state', routerState)
  // SECOND: Handle new STMR creation with router state
  if (routerState && routerState.stmrHeader) {
    console.log('STMRDetails - Router state data found');
    
    // Set the properties from router state FIRST
    this.stmrHeader = routerState.stmrHeader;
    this.stmrCrew = routerState.stmrCrew || [];
    this.stmrTopicEntity = routerState.stmrTopicEntity || [];
    this.isNewSTMR = routerState.isNewSTMR || false;
    
    console.log('STMRDetails - Using pre-initialized STMRID:', this.stmrHeader.STMR_ID);
    console.log('STMRDetails - Received crew count:', this.stmrCrew.length);
    console.log('STMRDetails - Received topics count:', this.stmrTopicEntity.length);
    
    // THEN: For new STMRs, get prefill data from store
    if (this.isNewSTMR) {
      this.prefilledData$.pipe(take(1)).subscribe(data => {
        if (data) {
          this.prefillData = data;
          this.autoFillData = data;
          console.log('STMRDetails - Prefill data loaded from store:', this.prefillData);
        } else {
          console.warn("No prefill data found in store yet");
        }
      });
    }
    


  else {
    // THIRD: Fallback - Load from database (editing existing STMR)
    console.log('STMRDetails - No router state, loading from DB');
    await this.getAllData();
  }
}
  // Set initial state
  this.markSTMRAsPristine();
}

async ionViewWillEnter(){
 this.activatedRoute.queryParams.subscribe(params => {
  const savedTopic = params['savedTopic'] ? JSON.parse(params['savedTopic']) : null;
  const stmrActions = params['stmrActions'] ? JSON.parse(params['stmrActions']) : [];
  const stmrHeader = params['stmrHeader'] ? JSON.parse(params['stmrHeader']) : null;

  console.log(savedTopic);
if (savedTopic.topic) {
  const alreadyExists = this.stmrTopicEntity.some((topic: any) => {
    // Check if the topic IDs and numbers match
    return (
      topic.topic?.TOPIC_ID === savedTopic.topic.TOPIC_ID &&
      topic.topic?.TOPIC_NO === savedTopic.topic.TOPIC_NO
    );
  });

  if (!alreadyExists) {
    this.stmrTopicEntity.push(savedTopic);
  }
}

});
console.log('TopicEntity', this.stmrTopicEntity)
}


  async getAllData() {
  try {
    this.stmrCrew = await this.dataService.getCrewMembers(this.stmrHeader.STMR_ID); // unchanged [attached_file:4]

    // Fetch raw
    const topicsRaw   = await this.dataService.getTopics(this.stmrHeader.STMR_ID);   // already filters P_MODE != 'D' in SQL [attached_file:6]
    const formsRaw    = await this.dataService.getForms(this.stmrHeader.STMR_ID);    // already filters P_MODE != 'D' in SQL [attached_file:6]
    const formDataRaw = await this.dataService.getFormData(this.stmrHeader.STMR_ID); // already filters P_MODE != 'D' in SQL [attached_file:6]

    // Extra safety: also drop anything flagged as OBJECT_STATUS.DELETE
    const topics = topicsRaw.filter(t => t?.P_MODE !== 'D' && t?.OBJECT_STATUS !== AppConstants.OBJECT_STATUS.DELETE); 
    const forms = formsRaw.filter(f => f?.P_MODE !== 'D' && f?.OBJECT_STATUS !== AppConstants.OBJECT_STATUS.DELETE); 
    const formData = formDataRaw.filter(fd => fd?.P_MODE !== 'D' && fd?.OBJECT_STATUS !== AppConstants.OBJECT_STATUS.DELETE);

    this.stmrActions = await this.dataService.getFormActions(this.stmrHeader.STMR_ID); 

    // Build Topic Entities (only from active topics)
    const topicHashList: Record<string, STMR_TOPIC_ENTITY> = {}; 
    topics.forEach(topic => {                                     // use filtered topics
      const topicEntity = new STMR_TOPIC_ENTITY();
      topicEntity.topic = topic;
      topicHashList[topic.TOPIC_NO] = topicEntity;
    }); 

    // Attach only active forms/data; if topic was deleted, optional chaining prevents attaching
    forms.forEach(form => {
      topicHashList[form.TOPIC_NO]?.forms.push(form);
    }); 
    formData.forEach(fd => {
      topicHashList[fd.TOPIC_NO]?.data.push(fd);
    }); 

    this.stmrTopicEntity = Object.values(topicHashList); 
      // Reload header
      try {
        this.stmrHeader = await this.dataService.reloadHeader(this.stmrHeader.LID);
      } catch (err: any) {
        if (err.message.includes('No STMR Header')) {
          await this.dataService['unviredSDK'].logInfo('STMRDetailsPage', 'getAllData', 'Creating a new STMR.');
        }
      }
    } catch (error: any) {
      this.alertService.showAlert('Error', error.message || 'Unexpected error');
      await this.dataService['unviredSDK'].logError('STMRDetailsPage', 'getAllData', error.message);
    }
  }


  async addTopic(topicEntity: any = null, mode: 'Add' | 'Edit' = 'Add'): Promise<void> {
  // Navigate to topics page with data - similar to componentProps
  this.router.navigate(['/topics'], {
    state: {
      stmrHeader: this.stmrHeader,
      topicEntity: topicEntity,
      mode: mode,
      existingTopics: this.stmrTopicEntity,
      isReadonly: this.isSTMRReadonly(this.stmrHeader)
    }
  });
  console.log('the stmr header is', this.stmrHeader);
}

  async editTopic(entity: any, i: number, mode: 'Add' | 'Edit' = 'Edit') {   
  // Navigate to topics page with existing topic data
  this.router.navigate(['/topics'], {
    state: {
      stmrHeader: this.stmrHeader,
      topicEntity: entity,
      mode: mode,
      existingTopics: this.stmrTopicEntity,
      isReadonly: this.isSTMRReadonly(this.stmrHeader)
    }
  });
  }


  async removeTopic(topic: any, idx: number) {
  const alert = await this.alertController.create({
    header: 'Delete Topic',
    message: `Are you sure you want to delete this topic?`,
    buttons: [
      { text: 'Cancel', role: 'cancel' },
      { 
        text: 'Delete', 
        role: 'destructive', 
        handler: () => {
          // Do the deletion directly here, don't call deleteStmrTopicOnly
          this.stmrTopicEntity[idx].topic.P_MODE = 'D';
          this.stmrTopicEntity[idx].topic.OBJECT_STATUS = AppConstants.OBJECT_STATUS.DELETE;
          
          // Update forms and data
          this.stmrTopicEntity[idx].forms = this.stmrTopicEntity[idx].forms.map((el: any) => ({
            ...el,
            P_MODE: 'D',
            OBJECT_STATUS: AppConstants.OBJECT_STATUS.DELETE
          }));
          
          this.stmrTopicEntity[idx].data = this.stmrTopicEntity[idx].data.map((el: any) => ({
            ...el,
            P_MODE: 'D',
            OBJECT_STATUS: AppConstants.OBJECT_STATUS.DELETE
          }));
          
          this.updateSTMRHeaderLastSync(this.stmrHeader);
          this.markSTMRAsUpdated();
        }
      }
    ]
  });

  await alert.present();
  // Remove the deleteStmrTopicOnly call completely
}



  async addCrew() {
  const modal = await this.modalController.create({
    component: CrewPage,
    cssClass: 'full-screen-modal',
    componentProps: {
      crewData: this.stmrCrew,
      theme: this.styleTheme,
      RIG_NO: this.stmrHeader?.RIG_NO,
    }
  });

  await modal.present();
  const { data, role } = await modal.onWillDismiss();

// Only apply on explicit confirm
if (role !== 'confirm' || !data?.selectedCrew) {
  return;
}

this.ngZone.run(() => {
  // Keep 3rd-party crew
  const existingThirdPartyCrew = this.stmrCrew.filter(c =>
    (c.CREW_TYPE ?? c.CREWTYPE) === 'THIRD_PARTY'
  );

  // Build a lookup of existing non-deleted, non-3rd-party crew by ID
  const existingById = new Map(
    this.stmrCrew
      .filter(c => (c.P_MODE ?? c.PMODE) !== 'D' && (c.CREW_TYPE ?? c.CREWTYPE) !== 'THIRD_PARTY')
      .map(c => [ (c.CREW_ID ?? c.CREWID), c ])
  );

  // Map modal payload to STMR_CREW and preserve signatures
  const selectedCrewMapped = data.selectedCrew.map((c: any) => {
    const id = c.CREWID ?? c.CREW_ID ?? c.PERSONNO ?? c.PERSON_NO;
    const existing = existingById.get(id);
    const mapped: STMR_CREW = new STMR_CREW();
    mapped.STMR_ID     = this.stmrHeader.STMR_ID;
    mapped.CREW_ID     = id;
    mapped.CREW_POS    = c.CREWPOS ?? c.CREW_POS ?? c.DESIGNATION ?? '';
    mapped.CREW_NAME   = c.CREWNAME ?? c.CREW_NAME ?? c.USERNAME ?? c.USER_NAME ?? '';
    mapped.CREW_TYPE   = c.CREWTYPE ?? c.CREW_TYPE ?? 'INTERNAL';
    mapped.CREW_SIGN   = c.CREWSIGN ?? c.CREW_SIGN ?? existing?.CREW_SIGN ?? existing?.CREWSIGN ?? null;
    mapped.FID         = this.stmrHeader.LID;
    mapped.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
    mapped.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
    mapped.P_MODE      = 'A';
    return mapped;
  });

  // Merge selected + existing third-party
  this.stmrCrew = [...selectedCrewMapped, ...existingThirdPartyCrew];
  this.markSTMRAsUpdated();
});

  }



  async editCrew(crew: any, i: number) {
    const modal = await this.modalController.create({
      component: CrewPage,
      cssClass: 'full-screen-modal',
      componentProps: { crew }
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data && data.crew) {
      this.stmrCrew[i] = { ...data.crew, P_MODE: 'M' };
      this.markSTMRAsUpdated();
    }
  }

  /**
 * Open Crew select modal
 */
public async openSelectCrew(field: string, userName?: string, searchValue?: string): Promise<void> {
  if (this.isPopOverOpen) return;
  await this.showMembers(field, userName, searchValue);
}

/**
 * Show members from CREW either from autoFillData or DB
 */
public async showMembers(field: string, userName?: string, searchValue?: string): Promise<void> {
  if (this.isPopOverOpen) return;
  
  console.log('STMRDetails - showMembers called for field:', field);
  
  try {
    let crewData: any[] = [];

    // First try to get crew from autoFillData
    if (this.autoFillData?.CREW && this.autoFillData.CREW.length > 0) {
      console.log('STMRDetails - Using crew data from autoFillData');
      crewData = this.autoFillData.CREW;
    } else {
      // Fallback to DB query if autoFillData is not available
      console.log('STMRDetails - autoFillData not available, querying DB');
      const result = await this.dataService.getCrewHeaderData();
      
       console.log('STMRDetails - DB result:', result);
      console.log('STMRDetails - DB result type:', typeof result);
      
      // Handle different result structures
      if (Array.isArray(result)) {
        crewData = result;
      } else if (result && result.data && Array.isArray(result.data)) {
        crewData = result.data;
      } else if (result && result.type === ResultType.success && result.data) {
        crewData = Array.isArray(result.data) ? result.data : [result.data];
      } else {
        console.warn('STMRDetails - Unexpected result structure from getCrewHeaderData');
        crewData = [];
      }
      
      console.log('STMRDetails - Processed crew data:', crewData.length, 'records');
    }

    if (!crewData || crewData.length === 0) {
      console.warn('STMRDetails - No crew data available');
      this.alertService.showAlert('Info', 'No crew members available. Please sync data first.');
      return;
    }

    // Process crew data into display format
    const listData = crewData.map((crew: any) => {
      let displayString = crew.USER_NAME || 'Unknown';
      if (crew.SOURCE === 'SAP') {
        displayString += ` - ${crew.DESIGNATION || ''} - ${crew.PERSON_NO || ''}`;
      } else {
        displayString += ` - ${crew.DESIGNATION || ''} - ${crew.SOURCE || 'LOCAL'}`;
      }
      return {
        DisplayString: displayString,
        object: crew,
        isSelected: crew.USER_NAME === userName ? AppConstants.BOOL_TRUE : AppConstants.BOOL_FALSE
      };
    });

    console.log('STMRDetails - Opening crew selection modal');
    const crewModal = await this.modalController.create({
      component: SelectListPage,
      cssClass: 'full-screen-modal',
      componentProps: {
        listData,
        listDetails: {
          title: `Select Crew Member`,
          searchPlaceHolder: `Search / Type a New Crew Member`,
          selectKey: 'isSelected',
          multiSelect: AppConstants.BOOL_FALSE,
          searchValue: searchValue || ''
        },
        theme: this.styleTheme
      },
      backdropDismiss: false
    });

    this.isPopOverOpen = AppConstants.BOOL_TRUE;
    
    await crewModal.present();
    
    const modalResult = await crewModal.onDidDismiss();
    
    this.isPopOverOpen = AppConstants.BOOL_FALSE;
    const prev = (this.stmrHeader as any)[field]?.trim() || '';
    
    if (modalResult?.data?.DisplayString && modalResult.data.object) {
  const newName = (modalResult.data.object.USER_NAME || modalResult.data.DisplayString || '').trim();
this.stmrHeader = JSON.parse(JSON.stringify(this.stmrHeader));
  // Search stmrHeader for a matching key
  const key = Object.keys(this.stmrHeader).find(k => k === field);

  if (key) {
    const prev = (this.stmrHeader as any)[key]?.trim() || '';

    if (newName !== prev) {
      // Special case: reset signature if onsite supervisor changes
      if (key === 'ONSITE_SUP') {
        this.stmrHeader.ONSITE_SUP_SIGN = '';
        this.markSTMRAsUpdated();
      }

      this.ngZone.run(() => {
        (this.stmrHeader as any)[key] = newName;
      });

      console.log(`${key} updated:`, newName);
    }
  } else {
    console.warn(`Key '${field}' not found in stmrHeader`);
  }
}

  } catch (error) {
    console.error('STMRDetails - Error in showMembers:', error);
    this.unviredSDK.logError(
      'STMRDetailsPage',
      'showMembers',
      `Error showing crew members: ${error}`
    );
    this.alertService.showAlert(
      'Alert',
      'No crew members are configured in this site. Please configure crew members to continue.'
    );
  }
}

onAnyFieldChanged(_evt?: Event): void {
  this.isSTMRUpdated = true;
  if (this.stmrHeader) {
    (this.stmrHeader as any).saved = false;
  }
}

// Back-button handler for header back and any programmatic “close”
async onBack(): Promise<void> {
  if (this.isSTMRReadonly(this.stmrHeader)) {
    this.goToFormsPage();
    return;
  }
  await this.showExitAlertAlways();
}


private async showExitAlertAlways(): Promise<void> {
  const alert = await this.alertController.create({
    header: this.translate.instant('Are you sure you want to go back?'),
    subHeader: this.translate.instant("Your unsaved changes will be lost if you don't save them."),
    buttons: [
      {
        text: this.translate.instant('Save'),
        handler: async () => {
          try {
            await this.localSaveSTMR();         // local save only
            await this.goToFormsPage();         // then leave
          } catch {
            await this.showAlert(
              this.translate.instant('Error'),
              this.translate.instant('Failed to save before leaving. Please try again.')
            );
          }
        }
      },
      {
        text: this.translate.instant("Don't Save"),
        handler: async () => {
          await this.goToFormsPage();           // leave without saving
        }
      },
      {
        text: this.translate.instant('Cancel'),
        role: 'cancel'                          // stay here
      }
    ],
  });
  await alert.present();
}


  async presentSettingsPopover(event: UIEvent): Promise<void> {
  const popover = await this.popoverCtrl.create({
    component: StmrOptionsPopoverPage,
    event,
    translucent: true
  });
  await popover.present();
  const { data } = await popover.onDidDismiss();
  if (!data) {
    return;
  }
  else if (data.print) {
    try {
      // Extract topics from stmrTopicEntity
      const topics: STMR_TOPIC[] = this.stmrTopicEntity.map(entity => entity.topic);
      const STMRTopicsWithHSENames = await this.updateHSENamesForSTMRTopics(topics);
      const STMRTopicsWithCTANames = await this.updateCTANamesForSTMRTopics(STMRTopicsWithHSENames);
      const STMRTopicWithCTAForms = await this.updateFormsForCTATopic(STMRTopicsWithCTANames)
      // Print STMR
      this.printSTMRForm.printSTMRForm(this.stmrHeader.STMR_ID, {
        stmrHeader: this.stmrHeader,
        stmrTopic: STMRTopicWithCTAForms,
        stmrCrew: this.stmrCrew
      });
      console.log('STMRDetails - Print initiated for STMR_ID:', this.stmrHeader.STMR_ID); 
      console.log('STMR Crew:'+ JSON.stringify(this.stmrCrew));
    } catch (error) {
      console.error('Error while preparing STMR for print:', error);
    }
  }
}

async updateHSENamesForSTMRTopics(stmrTopics: STMR_TOPIC[]): Promise<STMR_HSE_TOPIC[]> {
  if (!stmrTopics.length) return [];
  const HSEIds = stmrTopics.map(item => item.STD_ID).filter(Boolean);
  if (!HSEIds.length) {
    return stmrTopics.map(item => new STMR_HSE_TOPIC(item));
  }
  const inClause = HSEIds.map(id => `'${id}'`).join(',');
  const query = `SELECT * FROM HSE_STANDARD_HEADER WHERE STD_ID IN (${inClause})`;
  const result = await this.unviredSDK.dbExecuteStatement(query);
  if (result.type !== ResultType.success) {
    console.error('Failed to fetch HSE headers');
    return stmrTopics.map(item => new STMR_HSE_TOPIC(item));
  }
  const hseMap: Map<string, string> = new Map<string, string>(
  result.data.map((hse: HSE_STANDARD_HEADER) => [hse.STD_ID, hse.NAME])
 );
  return stmrTopics.map(item => {
    const hseTopic = new STMR_HSE_TOPIC(item);
    hseTopic.STD_TYPE = hseMap.get(item.STD_ID) || '';
    return hseTopic;
  });
}

async updateCTANamesForSTMRTopics(stmrTopics: STMR_TOPIC[]): Promise<STMR_CTA_TOPIC[]> {
  if (!stmrTopics.length) return [];
  const CTAIds = stmrTopics.map(item => item.CTA_ID).filter(Boolean);
  if (!CTAIds.length) {
    return stmrTopics.map(item => new STMR_CTA_TOPIC(item));
  }
  const inClause = CTAIds.map(id => `'${id}'`).join(',');
  const query = `SELECT * FROM CTA_HEADER WHERE CTA_ID IN (${inClause})`;
  const result = await this.unviredSDK.dbExecuteStatement(query);
  if (result.type !== ResultType.success) {
    console.error('Failed to fetch CTA headers');
    return stmrTopics.map(item => new STMR_CTA_TOPIC(item));
  }
  const ctaMap: Map<string, string> = new Map<string, string>(
  result.data.map((cta: CTA_HEADER) => [cta.CTA_ID, cta.NAME])
);
  return stmrTopics.map(item => {
    const ctaTopic = new STMR_CTA_TOPIC(item);
    ctaTopic.CTA_TYPE = ctaMap.get(item.CTA_ID) || '';
    return ctaTopic;
  });
}

async updateFormsForCTATopic(stmrWithCTATopics: STMR_CTA_TOPIC[]): Promise<STMR_CTA_TOPIC[]> {
  if (!stmrWithCTATopics.length) return [];
  const stmrId = stmrWithCTATopics[0].STMR_ID;
  const result = await this.unviredSDK.dbSelect("STMR_FORM", `STMR_ID = '${stmrId}'`);
  if (result.type !== ResultType.success || !result.data?.length) {
    console.error("Error fetching forms for CTA topics or no data");
    return stmrWithCTATopics;
  }
 const forms: STMR_FORM[] = result.data;
  stmrWithCTATopics.forEach(topic => {
    const formsForTopic = forms.filter(form => form.TOPIC_NO === topic.TOPIC_NO);
    topic.FORMS = formsForTopic.map(f => f.NAME);
  });
  return stmrWithCTATopics;
}
  
  async localSaveSTMR(): Promise<void> {
    const now = Date.now();
    if (now - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
      this.unviredSDK.logInfo('STMRDetailsPage', 'localSaveSTMR', 'Ignoring double tap');
      return;
    }
    this.timestampOfLastTap = now;
    if (this.stmrHeader.SYNC_STATUS === AppConstants.SYNC_STATUS.QUEUED) {
      // Save to server queue directly if already queued
      await this.saveSTMRToServer();
      return;
    }
    try {
      await this.prepareAndSaveSTMR();
      const toast = await this.toastController.create({
        message: this.translate.instant('STMR Changes saved successfully'),
        duration: 1000,
        position: 'bottom'
      });
      await toast.present();
      this.markSTMRAsPristine();
    } catch (error:any) {
      this.unviredSDK.logError('STMRDetailsPage', 'localSaveSTMR', 'Error while saving STMR to DB');
      await this.showAlert(this.translate.instant("Error"), this.translate.instant("Error while saving STMR: ") + this.getErrorMessage(error));
      throw error;
    }
  }

  /**
   * Save STMR into the server queue and submit the data
   */
  async saveSTMRToServer(): Promise<void> {
    try {
      await this.prepareAndSaveSTMR();
      await this.submitToServer();
      this.markSTMRAsPristine();
    } catch (error) {
      this.unviredSDK.logError('STMRDetailsPage', 'saveSTMRToServer', 'Error while queueing STMR to server');
      await this.showAlert(
        this.translate.instant("Error"),
        this.translate.instant("Error while submitting STMR to server: ") + this.getErrorMessage(error)
      );
      throw error;
    }
  }

  async saveAndExitSTMR(): Promise<void> {
    const now = Date.now();
    if (now - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
      this.unviredSDK.logInfo("STMRDetailsPage", "saveAndExitSTMR", "Ignoring double tap");
      return;
    }
    this.timestampOfLastTap = now;
    this.isPageActive = false;
    const toast = await this.toastController.create({
      message: this.translate.instant('Saving and submitting STMR...'),
      duration: 1000,
      position: 'bottom'
    });
    

    try {
      await this.prepareAndSaveSTMR();
      await this.submitToServer();
      this.store.dispatch(RigActions.loadAllFormsFromDb());
      this.store.dispatch(RigActions.loadStmrDataAndTopicsFromDb());
      await new Promise(res => setTimeout(res, 100));     
      await toast.present();   
      await this.goToFormsPage();
       await toast.dismiss();
    } catch (error) {
      await toast.dismiss();
      this.unviredSDK.logError("STMRDetailsPage", "saveAndExitSTMR", "Error while queueing STMR to server");
      await this.showAlert(
        this.translate.instant("Error"),
        this.translate.instant("Error while submitting STMR to server: ") + this.getErrorMessage(error)
      );
    } finally {
      this.isPageActive = true;
    }
  }

  /**
   * Prepare and save STMR only if it is not already sent 
   */
  prepareAndSaveSTMR(): Promise<void> {
  console.log('PREPARE] Starting prepareAndSaveSTMR...');
  return new Promise((resolve, reject) => {
    console.log('[PREPARE] Checking if STMR is in sent items...');
    this.unviredSDK.logInfo("STMRDetailsPage", "prepareAndSaveSTMR", "Checking if STMR is in sent items.");
    
    this.unviredSDK.isInSentItem(this.stmrHeader.LID).then(resultInSent => {
      console.log('[PREPARE] isInSentItem result:', resultInSent);
      
      const isSent = (String(resultInSent.data).toLowerCase() === "true" || resultInSent.data === AppConstants.BOOL_TRUE);
      console.log('[PREPARE] isSent:', isSent);
      
      if (isSent) {
        console.log('[PREPARE] STMR already sent to server');
        this.unviredSDK.logInfo("STMRDetailsPage", "prepareAndSaveSTMR", "STMR is already sent to server.");
        this.showAlert(
          this.translate.instant("Previous request still being reconciled!"),
          this.translate.instant("STMR could not be submitted. Please wait till response is received.")
        );
        reject('Already sent');
      } else {
        console.log('[PREPARE] Saving STMR to DB...');
        this.unviredSDK.logInfo("STMRDetailsPage", "prepareAndSaveSTMR", "Saving STMR with latest changes.");
        
        this.saveSTMRIntoDB().then(() => {
          console.log('[PREPARE] Successfully saved STMR to DB');
          resolve();
        }).catch((err: any) => {
          console.error('[PREPARE] Error saving STMR to DB:', err);
          reject(err);
        });
      }
    }).catch(err => {
      console.error('[PREPARE] Error checking sent items:', err);
      this.unviredSDK.logError("STMRDetailsPage", "prepareAndSaveSTMR", err);
      reject(err);
    });
  });
}

  /**
 * Save STMR Header and Items into DB 
 */
async saveSTMRIntoDB(): Promise<void> {
    console.log('[SAVE_DB] Starting saveSTMRIntoDB...');   
    try {
        await this.performSaveSTMRIntoDB();
        console.log('[SAVE_DB] saveSTMRIntoDB completed successfully');
    } catch (error) {
        console.error('[SAVE_DB] saveSTMRIntoDB failed with ACTUAL SDK ERROR:');
        console.error('Full SDK Error:', error);
        console.error('SDK Error JSON:', JSON.stringify(error, null, 2));
        this.unviredSDK.logError('STMRDetailsPage', 'saveSTMRIntoDB', 
            `Database save failed: ${JSON.stringify(error, null, 2)}`);
        throw error; 
    }
}


private async performSaveSTMRIntoDB(): Promise<void> {
  this.unviredSDK.logInfo("STMRDetails", "saveSTMRIntoDB", "Initiating STMR Save...");
  console.log('Autofill data', this.prefillData)
  if(this.prefillData != null){
  this.stmrHeader = JSON.parse(JSON.stringify(this.stmrHeader));
  this.stmrHeader.LAST_SYNC_USER = this.prefillData.USER_ID || "";
  this.stmrHeader.SUBM_BY = this.prefillData.USER_ID || "";
  this.stmrHeader.TIME_ZONE = this.UtilityService.getTimezone();
  }
  console.log('[SAVE_DB] Inserting STMR header...');
  await this.insertSTMR();
  console.log('[SAVE_DB] STMR header inserted');
  // Save Items
  console.log('[SAVE_DB] Inserting STMR items...');
  await this.insertSTMRItems();
  console.log('[SAVE_DB] STMR items inserted');
  this.unviredSDK.logInfo(
    "STMRDetails",
    "saveSTMRIntoDB",
    "Successfully saved STMR header and items into DB."
  );
}


/**
 * Update STMR_HEADER and insert into DB
 */
public async insertSTMR(): Promise<void> {
  this.stmrHeader = JSON.parse(JSON.stringify(this.stmrHeader))
  try {   
    console.log('insert stmr', this.stmrHeader.WELL_LOC)   
    if (this.stmrHeader.WELL_LOC) this.stmrHeader.WELL_LOC = this.stmrHeader.WELL_LOC.trim();
    if (this.stmrHeader.CHAIRED_BY) this.stmrHeader.CHAIRED_BY = this.stmrHeader.CHAIRED_BY.trim();
    if (this.stmrHeader.ONSITE_MGR) this.stmrHeader.ONSITE_MGR = this.stmrHeader.ONSITE_MGR.trim();
    if (this.stmrHeader.ONSITE_SUP) this.stmrHeader.ONSITE_SUP = this.stmrHeader.ONSITE_SUP.trim();
    console.log('[DEBUG] Pre-DB Insert RIG_SUB_TYPE:', this.stmrHeader.RIG_SUB_TYPE);  
    // Check for potential truncation
    if (this.stmrHeader.RIG_SUB_TYPE) {
      const originalLength = this.stmrHeader.RIG_SUB_TYPE.length;
      console.log('[DEBUG] RIG_SUB_TYPE length:', originalLength);   
      // Check for common truncation patterns
      if (this.stmrHeader.RIG_SUB_TYPE.includes('-')) {
        const parts = this.stmrHeader.RIG_SUB_TYPE.split('-');
        console.log('[DEBUG] RIG_SUB_TYPE parts after split:', parts);
      }
    }
    // Set completion date and metadata
    const dateComp = moment.utc().unix();
    this.stmrHeader.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
    this.stmrHeader.DATE_COMP = dateComp;
    this.stmrHeader.LAST_SYNC_TIME = moment.utc().unix();
    const data = this.prefillData;
    if (data) {
    this.stmrHeader.LAST_SYNC_USER = data.USER_ID;
    this.stmrHeader.SUBM_BY = data.USER_ID;
    } 
    else {
    await new Promise<void>((resolve) => {
      this.store.select(selectPrefilledData)
        .pipe(take(1))
        .subscribe(data => {
          if (data?.USER_ID) {
            this.stmrHeader.LAST_SYNC_USER = data.USER_ID;
            this.stmrHeader.SUBM_BY = data.USER_ID;
          } else {
            console.warn('No user ID found in store');
          }
          resolve();
        });
    });
    }
    this.stmrHeader.TIME_ZONE = this.UtilityService.getTimezone();
    const stmrHeaderForDB = { ...this.stmrHeader };
    delete (stmrHeaderForDB as any).FID;
    delete (stmrHeaderForDB as any).stmrStatus;
    delete (stmrHeaderForDB as any).syncStatus;
    delete (stmrHeaderForDB as any).isSelected;
    const result = await this.unviredSDK.dbInsertOrUpdate(
      AppConstants.STMR_HEADER,
      stmrHeaderForDB,
      true
    );
    if (result.type !== ResultType.success) {
      this.busyIndicator.hideBusyIndicator();
      this.alertService.showAlert("Error", result.message || "Failed to insert STMR Header");
      throw result;
    }
    this.unviredSDK.logInfo("StmrDetailsPage", "insertSTMR", "STMR Header inserted successfully");
  } catch (error) {
    this.unviredSDK.logError("StmrDetailsPage", "insertSTMR", `Error inserting STMR Header: ${this.getErrorMessage(error)}`);
    throw error;
  }
}

/**
 * Insert STMR Items (crew, topics, forms, actions)
 */
public async insertSTMRItems(): Promise<void> {
  try {
    const crew = JSON.parse(JSON.stringify(this.stmrCrew));
    await this.insertSTMRCrew(crew); 
  } catch (error) {
    this.unviredSDK.logError("StmrDetailsPage", "insertSTMRItems", `Error inserting STMR Items: ${this.getErrorMessage(error)}`);
    throw error;
  }
}
/**
 * Insert STMR_CREW 
 */
async insertSTMRCrew(crew: any[]): Promise<void> {
    console.log(`[INSERT_CREW] Processing ${crew.length} crew members`);
    
    if (crew.length === 0) {
        console.log('[INSERT_CREW] No crew to insert, moving to topics');
        const stmrTopicEntityCopy = JSON.parse(JSON.stringify(this.stmrTopicEntity));
        await this.insertSTMRTopic(stmrTopicEntityCopy, [], [], []);
        return;
    }
    
    const crewMember = crew[0];
    
    console.log('[DEBUG_CREW_BEFORE_DB] Crew member object being sent to DB:', JSON.stringify(crewMember, null, 2));
    console.log('[DEBUG_CREW_BEFORE_DB] Crew member keys:', Object.keys(crewMember));
    console.log('[DEBUG_CREW_BEFORE_DB] Checking for problematic fields:');
    console.log('  - INFO_MSG_CAT:', crewMember.INFO_MSG_CAT);
    console.log('  - HAS_CONFLICT:', crewMember.HAS_CONFLICT);
    console.log('  - TIMESTAMP:', crewMember.TIMESTAMP);
    // Skip GLOBAL items
    if (crewMember.OBJECT_STATUS === AppConstants.OBJECT_STATUS.GLOBAL) {
        console.log('[INSERT_CREW] Skipping GLOBAL crew member');
        crew.splice(0, 1);
        return this.insertSTMRCrew(crew);
    }
    console.log(`[INSERT_CREW] Inserting crew member: ${crewMember.CREW_NAME}`);  
    const cleanCrewMember = {
    LID: crewMember.LID,
    STMR_ID: crewMember.STMR_ID,
    CREW_ID: crewMember.CREW_ID,
    CREW_POS: crewMember.CREW_POS,
    CREW_NAME: crewMember.CREW_NAME,
    CREW_SIGN: crewMember.CREW_SIGN,
    CREW_TYPE: crewMember.CREW_TYPE,
    FID: crewMember.FID,
    SYNC_STATUS: crewMember.SYNC_STATUS,
    OBJECT_STATUS: crewMember.OBJECT_STATUS,
    P_MODE: crewMember.P_MODE
  };  
    try {
        const result = await this.unviredSDK.dbInsertOrUpdate(
            AppConstants.TABLE_STMR_CREW,
            cleanCrewMember,
            AppConstants.BOOL_FALSE
        );      
        console.log('[INSERT_CREW] Crew member inserted successfully');        
        this.unviredSDK.logInfo('STMRDetailsPage', 'insertSTMRCrew', 
            `Successfully inserted crew member: ${crewMember.CREW_NAME}`);        
        await this.updateSTMRHeaderLastSync(this.stmrHeader);        
        // Continue with next crew member
        crew.splice(0, 1);
        await this.insertSTMRCrew(crew);        
    } catch (error) {
        console.error('[INSERT_CREW] ACTUAL SDK ERROR inserting crew member:');
        console.error('SDK Error Object:', error);
        console.error('SDK Error Details:', {
            type: (error as any)?.type,
            data: (error as any)?.data,
            message: (error as any)?.message,
            code: (error as any)?.code
        });
        console.error('SDK Error JSON:', JSON.stringify(error, null, 2));        
        this.unviredSDK.logError('STMRDetailsPage', 'insertSTMRCrew', 
            `Failed to insert crew member ${crewMember.CREW_NAME}: ${JSON.stringify(error, null, 2)}`);   
        throw error;
    }
}

/**
 * Insert STMR_TOPIC
 * Builds arrays for CTA_ID and HSE (STD_ID); then proceeds to STMR_FORM
 */
async insertSTMRTopic(
    tempTopic: STMR_TOPIC_ENTITY[],
    ctaIdArr: string[] = [],
    hseIdArr: string[] = [],
    formArray: Array<{ CTA_ID: string; TOPIC_NO: string }> = []
): Promise<void> {
    console.log(`[INSERT_TOPIC] Processing ${tempTopic.length} topics`);    
    if (tempTopic.length > 0) {
        const itemStmrTopic: STMR_TOPIC = tempTopic[0].topic;       
        if (itemStmrTopic) {
            if (!ctaIdArr) ctaIdArr = [];
            ctaIdArr.push(itemStmrTopic.CTA_ID);
            if (!hseIdArr) hseIdArr = [];
            hseIdArr.push(itemStmrTopic.STD_ID);
            if (!formArray) formArray = [];
            formArray.push({
                CTA_ID: itemStmrTopic.CTA_ID,
                TOPIC_NO: itemStmrTopic.TOPIC_NO
            });
            if (itemStmrTopic.OBJECT_STATUS === AppConstants.OBJECT_STATUS.GLOBAL) {
                console.log('[INSERT_TOPIC] Skipping GLOBAL topic');
                tempTopic.splice(0, 1);
                return this.insertSTMRTopic(tempTopic, ctaIdArr, hseIdArr, formArray);
            }
            console.log('[INSERT_TOPIC] Inserting topic:', itemStmrTopic.TOPIC_NAME);
            this.unviredSDK.logInfo('STMRDetailsPage', 'insertSTMRTopic',
                `Attempting to insert topic: ${itemStmrTopic.TOPIC_NAME}`);
            try {
                const result = await this.unviredSDK.dbInsertOrUpdate(AppConstants.TABLE_STMR_TOPIC, itemStmrTopic, AppConstants.BOOL_FALSE);
                console.log('[INSERT_TOPIC] Topic inserted successfully');
                this.unviredSDK.logInfo('STMRDetailsPage', 'insertSTMRTopic',
                    `Successfully inserted topic: ${itemStmrTopic.TOPIC_NAME}`);
                tempTopic.splice(0, 1);
                return this.insertSTMRTopic(tempTopic, ctaIdArr, hseIdArr, formArray);
            } catch (error) {
                console.error('[INSERT_TOPIC] ACTUAL SDK ERROR inserting topic:');
                console.error('SDK Error Object:', error);
                console.error('SDK Error Details:', {
                    type: (error as any)?.type,
                    data: (error as any)?.data,
                    message: (error as any)?.message,
                    code: (error as any)?.code,
                    topicName: itemStmrTopic.TOPIC_NAME
                });
                console.error('SDK Error JSON:', JSON.stringify(error, null, 2));
                this.unviredSDK.logError('STMRDetailsPage', 'insertSTMRTopic',
                    `Failed to insert topic ${itemStmrTopic.TOPIC_NAME}: ${JSON.stringify(error, null, 2)}`);
                throw error; 
            }
        }
    } else {
        console.log('[INSERT_TOPIC] All topics processed, moving to forms');
        console.log('[INSERT_TOPIC] stmrTopicEntity length:', this.stmrTopicEntity.length);
        
        let stmrForms: STMR_FORM[] = [];
        this.stmrTopicEntity.forEach((e, index) => {
            console.log(`[INSERT_TOPIC] Entity ${index}: forms=${e.forms?.length || 0}, data=${e.data?.length || 0}`);
            stmrForms = stmrForms.concat(e.forms || []);
        });
        
        console.log('[INSERT_TOPIC] Total forms collected:', stmrForms.length);
        console.log('[INSERT_TOPIC] Calling insertSTMRForms...');
        
        await this.insertSTMRForms(stmrForms);
        
        console.log('[INSERT_TOPIC] insertSTMRForms completed');
    }
}

/**
 * Insert STMR_FORM
 * After STMR_FORM, proceed to STMR_FORM_DATA
 */
async insertSTMRForms(stmrForms: STMR_FORM[]): Promise<void> {
  console.log(`[INSERT_FORMS] Processing ${stmrForms.length} forms`);
  
  if (stmrForms.length === 0) {
    console.log('[INSERT_FORMS] No forms to process, moving to form data');
    let stmrData: STMR_FORM_DATA[] = [];
    this.stmrTopicEntity.forEach((e, index) => {
      console.log(`[INSERT_FORMS] Entity ${index} data length:`, e.data?.length || 0);
      stmrData = stmrData.concat(e.data || []);
    });
    console.log('[INSERT_FORMS] Total form data collected:', stmrData.length);
    console.log('[INSERT_FORMS] Calling insertSTMRFormData...');
    await this.insertSTMRFormData(stmrData);
    console.log('[INSERT_FORMS] insertSTMRFormData completed');
    return;
  }
  
  if (stmrForms[0].OBJECT_STATUS === AppConstants.OBJECT_STATUS.GLOBAL) {
    console.log('[INSERT_FORMS] Skipping GLOBAL form');
    stmrForms.splice(0, 1);
    return this.insertSTMRForms(stmrForms);
  }
  
  const formToSave = { ...stmrForms[0] };
  if ((formToSave as any)['syncStatus']) delete (formToSave as any)['syncStatus'];
  if ((formToSave as any)['formStatus']) delete (formToSave as any)['formStatus'];
  
  try {
    console.log('[INSERT_FORMS] Inserting form:', formToSave.FORM_ID);
    await this.unviredSDK.dbInsertOrUpdate(AppConstants.TABLE_STMR_FORM, formToSave, false);
    console.log('[INSERT_FORMS] Form inserted successfully');
    await this.unviredSDK.logInfo('stmrDetailsPage', 'insertSTMRForms',
      'Done. Inserting / Updating STMR Form.');
    stmrForms.splice(0, 1);
    await this.insertSTMRForms(stmrForms);
  } catch (result: any) {
    console.error('[INSERT_FORMS] Error inserting form:', result);
    await this.unviredSDK.logError('stmrDetailsPage', 'insertSTMRForms',
      `inserting / Updating STMR Form into database failed: ${result.message} ${result.error}`);
    throw result;
  }
}

/**
 * Insert STMR_FORM_DATA
 * After STMR_FORM_DATA, proceed to STMR_ACTION
 */
async insertSTMRFormData(stmrFormData: STMR_FORM_DATA[]): Promise<void> {
  console.log(`[INSERT_FORM_DATA] Processing ${stmrFormData.length} form data records`);
  
  if (stmrFormData.length === 0) {
    console.log('[INSERT_FORM_DATA] No form data to process, moving to actions');
    console.log('[INSERT_FORM_DATA] Actions to process:', this.stmrActions.length);
    console.log('[INSERT_FORM_DATA] Calling insertSTMRFormAction...');
    await this.insertSTMRFormAction(this.stmrActions);
    console.log('[INSERT_FORM_DATA] insertSTMRFormAction completed');
    return;
  }
  
  if (stmrFormData[0].OBJECT_STATUS === AppConstants.OBJECT_STATUS.GLOBAL) {
    console.log('[INSERT_FORM_DATA] Skipping GLOBAL form data');
    stmrFormData.splice(0, 1);
    return await this.insertSTMRFormData(stmrFormData);
  }
  
  try {
    console.log('[INSERT_FORM_DATA] Inserting form data:', stmrFormData[0].FORM_ID);
    await this.unviredSDK.dbInsertOrUpdate(AppConstants.TABLE_STMR_FORM_DATA, stmrFormData[0], false);
    console.log('[INSERT_FORM_DATA] Form data inserted successfully');
    stmrFormData.splice(0, 1);
    await this.insertSTMRFormData(stmrFormData);
  } catch (result: any) {
    console.error('[INSERT_FORM_DATA] Error inserting form data:', result);
    await this.unviredSDK.logError('stmrDetailsPage', 'insertSTMRFormData',
      `Inserting STMR Form data into database failed: ${result.message} ${result.error}`);
    throw result;
  }
}

/**
 * Insert STMR_ACTION
 */
async insertSTMRFormAction(stmrActions: STMR_ACTION[]): Promise<void> {
  console.log(`[INSERT_ACTION] Processing ${stmrActions.length} actions`);
  
  if (stmrActions.length === 0) {
    console.log('[INSERT_ACTION] No actions to process, DB save complete');
    return;
  }
  
  if (stmrActions[0].OBJECT_STATUS === AppConstants.OBJECT_STATUS.GLOBAL) {
    console.log('[INSERT_ACTION] Skipping GLOBAL action');
    stmrActions.splice(0, 1);
    return await this.insertSTMRFormAction(stmrActions);
  }
  
  try {
    console.log('[INSERT_ACTION] Inserting action:', stmrActions[0].ACTION_CODE);
    await this.unviredSDK.dbInsertOrUpdate(AppConstants.TABLE_STMR_ACTION, stmrActions[0], false);
    console.log('[INSERT_ACTION] Action inserted successfully');
    stmrActions.splice(0, 1);
    await this.insertSTMRFormAction(stmrActions);
  } catch (result: any) {
    console.error('[INSERT_ACTION] Error inserting action:', result);
    await this.unviredSDK.logError('stmrDetailsPage', 'insertSTMRFormAction',
      `Inserting STMR Form action into database failed: ${result.message} ${result.error}`);
    throw result;
  }
}


/**
  Fetch Linked Templates with CTA type linked to CTA
 */
async fetchLinkedForms(
  ctaIdArr: string[],
  linkedForms: any[],
  formArray: Array<{ CTA_ID: string; TOPIC_NO: string }>
): Promise<any> {
  let tmp: any = {};
  if (formArray.length > 0) {
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_CTA_TMPLT, { CTA_ID: formArray[0].CTA_ID });
      if (result?.data?.length > 0) {
        if (!linkedForms) linkedForms = [];
        for (let i = 0; i < result.data.length; i++) {
          tmp = { ...result.data[i] };
          tmp['TOPIC_NO'] = formArray[0].TOPIC_NO;
          linkedForms.push(tmp);
        }
      }
      ctaIdArr.splice(0, 1);
      formArray.splice(0, 1);
      return this.fetchLinkedForms(ctaIdArr, linkedForms, formArray);
    } catch (result: any) {
      this.busyIndicator.hideBusyIndicator();
      this.alertService.showAlert('Error', `${result.error} ${result.message}`);
      await this.unviredSDK.logError('stmrDetailsPage', 'fetchLinkedForms',
        `Error while fetching Linked Templates from DB : ${result.message} ${result.error}`);
      throw result;
    }
  } else {
    const topics: STMR_TOPIC[] = this.stmrTopicEntity.map(entity => entity.topic);
    const tmpLinkedForms = JSON.parse(JSON.stringify(linkedForms));
    const tmpStmrTopic = JSON.parse(JSON.stringify(topics));
    this.linkedForms = JSON.parse(JSON.stringify(linkedForms));
    return this.fetchTemplates(tmpLinkedForms, tmpStmrTopic);
  }
}

/**
 Fetch Templates (released version), annotate linkedForms, then insert STMR_FORM
 */
async fetchTemplates(linkedForms: any[], strmrTopic: any[]): Promise<any> {
  if (linkedForms.length > 0) {
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_TEMPLATE_VERSION, { STATUS: 'REL' });
      if (result?.data?.length > 0) {
        this.templates = result.data;
        // Join released versions onto linkedForms
        for (let i = 0; i < result.data.length; i++) {
          for (let j = 0; j < linkedForms.length; j++) {
            if (result.data[i].TMPLT_ID === linkedForms[j].TEMPLATE_ID) {
              linkedForms[j].VER_ID = result.data[i].VER_ID;
              linkedForms[j].CRTD_BY = result.data[i].CRTD_BY;
              linkedForms[j].CRTD_ON = result.data[i].CRTD_ON;
            }
          }
        }
      }
      const tmpLinkedForms = JSON.parse(JSON.stringify(linkedForms));
      this.linkedForms = JSON.parse(JSON.stringify(linkedForms));
      return this.insertSTMRForm(tmpLinkedForms);
    } catch (result: any) {
      this.busyIndicator.hideBusyIndicator();
      this.alertService.showAlert('Error', `${result.error} ${result.message}`);
      await this.unviredSDK.logError('stmrDetailsPage', 'fetchTemplates',
        `Error while fetching Linked Templates from DB : ${result.message} ${result.error}`);
      throw result;
    }
  } else {
    const tmpLinkedForms = JSON.parse(JSON.stringify(linkedForms));
    this.linkedForms = JSON.parse(JSON.stringify(linkedForms));
    return this.insertSTMRForm(tmpLinkedForms);
  }
}

/**
 * Insert STMR_FORM
 * After STMR_FORM, insert STMR_FORM_DATA
 */
async insertSTMRForm(tempTmplt: any[]): Promise<void> {
  if (tempTmplt.length > 0) {
    let itemStmrForm: STMR_FORM = {} as STMR_FORM;
    let itemStmrFormData: STMR_FORM_DATA = {} as STMR_FORM_DATA;
    const formId = this.UtilityService.guid32();
    // STMR_FORM object 
    itemStmrForm.STMR_ID = this.stmrHeader.STMR_ID;
    itemStmrForm.TOPIC_NO = tempTmplt[0].TOPIC_NO;
    itemStmrForm.FORM_ID = formId;
    itemStmrForm.VER_ID = tempTmplt[0].VER_ID;
    itemStmrForm.CRTD_BY = tempTmplt[0].CRTD_BY;
    itemStmrForm.CRTD_ON = moment.utc().unix();
    itemStmrForm.COMPANY = this.stmrHeader.COMPANY;
    itemStmrForm.RIG_NO = this.stmrHeader.RIG_NO;
    itemStmrForm.COMMENTS = '';
    itemStmrForm.FORM_STATUS = AppConstants.VAL_FORM_STATUS.OPEN;
    itemStmrForm.FID = this.stmrHeader.LID;
    itemStmrForm.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
    itemStmrForm.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
    // STMR_FORM_DATA object
    itemStmrFormData.STMR_ID = this.stmrHeader.STMR_ID;
    itemStmrFormData.FORM_ID = formId;
    itemStmrFormData.TOPIC_NO = tempTmplt[0].TOPIC_NO;
    itemStmrFormData.DATA = '';
    itemStmrFormData.FID = this.stmrHeader.LID;
    itemStmrFormData.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
    itemStmrFormData.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
    await this.unviredSDK.logInfo('stmrDetailsPage', 'insertSTMRForm', 'Inserting STMR Form into the database...');
    try {
      await this.unviredSDK.dbInsertOrUpdate(AppConstants.STMR_FORM, itemStmrForm, AppConstants.BOOL_FALSE);
      await this.unviredSDK.logInfo('stmrDetailsPage', 'insertSTMRForm',
        'Done. Inserting STMR Form into the database. Inserting STMR Form data...');
      await this.unviredSDK.dbInsertOrUpdate(AppConstants.STMR_FORM_DATA, itemStmrFormData, AppConstants.BOOL_FALSE);
      await this.unviredSDK.logInfo('stmrDetailsPage', 'insertSTMRForm',
        'Done. Inserting STMR Formdata into the database.');
      tempTmplt.splice(0, 1);
      return this.insertSTMRForm(tempTmplt);
    } catch (result: any) {
      this.busyIndicator.hideBusyIndicator();
      this.alertService.showAlert('Error', `${result.error} ${result.message}`);
      const where = !itemStmrFormData?.FORM_ID ? 'Form Header' : 'Form Header/Data';
      await this.unviredSDK.logError('stmrDetailsPage', 'insertSTMRForm',
        `Inserting ${where} into database failed: ${result.message} ${result.error}`);
      throw result;
    }
  } else {
    // Move to forms tab 
    this.busyIndicator.hideBusyIndicator();
    return;
  }
}
  /**
   * Validation and submitting STMR
   */
async submitSTMR(): Promise<void> {
  console.log('[SUBMIT] Starting submitSTMR...');
  const now = Date.now();
  if (now - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
    console.log('[SUBMIT] Ignoring double tap');
    return;
  }

  this.timestampOfLastTap = now;
  this.isPageActive = false;
  
  try {
    console.log('[SUBMIT] Starting validation...');
    if (!this.validateSTMR(false, true)) {
      console.log('[SUBMIT] Validation failed');
      this.isPageActive = true; // Reset page active state
      return; 
    }

    if (!this.areAllFormsCompleted()) {
      console.log('[SUBMIT] Forms not completed');
      await this.showAlert("Alert", "It seems like some forms are not filled completely.");
      this.isPageActive = true; // Reset page active state
      return; 
    }

    console.log('[SUBMIT] Creating STMR action...');
    let stmrAction = this.createSTMRAction(this.stmrHeader);
    this.stmrActions = [...this.stmrActions, stmrAction];

    console.log('[SUBMIT] Preparing and saving STMR...');
    await this.prepareAndSaveSTMR();

    console.log('[SUBMIT] Submitting to server...');
    await this.submitToServer();

    console.log('[SUBMIT] Successfully submitted to server');
    const successToast = await this.toastController.create({
        message: this.translate.instant('STMR submitted successfully'),
        duration: 1000,
        position: 'bottom'
      });
      await successToast.present();
    await this.goToFormsPage();

  } catch (error: any) {
    console.error('[SUBMIT] Error in submitSTMR:', error);
    this.isPageActive = true;
    // Revert STMR status on error
    this.stmrHeader.STMR_STATUS = AppConstants.VAL_FORM_STATUS.OPEN;
    
    await this.showAlert(
      this.translate.instant("Error"),
      this.translate.instant("Error while submitting STMR to server: ") + this.getErrorMessage(error)
    );
  }
}


  async submitToServer(): Promise<void> {
  console.log('[SERVER] Starting submitToServer...', this.stmrHeader);
  try {
    console.log('[SERVER] Calling syncBackground...');
    console.log('[SERVER] STMR_ID:', this.stmrHeader.STMR_ID);
    console.log('[SERVER] LID:', this.stmrHeader.LID);
    const result = await this.unviredSDK.syncBackground(
      RequestType.RQST,
      { "STMR_HEADER": this.stmrHeader },
      '',
      AppConstants.PA_FORMS_STMR_SUBMIT,
      'STMR',
      this.stmrHeader.LID,
      false
    ); 
    console.log('[SERVER] syncBackground result:', result);
    console.log('[SERVER] result type:', result.type);
    console.log('[SERVER] ResultType.success:', ResultType.success);
    if (result.type === ResultType.success) {
      console.log('[SERVER] Submit successful');
      // Update local STMR with server response
      if (result.data && result.data.STMR && result.data.STMR[0] && result.data.STMR[0].STMR_HEADER) {
        console.log('[SERVER] Updating local STMR with server response...');
        const serverStmrHeader = result.data.STMR[0].STMR_HEADER;
        // Update the STMR_ID with server-generated ID
        if (serverStmrHeader.STMR_ID && serverStmrHeader.STMR_ID !== this.stmrHeader.STMR_ID) {
          console.log('[SERVER] Updating STMR_ID from server:', serverStmrHeader.STMR_ID);
          this.stmrHeader.STMR_ID = serverStmrHeader.STMR_ID;       
          // Also update RIG_TYPE if server corrected it
          if (serverStmrHeader.RIG_TYPE && serverStmrHeader.RIG_TYPE.trim() !== '') {
            this.stmrHeader.RIG_TYPE = serverStmrHeader.RIG_TYPE;
          }          
          // Update database with new STMR_ID
          console.log('[SERVER] Updating database with new STMR_ID...');
          await this.unviredSDK.dbInsertOrUpdate(
            AppConstants.STMR_HEADER,
            this.stmrHeader,
            true
          );
          console.log('[SERVER] Database updated with new STMR_ID');
        }
      }   
      this.unviredSDK.logInfo('StmrDetailsPage', 'submitToServer', 'STMR Form submitted successfully.');
      console.log('[SERVER] submitToServer completed successfully');
    } else {
      console.error('[SERVER] Submit failed with result:', result);
      throw new Error(result.message ?? 'Unknown error');
    }
  } catch (error: any) {
    console.error('[SERVER] Error in submitToServer:', error);
    console.error('[SERVER] Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    this.unviredSDK.logError('StmrDetailsPage', 'submitToServer', `Error while submitting STMR: ${this.getErrorMessage(error)}`);
    throw error;
  }
}


async goToFormsPage(): Promise<void> {
  try {
    // Optional: refresh DB so Forms shows latest
    this.store.dispatch(RigActions.loadAllFormsFromDb());
    this.store.dispatch(RigActions.loadStmrDataAndTopicsFromDb());

    // 1) Navigate first so the underlying page becomes Forms
    await this.ngZone.run(async () => {
      await this.router.navigate(['/forms'], {
        replaceUrl: true,
        state: { refresh: true },
        queryParams: { refresh: Date.now() }
      });
    });

    // 2) Then dismiss all open modals so Forms is revealed
    // let top = await this.modalController.getTop();
    // while (top) {
    //   await this.modalController.dismiss({ refresh: true }, 'confirm');
    //   top = await this.modalController.getTop();
    // }
  } catch {
    window.location.href = '/forms';
  }
}






/**
 * Checks if all forms associated with topics are completed.
 * Completed = P_MODE !== 'D' AND FORM_STATUS is SUBM or SKIP
 */
areAllFormsCompleted(): boolean {
  return this.stmrTopicEntity.every(topic =>
    topic.forms.every((form: any) =>
      form.P_MODE === 'D' ||
      form.FORM_STATUS === AppConstants.VAL_FORM_STATUS.SUBM ||
      form.FORM_STATUS === AppConstants.VAL_FORM_STATUS.SKIP
    )
  );
}

  private createSTMRAction(stmrHeader: { STMR_ID: string; LID: string }): STMR_ACTION {
  this.markSTMRAsUpdated();
  const stmrActionObj = new STMR_ACTION();
  stmrActionObj.STMR_ID = stmrHeader.STMR_ID;
  stmrActionObj.FORM_ID = stmrHeader.STMR_ID;
  stmrActionObj.ACTION_CODE = AppConstants.ACTION_CODE.COMPLETE;
  stmrActionObj.P_MODE = 'A';
  stmrActionObj.FID = stmrHeader.LID;
  stmrActionObj.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
  return stmrActionObj;
}

validateSTMR(noAlert = false, isSubmit = false): boolean {
  // Header validations
  console.log('[VALIDATION] Starting validation, isSubmit:', isSubmit);

  if (!this.stmrHeader.SHIFT) {
    console.log('[VALIDATION] Failed: No shift selected')
    return this.fail('Please select one of the shift types.', noAlert);
  }
  if (!this.stmrHeader.WELL_LOC) {
    return this.fail('Please provide Location.', noAlert);
  }
  if (!this.stmrHeader.WELL_LOC.trim()) {
    return this.fail('Please provide a valid Location.', noAlert);
  }
  if (!this.stmrHeader.CHAIRED_BY) {
    return this.fail('Please provide "Chaired By" name.', noAlert);
  }
  if (!this.stmrHeader.CHAIRED_BY.trim()) {
    return this.fail('Please provide a valid "Chaired By" name.', noAlert);
  }
  if (!this.stmrHeader.OPERATOR) {
    return this.fail('Please provide Operator name.', noAlert);
  }
  if (!this.stmrHeader.OPERATOR.trim()) {
    return this.fail('Please provide a valid Operator name.', noAlert);
  }
  // Crew validations
  if (!this.stmrCrew || this.stmrCrew.length === 0) {
    return this.fail('Please add a minimum of one crew member.', noAlert);
  }
  const activeCrew = this.stmrCrew.filter(c => c.P_MODE !== 'D');
  const nonThirdPartyCrew = activeCrew.filter(c => c.CREW_TYPE !== 'THIRD_PARTY');
  const deletedCrewCount = this.getNoOfDeletedCrew(this.stmrCrew);
  if (this.stmrCrew.length === deletedCrewCount) {
    return this.fail('Please add a minimum of one crew member.', noAlert);
  }
  if (nonThirdPartyCrew.length === 0) {
    return this.fail('Please add at least one non-third-party crew member.', noAlert);
  }
  // Check crew signatures and names
  for (let crew of nonThirdPartyCrew) {
    if (!crew.CREW_SIGN || !crew.CREW_NAME?.trim()) {
      return this.fail('Please capture names & signatures for all crew members.', noAlert);
    }
  }
  // Topics validations
  if (!this.stmrTopicEntity || this.stmrTopicEntity.length === 0) {
    return this.fail('Please add a minimum of one topic.', noAlert);
  }
  const deletedTopicCount = this.getNoOfDeletedTopics(this.stmrTopicEntity);
  if (this.stmrTopicEntity.length === deletedTopicCount) {
    return this.fail('Please add a minimum of one topic.', noAlert);
  }
  // On Submit validations
  if (isSubmit) {
    if (!this.stmrHeader.ONSITE_SUP) {
      return this.fail('Please provide Crew Supervisor Name.', noAlert);
    }  
    if (!this.stmrHeader.ONSITE_SUP.trim()) {
      return this.fail('Please provide a valid Crew Supervisor Name.', noAlert);
    }
    if (!this.stmrHeader.ONSITE_SUP_SIGN) {
      return this.fail('Please provide Crew Supervisor Signature.', noAlert);
    }
  }
  // All validations passed
  return true;
}


// Small helper to avoid repeating
private fail(message: string, noAlert: boolean): false {
  if (!noAlert) {
    this.showAlert('Alert', message);
  }
  return false;
}

  getNoOfDeletedCrew(crewList: any[]): number {
    return crewList.filter(c => c.P_MODE === 'D').length;
  }

  getNoOfDeletedTopics(topicList: any[]): number {
    return topicList.filter(t => t.topic?.P_MODE === 'D').length;
  }

  callbackAfterAddCrew(selectedCrew: CREW_HEADER[]) {
  this.ngZone.run(() => {
    this.stmrCrew = selectedCrew;   
    console.log('Selected Crew:', this.stmrCrew);
  });
}

  isSTMRReadonly(stmrHeader: STMR_HEADER): boolean {
  // STMR is readonly if it’s submitted and not in error state
  return (
    stmrHeader?.STMR_STATUS === 'SUBM' &&
    stmrHeader?.SYNC_STATUS !== AppConstants.SYNC_STATUS.ERROR
  );
}


  getSTMRIDForDisplay(formId: string): string {
    return formId?.startsWith('New') ? 'New' : formId;
  }


getFormattedShiftTime(): string {
  if (!this.stmrHeader?.SHIFT_TIME) return 'Not Set';  
  const timestamp = this.stmrHeader.SHIFT_TIME;
  const date = new Date(timestamp * 1000);  
  if (isNaN(date.getTime())) return 'Invalid Date';  
  // Get individual components
  const month = date.toLocaleString('en-US', { month: 'short' });
  const day = date.getDate().toString().padStart(2, '0');
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');  
  return `${month} ${day} ${year} ${hours}:${minutes}`;
}


getCurrentUnixTime(): number {
  return moment().unix();
}


onModelChange(value: any): void {
  console.log('[StmrDetailsPage] Shift changed:', value);
  this.stmrHeader.SHIFT = value;
  // Mark STMR as dirty (so Save/Save & Exit knows changes exist)
  (this.stmrHeader as any).saved = false;
  this.unviredSDK.logInfo(
    'StmrDetailsPage',
    'onModelChange',
    `Shift updated to ${value} for STMR ${this.stmrHeader?.STMR_ID}`
  );
}


 dispTime(date: string): string {
  if (!date) return '';

  const timestamp = parseInt(date);
  const d = isNaN(timestamp) 
    ? new Date(date)  
    : new Date(timestamp * 1000);  
    
  if (isNaN(d.getTime())) return '';
  const day = d.getDate();
  const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
  const year = d.getFullYear();
  const hours = d.getHours();
  const minutes = d.getMinutes();

  function getOrdinal(n: number): string {
    if (n > 3 && n < 21) return 'th';
    switch (n % 10) {
      case 1: return 'st';
      case 2: return 'nd';
      case 3: return 'rd';
      default: return 'th';
    }
  }

  const paddedMinutes = minutes.toString().padStart(2, '0');

  return `${day}${getOrdinal(day)} ${monthNames[d.getMonth()]} ${year} ${hours}:${paddedMinutes}`;
}

  // Crew callback
callbackAfterCrew(crew: any[]) {
  this.unviredSDK.logInfo("STMRDetails", `callbackAfterCrew() crew members count: ${crew.length}`, "STMRDetailsPage");
  
  this.ngZone.run(() => {
    // PRESERVE existing third party crew members
    const existingThirdPartyCrew = this.stmrCrew.filter(c => 
      c.CREW_TYPE === 'THIRD_PARTY'
    );
    
    // Reset P_MODE for non-deleted, non-third-party items
    const nonThirdPartyCrew = this.stmrCrew.filter(c => c.CREW_TYPE !== 'THIRD_PARTY');
    nonThirdPartyCrew.forEach(member => {
      if (member.P_MODE !== 'D') {
        member.P_MODE = 'M';
      }
    });

    for (const c of crew) {
      let foundIndex = nonThirdPartyCrew.findIndex(x => x.CREW_ID === c.PERSON_NO);
      
      if (foundIndex === -1 && c.isSelected) {
        // Insert new
        this.insertStmrCrewOnly(c);
      } else if (foundIndex !== -1) {
        if (c.isSelected) {
          // Update existing
          this.updateStmrCrewOnly(c);
        } else {
          // Delete existing
          this.deleteStmrCrewOnly(nonThirdPartyCrew[foundIndex], foundIndex);
        }
      }
    }
    
    // Ensure third party crew members are still in the array
    const finalNonThirdPartyCrew = this.stmrCrew.filter(c => c.CREW_TYPE !== 'THIRD_PARTY');
    this.stmrCrew = [
      ...finalNonThirdPartyCrew,
      ...existingThirdPartyCrew
    ];
  });
}

  // Delete stmr crew
  deleteStmrCrewOnly(tempCrew: STMR_CREW, index: number) {
    if (this.stmrCrew[index].CREW_TYPE !== 'THIRD_PARTY') {
      this.stmrCrew[index].P_MODE = 'D';
      this.stmrCrew[index].OBJECT_STATUS = AppConstants.OBJECT_STATUS.DELETE;
      this.stmrCrew[index].CREW_SIGN = '';
      this.markSTMRAsUpdated();
    }
  }

  // Insert stmr crew
  insertStmrCrewOnly(tempCrew: any) {
    const itemStmrCrew = new STMR_CREW();
    itemStmrCrew.STMR_ID = this.stmrHeader.STMR_ID;
    itemStmrCrew.CREW_ID = tempCrew.PERSON_NO || tempCrew.CREW_ID;
    itemStmrCrew.CREW_POS = tempCrew.DESIGNATION || tempCrew.CREW_POS;
    itemStmrCrew.CREW_NAME = tempCrew.USER_NAME || tempCrew.CREW_NAME;
    itemStmrCrew.CREW_SIGN = tempCrew.CREW_SIGN || ''
    itemStmrCrew.FID = this.stmrHeader.LID;
    itemStmrCrew.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
    itemStmrCrew.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
    itemStmrCrew.P_MODE = 'A';
    this.updateSTMRHeaderLastSync(this.stmrHeader);
    this.stmrCrew.push(itemStmrCrew);
  }

  // Topic callback
async callbackAfterTopic(updatedStmrHeader: STMR_HEADER, topicEntity: STMR_TOPIC_ENTITY, stmrActions: STMR_ACTION[]) {
  console.log('[STMR_CALLBACK] callbackAfterTopic called');
  console.log('[DEBUG_CALLBACK] updatedStmrHeader:', JSON.stringify(updatedStmrHeader, null, 2));
  console.log('[DEBUG_CALLBACK] topicEntity:', JSON.stringify(topicEntity, null, 2));
  console.log('[DEBUG_CALLBACK] stmrActions:', JSON.stringify(stmrActions, null, 2));
const existingCrew = [...this.stmrCrew];
// Restore crew data if it was cleared
  if (this.stmrCrew.length === 0 && existingCrew.length > 0) {
    this.stmrCrew = existingCrew;
    console.log('[STMR_CALLBACK] Restored crew data after topic save');
  }
  if (!updatedStmrHeader) {
    console.warn('[STMR_CALLBACK] callbackAfterTopic called with no header');
    this.eventsService.publish('topicSaveError', { message: 'Missing STMR header' });
    return;
  }

  // Update local state immediately
  this.stmrActions = [...(this.stmrActions || []), ...(stmrActions || [])];
  this.stmrHeader = updatedStmrHeader;
  
  if (topicEntity) {
    this.updateTopic(topicEntity);
  }

  try {
    // Save the STMR state locally
    await this.localSaveSTMR();
    console.log('[STMR_CALLBACK] STMR saved successfully');
        this.unviredSDK.logInfo('STMRDetailsPage', 'callbackAfterTopic', 
            'Topic saved successfully to database');
    
    // Publish success event for TopicsPage
    this.eventsService.publish('topicSaveComplete');
  } catch (error: any) {
    console.error('[STMR_CALLBACK] Error during save:', error);
    
    // Publish error event for TopicsPage
    this.eventsService.publish('topicSaveError', { message: error?.message || 'Failed to save topic' });
  }
  console.log('[DEBUG] Topic Entity received from Topics page:', JSON.stringify(topicEntity, null, 2));
  console.log('[DEBUG] Actions received:', JSON.stringify(stmrActions, null, 2));

}


  callbackAfterUserClosedAppInTopicsPage(updatedStmrHeader: STMR_HEADER, topicEntity: STMR_TOPIC_ENTITY, stmrActions: STMR_ACTION[]) {
    this.stmrActions = [...this.stmrActions, ...stmrActions];
    this.stmrHeader = updatedStmrHeader;
    this.updateTopic(topicEntity);
  }
  // Delete Topic
  async deleteStmrTopicOnly(tempTopic: STMR_TOPIC_ENTITY, index: number, fromTopicsScreen: boolean) {
    await this.confirmDeleteTopic(fromTopicsScreen, async () => {
      this.stmrTopicEntity[index].topic.P_MODE = "D";
      this.stmrTopicEntity[index].topic.OBJECT_STATUS = AppConstants.OBJECT_STATUS.DELETE;
      this.stmrTopicEntity[index].forms = this.stmrTopicEntity[index].forms.map((el: any) => ({
        ...el,
        P_MODE: 'D',
        OBJECT_STATUS: AppConstants.OBJECT_STATUS.DELETE
      }));
      this.stmrTopicEntity[index].data = this.stmrTopicEntity[index].data.map((el: any) => ({
        ...el,
        P_MODE: 'D',
        OBJECT_STATUS: AppConstants.OBJECT_STATUS.DELETE
      }));
      this.updateSTMRHeaderLastSync(this.stmrHeader);
    });
  }

  // Confirm delete
  private async confirmDeleteTopic(fromTopicsScreen: boolean, callback: () => void) {
    if (fromTopicsScreen) {
      callback();
    } else {
      const alert = await this.alertController.create({
        header: this.translate.instant("Delete Topic"),
        message: AppConstants.DELETE_FORMS_MSG,
        buttons: [
          {
            text: this.translate.instant('Delete'),
            handler: () => callback()
          },
          {
            text: this.translate.instant('Cancel'),
            role: 'cancel'
          }
        ]
      });
      await alert.present();
    }
  }

// Update Topic
updateTopic(topicEntity: STMR_TOPIC_ENTITY) {
  // Use LID (unique identifier) for finding existing topics
  const topicIndex = this.stmrTopicEntity.findIndex(el => 
     el.topic.LID === topicEntity.topic.LID && el.topic.PMODE !== 'D'
  );
  
  if (topicIndex >= 0) {
    // This is an existing topic being updated - DON'T change TOPIC_NO
    console.log('[UPDATE_TOPIC] Updating existing topic at index:', topicIndex);
    console.log('[UPDATE_TOPIC] Keeping original TOPIC_NO:', this.stmrTopicEntity[topicIndex].topic.TOPIC_NO);
    
    // Preserve the original TOPIC_NO to avoid unique constraint violation
    const originalTopicNo = this.stmrTopicEntity[topicIndex].topic.TOPIC_NO;
    topicEntity.topic.TOPIC_NO = originalTopicNo;
    
    // Update the existing topic
    this.stmrTopicEntity[topicIndex] = topicEntity;
  } else {
    // This is a completely new topic - assign next available TOPIC_NO
    console.log('[UPDATE_TOPIC] Adding new topic with existing TOPIC_NO:', topicEntity.topic.TOPIC_NO); 
    this.stmrTopicEntity.push(topicEntity);
  }

  this.markSTMRAsUpdated();
  this.ngZone.run(() => {
    // Trigger change detection
    this.stmrTopicEntity = [...this.stmrTopicEntity];
  });
}



  // Capture crew sign
  async captureSign(signatureType: string, crew: any) {
    try {
      console.log('STMRDetails - captureSign called with:');
      console.log('  - signatureType:', signatureType);
      console.log('  - crew:', crew);
      console.log('  - crew.CREW_NAME:', crew?.CREW_NAME);
      console.log('  - crew.CREW_TYPE:', crew?.CREW_TYPE);

      if (!crew.CREW_NAME && crew.CREW_TYPE === "THIRD_PARTY") {
        console.log('STMRDetails - Missing crew name for third party');
        this.alertService.showAlert("Alert", "Please enter the name");
        return;
      }
      console.log('STMRDetails - Creating signature modal...');
      const modal = await this.modalController.create({
        component: SignaturePage,
        componentProps: {
          signatureType,
          crew,
          theme: this.styleTheme
        },
        backdropDismiss: false
      });
      console.log('STMRDetails - Modal created successfully');
      modal.onDidDismiss().then((res: any) => {
        try {
          console.log('STMRDetails - Modal dismissed with result:', res);
          const data = res.data;
          if (data?.signatureType && data?.crew) {
            console.log('STMRDetails - Processing signature data:', data.signatureType);
            switch (data.signatureType) {
              case 'ONSITE_SUP_SIGN':
                console.log('STMRDetails - Saving onsite supervisor signature');
                this.stmrHeader.ONSITE_SUP_SIGN = data.CREW_SIGN || "";
                this.stmrHeader.OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;
                this.updateSTMRHeaderLastSync(this.stmrHeader);
                break;
              case 'CREW_SIGN':
                console.log('STMRDetails - Saving crew signature for:', data.crew.CREW_ID);
                const crewIndex = this.stmrCrew.findIndex(c => c.CREW_ID === data.crew.CREW_ID);
                if (crewIndex >= 0) {
                  this.ngZone.run(() => {
                this.stmrCrew[crewIndex].CREW_SIGN = data.CREW_SIGN || "";
                this.stmrCrew[crewIndex].OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;
                this.updateSTMRHeaderLastSync(this.stmrHeader);
              });
            }
            break;
        }
      } else {
        console.log('STMRDetails - No signature data returned from modal');
      }
        } catch (dismissError) {
          console.error('STMRDetails - Error processing modal dismiss:', dismissError);
        }
      });
      console.log('STMRDetails - Presenting modal...');
      await modal.present();
      console.log('STMRDetails - Modal presented successfully');
    } catch (error) {
      console.error('STMRDetails - Error in captureSign:', error);
      if (error instanceof Error) {
        console.error('STMRDetails - Error stack:', error.stack);
        this.alertService.showAlert('Error', 'Failed to open signature capture: ' + error.message);
      } else {
        this.alertService.showAlert('Error', 'Failed to open signature capture: ' + String(error));
      }
    }
  }

  // Capture onsite sign
  async captureOnsiteSign(signatureType: string) {
    try {
      console.log('STMRDetails - captureOnsiteSign called with:', signatureType);
      console.log('STMRDetails - Current supervisor name:', this.stmrHeader.ONSITE_SUP);
      console.log('STMRDetails - Current supervisor signature exists:', !!this.stmrHeader.ONSITE_SUP_SIGN);
      let contCapture = false;
      let userName = "";
      let userSign = "";
      switch (signatureType) {
        case 'ONSITE_SUP_SIGN':
          if (this.stmrHeader.ONSITE_SUP && this.stmrHeader.ONSITE_SUP.trim()) {
            contCapture = true;
            userName = this.stmrHeader.ONSITE_SUP;
            userSign = this.stmrHeader.ONSITE_SUP_SIGN || "";
            console.log('STMRDetails - Supervisor signature capture approved for:', userName);
          } else {
            console.log('STMRDetails - Supervisor name missing, showing alert');
            this.alertService.showAlert("Error", "Enter Crew Supervisor Name");
            return;
          }
          break;
        default:
          console.error('STMRDetails - Unknown signature type:', signatureType);
          return;
      }
      if (contCapture) {
        const crew = new STMR_CREW();
        crew.CREW_NAME = userName;
        crew.CREW_SIGN = userSign;
        crew.CREW_POS = this.translate.instant("Crew Supervisor");
        console.log('STMRDetails - Calling captureSign for supervisor with crew object:', crew);
        await this.captureSign(signatureType, crew);
      }
    } catch (error) {
      console.error('STMRDetails - Error in captureOnsiteSign:', error);
      if (error instanceof Error) {
        this.alertService.showAlert('Error', 'Failed to capture supervisor signature: ' + error.message);
      } else {
        this.alertService.showAlert('Error', 'Failed to capture supervisor signature');
      }
    }
  }

  
  getNoOfNonThirdPartyCrewMembers(stmrCrew: any[]): number {
  if (!stmrCrew) return 0;

  return stmrCrew.filter(crew =>
    crew.CREW_TYPE !== 'THIRD_PARTY' &&
    crew.P_MODE !== 'D' &&
    crew.CREW_NAME && crew.CREW_NAME.trim().length > 0
  ).length;
}

  // Add a new 3rd party crew member
  addThirdParty() {
  this.ngZone.run(() => {
    // Add first third party crew member
    let firstThirdPartyCrewHeader = this.getEmptyThirdPartyCrewHeader();
    this.stmrCrew.push(firstThirdPartyCrewHeader);
    
    // Add second third party crew member
    let secondThirdPartyCrewHeader = this.getEmptyThirdPartyCrewHeader();
    this.stmrCrew.push(secondThirdPartyCrewHeader);
    
    console.log('Added two third party crew members. Total crew:', this.stmrCrew.length);
  });
}

  // helper to create empty crew object
  getEmptyThirdPartyCrewHeader(): STMR_CREW {
    const crew = new STMR_CREW();
    crew.STMR_ID = this.stmrHeader.STMR_ID;
    crew.CREW_ID = this.UtilityService.guid32(); 
    crew.CREW_NAME = '';
    crew.CREW_POS = '';
    crew.CREW_SIGN = '';
    crew.CREW_TYPE = 'THIRD_PARTY';
    crew.P_MODE = 'A';
    crew.FID = this.stmrHeader.LID;
    crew.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
    crew.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
    return crew;
  }

  // Capture signature of 3rd party crew member
  async captureSignOfThirdParty(signatureType: string, index: number) {
    let itemStmrCrew: STMR_CREW = new STMR_CREW();
    if (!this.stmrCrew[index].CREW_NAME) {
      this.showAlert('Alert', 'Please enter the name');
      return;
    }
    this.ngZone.run(() => {
      itemStmrCrew.CREW_NAME = this.stmrCrew[index].CREW_NAME.trim();
      this.captureSignForThirdParty(signatureType, itemStmrCrew, itemStmrCrew.CREW_NAME, index);
    });
  }

  async captureSignForThirdParty(signatureType: string, itemStmrCrew: STMR_CREW, UserName: string, index: number) {
    const modal = await this.modalController.create({
      component: SignaturePage,
      componentProps: {
        signatureType,
        crew: itemStmrCrew,
        theme: this.styleTheme,
      },
      backdropDismiss: false,
    });
    await modal.present();
    const { data } = await modal.onDidDismiss();
    if (data?.signatureType && data?.crew) {
      if (data.signatureType === 'CREW_SIGN') {
        this.ngZone.run(() => {
          this.stmrCrew[index].STMR_ID = this.stmrHeader.STMR_ID;
          this.stmrCrew[index].CREW_POS = '';
          this.stmrCrew[index].CREW_NAME = UserName;
          this.stmrCrew[index].CREW_SIGN = data.CREW_SIGN || '';
          this.stmrCrew[index].FID = this.stmrHeader.LID;
          this.stmrCrew[index].CREW_TYPE = 'THIRD_PARTY';
          this.stmrCrew[index].SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
          this.stmrCrew[index].OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
          this.stmrCrew[index].P_MODE = 'A';
          this.updateSTMRHeaderLastSync(this.stmrHeader);
        });
      }
    }
  }

  // Handle third party crew name changes
  onThirdPartyCrewNameChange(crew: STMR_CREW, index: number) {
    if (crew.CREW_NAME && crew.CREW_NAME.trim().length > 0) {
      // Update the crew member properties when name is entered
      crew.STMR_ID = this.stmrHeader.STMR_ID;
      crew.FID = this.stmrHeader.LID;
      crew.CREW_TYPE = 'THIRD_PARTY';
      crew.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
      crew.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
      crew.P_MODE = 'A';
      this.markSTMRAsUpdated();
    }
  }

  getNoOfThirdPartyCrewMembers(array: STMR_CREW[]) {
    return array.filter(c => c.P_MODE !== 'D' && c.CREW_TYPE === 'THIRD_PARTY').length;
  }

  // Remove crew member
  async removeCrewMember(crew: STMR_CREW) {
    const alert = await this.alertController.create({
      header: 'Delete Crew',
      message: AppConstants.DELETE_FORMS_MSG,
      buttons: [
        { text: 'Cancel', role: 'cancel' },
        {
          text: 'Delete',
          handler: () => {
            this.ngZone.run(() => {
              let index = this.stmrCrew.indexOf(crew);
              this.stmrCrew[index].P_MODE = 'D';
              this.stmrCrew[index].OBJECT_STATUS = AppConstants.OBJECT_STATUS.DELETE;
              this.stmrCrew[index].CREW_SIGN = '';
              this.markSTMRAsUpdated();
            });
          },
        },
      ],
    });
    await alert.present();
  }

  
  markSTMRAsPristine() {
    this.isSTMRUpdated = false;
    this.ngZone.run(() => {
      this.isSTMRComplete = this.validateSTMR(true, true);
    });
  }
  markSTMRAsUpdated() {
    this.isSTMRUpdated = true;
    this.ngZone.run(() => {
      this.isSTMRComplete = this.validateSTMR(true, true);
    });
  }
    async showAlert(header: string, message: string) {
    const a = await this.alertController.create({ header, message, buttons: ['OK'] });
    await a.present();
  }

  public updateSTMRHeaderLastSync(stmrHeader: STMR_HEADER): void {
  this.store.select(selectPrefilledData).pipe(take(1)).subscribe(data => {
    if (!data) return; // guard [4]
    this.stmrHeader = JSON.parse(JSON.stringify(this.stmrHeader));
    (this.stmrHeader as any).LAST_SYNC_USER = data.USER_ID; // metadata [4]
    (this.stmrHeader as any).SUBM_BY = data.USER_ID; // submitter [4]
    this.stmrHeader.TIME_ZONE = this.UtilityService.getTimezone(); // tz [4]
    this.stmrHeader.LAST_SYNC_TIME = moment.utc().unix(); // critical for UI refresh [4]
    this.stmrHeader.OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY; // mark dirty [4]
    this.markSTMRAsUpdated(); // local state [4]
  });
}


updateStmrCrewOnly(crew: any): void {
  this.ngZone.run(() => {
    for (let j = 0, jLen = this.stmrCrew.length; j < jLen; j++) {
      if (crew.PERSON_NO === this.stmrCrew[j].CREW_ID) {
        this.stmrCrew[j].CREW_NAME = crew.USER_NAME;
        this.stmrCrew[j].STMR_ID = this.stmrHeader.STMR_ID;
        this.stmrCrew[j].CREW_POS = crew.DESIGNATION;
        this.stmrCrew[j].CREW_SIGN = this.stmrCrew[j].CREW_SIGN;
        this.stmrCrew[j].FID = this.stmrHeader.LID;
        this.stmrCrew[j].SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
        this.stmrCrew[j].OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;
        this.stmrCrew[j].P_MODE = 'M';
      }
    }
    this.markSTMRAsUpdated();
  });
}

private getErrorMessage(error: any): string {
  if (!error) return 'Unknown error occurred';
  if (typeof error === 'string') return error;
  if (error.message) return error.message;
  if (error.error) return error.error;
  if (error.code && error.message) return `${error.code}: ${error.message}`;
  if (error.data && error.type) return `${error.type}: ${error.data}`;
  const keys = Object.keys(error);
  if (keys.length > 0) {
    return keys.map(key => `${key}: ${error[key]}`).join(', ');
  }
  return 'Error details not available';
}

// method to check for network errors
private isNetworkError(error: any): boolean {
  if (!error) return false;

  if (!navigator.onLine) {
    console.log('[NETWORK] Browser reports offline');
    return true;
  }
  
  const errorMessage = this.getErrorMessage(error).toLowerCase();
  console.log('[NETWORK] Checking error message:', errorMessage);
  
  const networkKeywords = [
    'network', 'connection', 'timeout', 'offline',
    'unreachable', 'no internet', 'connectivity',
    'net::', 'failed to fetch', 'network request failed',
    'connection refused', 'host unreachable', 'dns'
  ];
  
  const hasNetworkKeyword = networkKeywords.some(keyword => errorMessage.includes(keyword));
  
  const hasNetworkCode = error.code === 'NETWORK_ERROR' || 
                        error.code === 'TIMEOUT' ||
                        error.type === 'network' ||
                        error.name === 'NetworkError';
  
  const isCordovaNetworkError = error.type === ResultType.error && 
                               (errorMessage.includes('connection') || errorMessage.includes('network'));
  
  const isNetworkError = hasNetworkKeyword || hasNetworkCode || isCordovaNetworkError;
  
  console.log('[NETWORK] Network error detection result:', {
    hasNetworkKeyword,
    hasNetworkCode,
    isCordovaNetworkError,
    isNetworkError,
    navigatorOnline: navigator.onLine
  });
  
  return isNetworkError;
}


}