import { Component, CUSTOM_ELEMENTS_SCHEMA, NgZone } from '@angular/core';
import { AlertController } from '@ionic/angular';
import {  Platform, LoadingController, PopoverController, IonRouterOutlet } from '@ionic/angular/standalone';
import { LoginListenerType, LogLevel, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { TranslateService } from '@ngx-translate/core';
import { Router, RouterModule } from '@angular/router';
import { AppConstants } from './constants/appConstants';
import { SideMenuComponent } from './components/side-menu/side-menu.component';
import { NotificationService } from './notification.service';
import { Store } from '@ngrx/store';
import * as RigActions from 'src/app/store/store.actions';
import {  filter, Observable, take } from 'rxjs';
import { RIG_HEADER } from 'src/models/RIG_HEADER';
import { DataService } from './services/data.service';
import { SynclogicService } from './services/synclogic.service';
import { selectProgressPercentage, selectRigData, selectRigLoadedFromDb } from './store/store.selector';

// import { ReleaseNotesService } from './services/release-notes.service';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  imports: [ SideMenuComponent, RouterModule, IonRouterOutlet],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  
})
export class AppComponent {
  
      rigData$!: Observable<RIG_HEADER| null>;
 
     url: string = '';
  loginParameters: any = { };
  constructor(
    public platform: Platform,
    private router: Router,
    public ngZone: NgZone,
    private unviredSDK: UnviredCordovaSDK,
    private alertController: AlertController,
    private loadingController: LoadingController,
    private translate: TranslateService,
    private notifService: NotificationService,
    private popoverController: PopoverController,
    private store: Store,
    private dataService: DataService,
    
    // private releaseNotesService: ReleaseNotesService,
  ) {
    console.log('[AppComponent] constructor called');
    this.initializeApp();
    
    this.unviredSDK.logError('appcomponent', 'constructor','test log')
  console.log('[AppModule] Root store instance:', store);

  }

   async initializeApp() {
    console.log('[AppComponent] initializeApp called');
    console.log('this.login params iss' , this.loginParameters)
   this.platform.ready().then(async () => {
    // this.navigateToLogin()
  



    this.notifService.init()
    const splash = document.getElementById('splash-screen');
    if (splash) splash.style.display = 'none';

     this.url = 'https://umpdev.pd.com:8443'
    this.loginParameters= {
      appName: AppConstants.APPLICATION_NAME,
      metadataPath: 'assets/metadata.json',
      url: this.url,
      company: AppConstants.COMPANY_NAME,
      loginType: 'SAML2',
      appVersion: AppConstants.RELEASE_NUMBER,
      autoSyncTime: 15 * 60 + '' // 15 minutes
    };
    
    console.log('this.login params in app component' , this.loginParameters)
     await this.checkAuthentication();
  }
)

// this.dataService.getReleaseNotesFromServer();
//             this.releaseNotesService.updateCount();    
  }

  setupGlobalErrorHandling() {
    // Catch synchronous errors
    var that = this
    window.onerror = function (message, source, lineno, colno, error) {
      console.error("Global error caught:", {
        message,
        source,
        lineno,
        colno,
        error
      });

      that.unviredSDK.logError('GlobalErrorHandler', 'window.onerror', JSON.stringify({ message, source, lineno, colno, error }));
      return false; // returning true prevents the default browser handling
    };

    // Catch unhandled Promise rejections
    window.addEventListener("unhandledrejection", function (event) {
      console.error("Unhandled promise rejection:", event.reason);

      that.unviredSDK.logError('GlobalErrorHandler', 'unhandledrejection', JSON.stringify(event.reason));
      event.preventDefault(); // optional: stops the default console warning
    });
  }

  async checkAuthentication() {
    this.unviredSDK.logInfo('appcomponent', 'checkAuth', "APP LOGIN PARAMS: " + JSON.stringify(this.loginParameters) )
    console.log('Checking authentication...');
    console.log('Login parameters in app component:', JSON.stringify(this.loginParameters));
    try {
    
      const loginResult = await this.unviredSDK.login(this.loginParameters);
      console.log('Login result:', loginResult.type , JSON.stringify(loginResult.type));
      this.unviredSDK.logInfo('Loginpage ' , 'checkAuthentication' , `${loginResult.type}` )
      switch (loginResult.type) {
        case LoginListenerType.auth_activation_required:
          console.log('Auth activation required');
          this.hideBusyIndicator();
            this.navigateToLogin();
          break;

        case LoginListenerType.login_success:
          console.log('Login successful');
          this.hideBusyIndicator();
          this.navigateToHome();
          break;

        default:
          console.log('Unexpected login result type:', loginResult.type , loginResult.message);
          this.hideBusyIndicator();
          this.navigateToLogin();
          break;
      }
    } catch (error: any) {
      console.error('Login failed:', error);
      this.hideBusyIndicator();
      this.navigateToLogin();
    }
  }





  async navigateToHome() {
    console.log('navigateToHome called')       
  
    this.router.navigate(['/forms']);

  }
  
  
  

   navigateToLogin() {
    console.log('navigate to login in app component')
    this.ngZone.run(() => {
      this.router.navigate(['/login']);
    });
  }
  async showErrorAlert(title: string, message: string) {
    const alert = await this.alertController.create({
      header: title,
      message,
      buttons: [this.translate.instant('OK')],
    });
    await alert.present();
  }

  hideBusyIndicator() {
    if (this.loadingController) {
      this.loadingController.dismiss();
     
    }
  }


}
