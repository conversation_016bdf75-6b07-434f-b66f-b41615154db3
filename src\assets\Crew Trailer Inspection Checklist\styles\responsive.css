/* Hide mobile content */
@media (min-width: 769px) {
  .hidden-print .desktop-only {
    display: block !important;
  }
  .hidden-print .mobile-only {
    display: none !important;
  }
}

/* Show mobile content */
@media (max-width: 768px) {
  .hidden-print .desktop-only {
    display: none !important;
  }
  .hidden-print .mobile-only {
    display: block !important;
  }
}

.signcontainer {
  height: 220px !important;
  width: 100% !important;
  max-width: 568px !important;
}

.hidden-print .section-padding {
  padding: 5px;
}

.hidden-print .section-padding-top {
  padding-top: 5px;
}

.hidden-print .font-bold {
  font-weight: bold;
}

.hidden-print .margin-top-10 {
  margin-top: 10px;
}

.hidden-print .middle-space-between {
  vertical-align: middle;
  justify-content: space-between;
}

.hidden-print .flex-space-around {
  display: flex;
  justify-content: space-around;
}

.hidden-print .section-padding-bottom {
  padding-bottom: 5px;
}

.hidden-print .flex-1 {
  flex: 1;
}

.hidden-print .margin-left-5 {
  margin-left: 5px;
}

.hidden-print .section-padding-horizontal {
  padding-left: 5px;
  padding-right: 5px;
}

.hidden-print .section-padding-horizontal-10 {
  padding-left: 10px;
  padding-right: 10px;
}

.hidden-print .section-header-space-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.hidden-print .justify-right {
  justify-content: flex-end;
}

.hidden-print .justify-center {
  justify-content: center;
}

.hidden-print .button-row-center {
  display: flex;
  justify-content: center;
  padding-top: 5px;
}

.hidden-print .outline-container-black {
  border: 1px solid black;
  padding: 10px;
  border-radius: 2px;
}

.hidden-print .outline-container-grey {
  border: 1px solid #555;
  padding: 10px;
  border-radius: 2px;
}

.hidden-print .custom-divider {
  border-bottom: 1px solid #555;
  height: 1.5em;
  margin: 0;
}

.hidden-print .remove-icon-absolute {
  position: absolute;
  z-index: 100;
  padding-right: 50px;
}

.hidden-print div.danger {
  background-color: #f2dede;
}

.hidden-print div.danger:hover {
  background-color: #ebcccc;
}

.hidden-print .side-by-side-fields {
  display: flex;
  gap: 20px;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.hidden-print .field-wrapper {
  flex: 1 1 45%;
  display: flex;
  flex-direction: column;
}

/* EXISTING table styles made RESPONSIVE */
@media (max-width: 768px) {
  .hidden-print .table-responsive {
    overflow-x: auto;
  }

  .hidden-print .table,
  .hidden-print .table tbody,
  .hidden-print .table tr,
  .hidden-print .table td,
  .hidden-print .table.table-bordered,
  .hidden-print .table.table-bordered tbody,
  .hidden-print .table.table-bordered tr,
  .hidden-print .table.table-bordered td {
    display: block;
    width: 100%;
  }

  .hidden-print .table thead,
  .hidden-print .table.table-bordered thead {
    display: none;
  }

  .hidden-print th {
    border: none !important;
  }

  .hidden-print th,
  .hidden-print td {
    white-space: normal !important;
    word-break: break-word;
    overflow-wrap: break-word;
    overflow-wrap: anywhere;
    max-width: 100%;
    box-sizing: border-box;
  }

  .hidden-print .table tr,
  .hidden-print .table.table-bordered tr {
    padding: 10px;
    border: none;
    display: block;
    border-bottom: 1px solid #ccc;
  }

  .hidden-print .table tr:last-child,
  .hidden-print .table.table-bordered tr:last-child {
    border-bottom: none;
  }

  .hidden-print .table td,
  .hidden-print .table.table-bordered td {
    word-break: break-word;
    white-space: normal;
    padding: 10px;
    position: relative;
    padding-left: 10px;
    border: none;
  }

  .hidden-print .table.ltr td,
  .hidden-print .table.table-bordered.ltr td {
    text-align: left;
  }

  .hidden-print .table.rtl td,
  .hidden-print .table.table-bordered.rtl td {
    text-align: right;
  }

  .hidden-print .table:not(.table-compact-mobile) td::before,
  .hidden-print .table.table-bordered:not(.table-compact-mobile) td::before,
  .hidden-print div[data-label]::before {
    position: absolute;
    top: -5px;
    /* left: 10px; */
    /* padding-right: 10px; */
    white-space: nowrap;
    font-weight: bold;
    content: attr(data-label);
  }

  .hidden-print .table-compact-borderless-mobile td::before {
    left: 0 !important;
  }

  .hidden-print .btn-group-justified {
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between;
    width: 100% !important;
  }

  .hidden-print .btn-group-justified > .btn,
  .hidden-print .btn-group-justified > .btn-group {
    flex: 1 1 0%;
    display: flex !important;
  }

  .hidden-print .btn-group-justified > .btn-group .btn {
    flex: 1 1 0%;
    width: 100% !important;
  }

  .hidden-print .btn-group-justified > .btn-group .dropdown-menu {
    left: auto !important;
    right: auto !important;
  }

  .hidden-print .icon-selected {
    color: #004c80;
    font-size: x-large;
    padding-left: 5px;
    cursor: pointer;
  }

  .btn-group-justified span {
    width: 100%;
  }
}

/* NEW table styles for RESPONSIVE */
@media (min-width: 769px) {
  /* Desktop */
  .hidden-print .responsive-cell.empty-cell::after {
    content: "";
    display: none;
  }

  .hidden-print .show-on-mobile {
    display: none !important;
  }

  .hidden-print .input-group {
    width: 100% !important;
  }

  .hidden-print .text-center-desktop {
    text-align: center;
  }
}

@media (max-width: 768px) {
  /* Mobile */
  .hidden-print td.center-text {
    text-align: revert;
  }

  .hidden-print .responsive-cell.empty-cell::after {
    content: attr(empty-text);
    display: inline;
    font-style: italic;
    color: gray;
  }

  .hidden-print .hide-on-mobile,
  .hidden-print tr.hide-on-mobile,
  .hidden-print td.hide-on-mobile,
  .hidden-print th.hide-on-mobile {
    display: none !important;
  }

  .hidden-print .show-on-mobile,
  .hidden-print tr.show-on-mobile,
  .hidden-print td.show-on-mobile,
  .hidden-print th.show-on-mobile {
    display: inline !important;
  }

  .hidden-print .table thead.show-header,
  .hidden-print .table.table-bordered thead.show-header {
    display: block;
    width: 100%;
    padding: 5px;
  }

  .hidden-print .table thead.show-header tr,
  .hidden-print .table.table-bordered thead.show-header tr {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .hidden-print .table thead.show-header th:first-child,
  .hidden-print .table.table-bordered thead.show-header th:first-child {
    flex: 1;
  }

  .hidden-print .table.ltr thead.show-header th:first-child,
  .hidden-print .table.table-bordered.ltr thead.show-header th:first-child {
    text-align: left;
  }

  .hidden-print .table.rtl thead.show-header th:first-child,
  .hidden-print .table.table-bordered.rtl thead.show-header th:first-child {
    text-align: right;
  }

  .hidden-print .table thead.show-header th:last-child,
  .hidden-print .table.table-bordered thead.show-header th:last-child {
    flex: 0 0 auto;
    text-align: center;
  }

  .hidden-print .table-compact-borderless-mobile,
  .hidden-print .table-compact-borderless-mobile thead,
  .hidden-print .table-compact-borderless-mobile tbody,
  .hidden-print .table-compact-borderless-mobile tr,
  .hidden-print .table-compact-borderless-mobile td,
  .hidden-print .table-compact-borderless-mobile th {
    border: none !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .hidden-print .table-compact-borderless-mobile tr {
    border-bottom: 1px solid #e0e0e0 !important;
  }

  .hidden-print .table-compact-borderless-mobile tr:last-child {
    border-bottom: none !important;
  }

  .hidden-print .table tr.table-compact-mobile,
  .hidden-print .table.table-bordered tr.table-compact-mobile {
    padding: 3px 0;
    border: none;
    display: block;
    border-bottom: 1px solid #ccc;
  }

  .hidden-print .table.table-compact-mobile tbody tr,
  .hidden-print .table.table-bordered.table-compact-mobile tbody tr {
    padding: 10px 0 0 0;
    border: none;
    display: block;
    border-bottom: 1px solid #ccc;
  }

  .hidden-print .table.table-compact-mobile > tbody > tr > td,
  .hidden-print .table.table-bordered.table-compact-mobile > tbody > tr > td {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }

  .hidden-print .table tr.table-compact-mobile td,
  .hidden-print .table.table-bordered tr.table-compact-mobile td {
    padding: 3px 0;
  }

  .hidden-print .table.table-compact-mobile tr td,
  .hidden-print .table.table-bordered.table-compact-mobile tr td {
    padding: 3px 0;
  }

  .hidden-print .responsive-checklist-row {
    display: flex !important;
    flex-wrap: nowrap;
    align-items: stretch;
    width: 100%;
    border-bottom: 1px solid #ccc;
  }

  .hidden-print .responsive-checklist-row > td {
    padding: 0;
    border: none !important;
    position: relative;
    box-sizing: border-box;
    width: auto !important;
    max-width: 100%;
  }

  .hidden-print .responsive-checklist-row > td::before {
    content: none !important;
  }

  .hidden-print .responsive-checklist-box {
    height: 100%;
    display: flex;
    align-items: center;
    padding: 10px;
  }

  .hidden-print .responsive-checklist-content {
    flex: 1 1 auto;
    padding: 10px;
  }

  .hidden-print .responsive-checklist-row.danger .responsive-checklist-box,
  .hidden-print .responsive-checklist-row.danger .responsive-checklist-content {
    background-color: #f2dede;
  }

  .hidden-print
    .responsive-checklist-row.danger:hover
    .responsive-checklist-box,
  .hidden-print
    .responsive-checklist-row.danger:hover
    .responsive-checklist-content {
    background-color: #ebcccc;
  }

  .hidden-print .responsive-checklist-row.danger .responsive-checklist-box,
  .hidden-print
    .responsive-checklist-row.danger:hover
    .responsive-checklist-box {
    background-color: transparent;
  }

  .hidden-print table.table-responsive-checklist {
    border-collapse: separate;
    border-spacing: 0 10px;
  }

  .hidden-print table.table-responsive-checklist tbody tr {
    display: flex !important;
    flex-wrap: nowrap;
    align-items: stretch;
    width: 100%;
    border-bottom: 1px solid #ccc;
  }

  .hidden-print table.table-responsive-checklist tbody tr > td:first-child {
    flex: 0 0 auto;
  }

  .hidden-print table.table-responsive-checklist tbody tr > td:nth-child(2) {
    flex: 1 1 auto;
  }

  .hidden-print table.table-responsive-checklist tbody tr > td {
    padding: 0;
    border: none !important;
    position: relative;
    box-sizing: border-box;
    width: auto !important;
    max-width: 100%;
  }

  .hidden-print table.table-responsive-checklist tbody tr > td::before {
    content: none !important;
  }

  .hidden-print table.table-responsive-checklist tbody tr.danger td {
    background-color: #f2dede;
  }

  .hidden-print table.table-responsive-checklist tbody tr.danger:hover td {
    background-color: #ebcccc;
  }

  .hidden-print .textarea-mobile-250 {
    min-height: 60px;
    height: 250px;
    max-height: 250px;
  }

  .hidden-print .icon-inline-fixed-mobile {
    position: absolute;
    z-index: 100;
    right: 15px;
    top: -70px;
  }

  .hidden-print .icon-inline-mobile {
    position: absolute;
    z-index: 100;
    right: 15px;
    top: -100px;
  }

  .hidden-print
    .row:has(> .icon-on-top-mobile > .icon-on-top-mobile.ng-valid)
    .icon-inline-mobile {
    top: -80px;
  }

  .hidden-print .reverse-header-mobile tr > th:first-child,
  .hidden-print .reverse-header-mobile tr > td:first-child,
  .hidden-print .reverse-header-mobile > th:first-child,
  .hidden-print .reverse-header-mobile > td:first-child {
    order: 2;
    text-align: right !important;
  }

  .hidden-print .padding-right-20 {
    padding-right: 20px;
  }

  .hidden-print .mobile-width-85 {
    width: 85%;
  }

  .hidden-print .padding-horizontal-10-sm {
    padding-left: 10px;
    padding-right: 10px;
  }
}

.hidden-print .text-align-left {
  text-align: left !important;
}

.hidden-print .wrap-text {
  white-space: normal !important;
  word-break: break-word;
  text-align: center;
}

input[type="date"], input[type="time"] {
  padding: 0.375rem 0.75rem;
  line-height: 1.5;
  height: 38px;
  box-sizing: border-box;
}

.hidden-print .input-group > button.input-group-addon {
  padding: 6px 12px;
  height: 38px;
  line-height: 1.42857143;
  white-space: nowrap;
}
