angular.module('PDTest')
.factory('ModelFactory', function ($filter) {

    return {
        form: function() {
            var mode = false;
            if(typeof debugMode !== 'undefined') { mode = debugMode; }
            var form = {
                title: 'Crew Trailer Inspection Checklist',
                usage: 'The Crew Trailer inspection to be used as a checklist to inspect the crew trailers on location as required.',
                version: '1.00',
                releaseDate: 'May 06 2025',
                allowNotifications: true,
                mainTable: 'CrewTrailerNew', 
                debug: mode
            };
            return form;
        },

        sections: function () {
            var sections = []
			sections.push({ value: 'topSection', name: 'Top' });
			sections.push({ value: 'section1', name: 'Checklist' });
			//sections.push({ value: 'section3', name: 'Signatures' });
			sections.push({ value: 'section4', name: 'Corrective Actions' });

            return sections;
        },

        getLocalDateTzOffset: function () {
            var date = new Date();
            var tzoffset = -(date.getTimezoneOffset()) / 60;
            var tzabbr = date.toTimeString().match(/\([a-z ]+\)/i);
            var tzname = undefined;
            if (tzabbr && tzabbr[0]) {
                tzname = tzabbr[0];
                tzabbr = tzabbr[0].match(/[A-Z]/g);
                tzabbr = tzabbr ? tzabbr.join('') : undefined;
            } else {
                tzabbr = timeString.match(/[A-Z]{3,5}/g);
                tzabbr = tzabbr ? tzabbr[0] : undefined;
            }
            if (tzabbr === 'GMT') {
                tzabbr = undefined;
            }

            return {
                date: date,
                tzname: tzname,
                tzabbr: tzabbr,
                tzoffset: tzoffset
            };
        },

        default: function (lists) {
            // get default operator
            var operator = '';
            if((lists.OPERATOR) && (lists.OPERATOR.length !== 0)) { operator = lists.OPERATOR[0].NAME; }

            var date_info = this.getLocalDateTzOffset();
            var data = {
                ParentReferenceNo: '',
                ReferenceNo: '',
                RigNumber: lists.RIG_NO,
                RigManager: '',
                CompanyName: lists.COMPANY,
                CompanyCode: lists.COMP_CODE,
                Operator: operator,
                InspType: 'Monthly / New Location',
                Position: '',
                Driller1: '',
                CompletedBy: '',
                AdditionalComments: '',
                CreateDate: date_info.date,
                CreateDateTzName: date_info.tzname,
                CreateDateTzAbbr: date_info.tzabbr,
                CreateDateTzOffest: date_info.tzoffset,
                CreatedBy: lists.USER_NAME,
                UpdateDate: undefined,
                UpdatedBy: undefined,

                SignOffRigManager: '',
                SignOffRigManagerSignature: undefined,
                SignOffCompletedBy: '',
                SignOffCompletedByPosition: '',
                SignOffCompletedBySignature: undefined,
                SignOffDrillerManager: '',
                SignOffDrillerManagerSignature: undefined,

            };

            return {
				CrewTrailerNew: data, 
                CrewTrailerNewMonthly: [this.getCrewTrailerNewDefaultData()], 
                FormNotification: [],
                CanBeSubmitted: false
            };
		},

        newDocumentSignoff: function() {
            return {
                CompletedBy: '',
                CompletedByPosition: '',
                CompletedBySignature: undefined,
            }
        },

        newDrillerSignoff: function(){
            return {
                SignOffDrillerManagerNew: '',
                SignOffDrillerManagerSignatureNew: undefined,
            }
        },

        newDriller: function(){
            return{
                DrillerNew: ''
            }
        },


        getCrewTrailerNewDefaultData: function() { 
            return {
				crewinspectchklst_visible: true,
				
               
            };
        },

        positions: function () {
            if (
                typeof prefill !== 'undefined' &&
                prefill.MASTER_DATA &&
                Array.isArray(prefill.MASTER_DATA.CREW_POSITION)
            ) {
                return prefill.MASTER_DATA.CREW_POSITION;
            }
            return [];
        },

        positionsShop: function () {
            if (
                typeof prefill !== 'undefined' &&
                prefill.MASTER_DATA &&
                Array.isArray(prefill.MASTER_DATA.CREW_POSITION_SHOP)
            ) {
                return prefill.MASTER_DATA.CREW_POSITION_SHOP;
            }
            return [];
        },

		// this is a repeater row that doesn't follow the usual pattern of 1 schema field per row, so it has to be handled
		// a bit differently
		bopAcFields: function() {
			return [
				'bopAc_AccumulatorPressure',
				'bopAc_Precharge',
				'bopAc_Before',
				'bopAc_After',
				'bopAc_PartFunctionTests',
				'bopAc_NitrogenBottles',
				'bopAc_BackupNitBottles',
				'bopAc_BOPLabeled',
				'bopAc_BOPComment',
				'bopAc_Oil',
				'bopAc_OilComment',
				'bopAc_PrechargePump'
			];
		},


		// there is a strange requirement on this form for both form sections, both with different names,
		// to use the same set of checklist items. so both lists here are identical
        FormSectionData: function() {
            return [
                {
                    section_name: 'Monthly / New Location',
                    table: 'CrewTrailerNewMonthly', 
                    groups: [
                        { groupName: 'Crew Trailer Inspection Checklist', prop: 'crewinspectchklst_visible' },
                      
                    ],
                    line_items: [
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_damagedwindows', label: 'Are there any damaged windows', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_platformgood', label: 'Platform to get into the house in good condition', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_trailergrounded', label: 'Trailer is bonded and grounded', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_watersupply', label: 'Water supply lines installed correctly', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_watertested', label: 'Has water been tested?', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_gfcibreakers', label: 'Are there GFCI Breakers', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_electricaloutlets', label: 'Exterior rated electrical outlets', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_doorwayclear', label: 'Doorway is clear of obstructions', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_passinspection', label: 'Fire extinguisher passes inspection', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_smokedetectors', label: 'Smoke detectors are present in all main areas and operational', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_fridgeworking', label: 'Refrigerator maintains 40 degrees F', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_freezerworking', label: 'Freezer maintains 0 degrees F', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_subfloorintact', label: 'Subfloor shows no signs of deterioration', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_airventcleaned', label: 'Air vents have been cleaned', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_airfilterchanged', label: 'Air Filters have been changed', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_floorwallclean', label: 'Floors, walls and ceilings are clean', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_nopests', label: 'No signs of pest infestation', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_portablewater', label: 'Signs for non potable water are posted above sinks', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_appliancesclean', label: 'All appliances are clean and operational', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_exhaustworking', label: 'Bathroom exhaust fans are operational', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_equipmentsecured', label: 'All equipment on rooftop is secured as per DROPS securement standard-Zone 4 overhead equipment inspection complete', removed: false, dataType: 1, allowNA: true },
                        { groupName: 'Crew Trailer Inspection Checklist', ctrl:'crewchklst_designatedsmoking', label: 'The designated smoking area is identified (no flammable or combustible material).', removed: false, dataType: 1, allowNA: true },
                    ]
				}
            ];
        },

        newNotification: function() {
            return {
                Reason: '',
                CreatedBy: '',
                CreateDate: new Date().toISOString(),
                Status: 'SCHEDULED',
                NotificationType: '',
                CodeGroup: '',
                Code: '',
                MainWorkCenter: '',
                SecondaryWorkCenter: '',
                RequiredStartDate: undefined,
                RequiredEndDate: undefined,
                Timezone: '',
                Priority: undefined,
                
                defaultReasonText: '',
                hideAdvancedOptions: true,
                isUpdated: false
            };
        },

        // Check if prefill data has been supplied
        // This is used for developer testing and is
        // supplied in the "prefillData.js" file
        testDefaultValues: function() {
            var lists = {};

            if(typeof prefill !== 'undefined') { lists = prefill; }
            return lists;
        },

        // Check if sample data has been supplied
        // This is used for developer testing and is
        // supplied in the "testData.js" file
        testPreviousValues: function() {
            var data = {};
            if(typeof testData !== 'undefined') { data = testData; }
            return data;
        },

        testSetStatus: function() {
            var status = {FormStatus: "SUBM",IsSTMR: true};
            return status;
        }

    };
});