angular.module('PDTest')
.directive('pdNotificationPrint', function() {

    function getHTML(model) {
        var html='<div ng-show="form.allowNotifications">';
        html +='    <hr>';
        html +='    <h5>Corrective Actions</h5>';
        html +='    <span ng-if="'+model+'.length===0">No action items documented</span>';
        html +='    <div class="table-responsive" ng-hide="'+model+'.length === 0">';
        html +='        <table class="table table-bordered">';
        html +='            <thead class="thead-inverse">';
        html +='                <td class="col-sm-2">Created By</td>';
        html +='                <td class="col-sm-2">Date</td>';
        html +='                <td class="col-sm-1">Status</td>';
        html +='                <td class="col-sm-1">SAP ID</td>';
        html +='                <td class="col-sm-7">Reason for Notification</td>';
        html +='                <td class="col-sm-7">Comments</td>';
        html +='            </thead>  ';
        html +='            <tbody>';
        html +='                <tr ng-repeat="item in '+model+'">';
		html +='                    <td class="col-sm-2"><label class="control-label print-line">{{item.CreatedBy}}</label></td>';
		html +='                    <td class="col-sm-2">{{item.CreateDate|checkUNIX: \'MMM dd yyyy H:mm\': \' '+model+'.CreateDateTzOffset\' }} {{ data[form.mainTable].CreateDateTzAbbr }} </td>';
		html +='                    <td class="col-sm-2" ng-if=\"item.Status === \'NEW\'\"> <svg class="icon icon-ion-close"><use xlink:href="#icon-ion-close"></use></svg>&nbsp;&nbsp;&nbsp;{{item.Status}}</td>';
		html +='                    <td class="col-sm-2" ng-if=\"item.Status === \'CLOSED\'\">  <svg class="icon icon-ion-wrench"><use xlink:href="#icon-ion-wrench"></use></svg>&nbsp;&nbsp;&nbsp;{{item.Status}}</td>';
		html +='                    <td class="col-sm-2" ng-if="item.Status !== \'NEW\' && item.Status !== \'CLOSED\'">{{item.Status}}</td>'
		html +='                    <td class="col-sm-1">{{item.NotificationNo}}</label></td>';
		html +='                    <td class="col-sm-7"><div style="white-space:pre-line">{{item.Reason}}</div> </td>'
        html +='                    <td class="col-sm-7"><label class="control-label print-line">{{item.Action}}</label></td>';
        html +='                </tr>';
        html +='            </tbody>';
        html +='        </table>';
        html +='    </div>';
        html +='</div>';
        return html;
    }

    return {
        restrict: 'EA',
        template: function(elem, attr){
            return getHTML(attr.model);
         },
    };

});