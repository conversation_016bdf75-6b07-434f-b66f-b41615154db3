import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, Input } from '@angular/core';
import { <PERSON>ert<PERSON>ontroller, IonRippleEffect, NavController, Platform } from '@ionic/angular/standalone';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { TranslateService } from '@ngx-translate/core';
import { AppConstants } from '../../constants/appConstants';
import { AlertService } from 'src/app/services/alert.service';
import { UtilityService } from 'src/app/services/utility.service';
import { DataService } from 'src/app/services/data.service';
import { TOPIC_HEADER } from 'src/models/TOPIC_HEADER';
import { CTA_HEADER } from 'src/models/CTA_HEADER';
import { STMR_TOPIC } from 'src/models/STMR_TOPIC';
import { TMPLT_VER } from 'src/models/TMPLT_VER';
import { TMPLT_ASSGN } from 'src/models/TMPLT_ASSGN';
import { STMR_FORM_DATA } from 'src/models/STMR_FORM_DATA';
import { STMR_FORM } from 'src/models/STMR_FORM';
import { EventsService } from 'src/app/services/events.service';
import { ModalController } from '@ionic/angular/standalone';
import moment from 'moment';
import { SubmitSTMRService } from 'src/app/services/submitSTMR.service';
import { STMR_ACTION } from 'src/models/STMR_ACTION';
import { STMR_HEADER } from 'src/models/STMR_HEADER';
import { FormsPage } from '../forms/forms.page';
import { STMR_TOPIC_ENTITY } from 'src/models/STMR_TOPIC_ENTITY';
import { Store } from '@ngrx/store';
import { Observable, Subscription } from 'rxjs';
import { selectPrefilledData } from 'src/app/store/store.selector';
import { TemplatesPage, ModeOfOperation } from '../templates/templates.page';
import { STMRDetailsPage } from '../stmr-details/stmr-details.page';
import { HSE_STANDARD_HEADER } from 'src/models/HSE_STANDARD_HEADER';
import { SelectListPage } from '../select-list/select-list.page';
import { ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { firstValueFrom } from 'rxjs';
import { filter } from 'rxjs/operators';
import { IonHeader, IonToolbar, IonButtons, IonButton, IonTitle, IonIcon, IonItem, IonLabel, IonDatetime, IonText, IonNote, IonContent, IonCol, IonList, IonListHeader, IonThumbnail } from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { arrowBackOutline, chevronDownOutline, addCircleOutline} from 'ionicons/icons';
import { ExternalLinkService } from 'src/app/services/external-link.service';
import { ActivatedRoute, Route, Router } from '@angular/router';
import { CustomDatePipe } from 'src/app/pipes/custom-date.pipe';

addIcons({
  arrowBackOutline,
  chevronDownOutline,
  addCircleOutline
});
declare var Windows: any;
declare var cordova: any;

@Component({
  selector: 'app-topics',
  templateUrl: './topics.page.html',
  styleUrls: ['./topics.page.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, TranslateModule, IonHeader, IonToolbar, IonButtons, IonButton, IonTitle, IonIcon, IonItem, IonLabel, IonDatetime, IonText, IonNote, IonContent, IonCol, IonList, IonListHeader, IonThumbnail, IonRippleEffect , CustomDatePipe]
})
export class TopicsPage implements OnInit, OnDestroy {

  @ViewChild('navBar') navBar: any;
  @Input() isReadonly: boolean = false;
  @Input() topic: any;
  @Input() stmrHeader: STMR_HEADER = new STMR_HEADER();
  @Input() mode!: 'Add' | 'Edit' ;
  topicType: string = '';
  isEditTopic: boolean = false;

  topics: TOPIC_HEADER[] = [];
  ctas: CTA_HEADER[] = [];
  hses: HSE_STANDARD_HEADER[] = [];
  topicEntity: STMR_TOPIC_ENTITY = new STMR_TOPIC_ENTITY();
  ctaDocs: any[] = [];
  hseDocs: any[] = [];
  ctaTmplt: any[] = [];

  templates: any[] = [];
  tempVer: TMPLT_VER[] = [];
  tempAssign: TMPLT_ASSGN[] = [];
  ctaAssign: any[] = [];

  returnDisplayDate: any;
  myBrowser: any;
  currStmrHeader: STMR_HEADER = new STMR_HEADER();
  stmrActionArray: STMR_ACTION[] = [];

  newTopicDesc: string = "";
  newCtaDesc: string = "";
  newhseDesc: string = '';
  styleTheme: string = "normal";
  prefillData$!: Observable<any>; // for async pipe OR manual subscription
  prefillData: any;  

  homePages: any;
  timestampOfLastTap: number = 0
  isTopicUpdated: boolean = false
  placeholderModel = "Description / Additional Comments"
  isFirst: boolean = true
  indexOfLastOpenedFormData: number = -1;
  indexOfLastOpenedForm: number = -1;
  topicStart: string = new Date().toISOString();
  TOPIC_NOTE :string = "";
  defaultDescription: string = '';
  minDate: Date = new Date();       
  maxDate: Date = new Date();
  errorMessageForInvalidDateTime: string = '';
  currentTimePast24HrsFromSTMRCreatedTime: boolean = false;
  intervalId:any;
  stmrCreateDate : any;
  STMRDetailsPage: any;
  time_now = new Date(Date.now());
  maxAllowedValue: string = '';
  maxAllowedTopicTime: any;
  monthValue: number = 0;
  dayValue: number = 0;
  yearValue: number = 0;
  hourValue: number = 0;
  minValue: number = 0;
  stmrCreatedTime: string = '';
  minDateStr: string = '';
  maxDateStr: string = '';
  exceedMaxTimeErr :boolean = false;
  belowSTMRTimeErr : boolean = false;
  monthNames = ['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'];
  stmrFormsArr: STMR_HEADER[] = []
  
  constructor(
    private unviredSDK: UnviredCordovaSDK,
    public translate: TranslateService,
    public navCtrl: NavController,
    public alertService: AlertService,
    public ngZone: NgZone,
    public dataService: DataService,
    public alertCtrl: AlertController,
    public platform: Platform,
    public alertController: AlertController,
    public submitSTMRService: SubmitSTMRService,
    private utilityService: UtilityService,
    private store: Store<any>,
    private modalController: ModalController,
    private eventsService: EventsService,
    private externalLinkService: ExternalLinkService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.returnDisplayDate = this.utilityService.returnDisplayDate;
  }

// In topics.page.ts - Replace ngOnInit to handle router state like NavParams
async ngOnInit(): Promise<void> {
  // Get navigation state data (router equivalent of NavParams)

  this.store.select(selectPrefilledData).subscribe(data => {
    this.prefillData = data;
  });

  const navigation = this.router.getCurrentNavigation();
  const state = navigation?.extras?.state || history.state;
  if (state?.stmrHeader) {
    // Set the same properties this were set via NavParams
    this.currStmrHeader = state.stmrHeader;
    this.minDate = new Date(this.currStmrHeader.CRTD_ON * 1000);
  this.minDateStr = this.toLocalISOString(this.minDate);
  this.computeMaxDate();
  this.currentTimePast24HrsFromSTMRCreatedTime = this.isCurrentTimePast24HrsFromSTMRCreatedTime();
    const topicData = state.topicEntity || state.data?.topicEntity;
    this.topicType = state.mode || 'Add';
    
    // Handle existing topic initialization (same logic as before)
    if (this.topicType === 'Edit' && topicData?.topic) {
      await this.initEditTopic(topicData);
    } else {
      // For new topics, pass existing topics for TOPICNO generation
      const existingTopics = state.existingTopics || state.data?.existingTopics || [];
      this.initNewTopic(existingTopics);
    }
    
    this.isReadonly = state.isReadonly || false;
    if (state.stmrForms) {
      // directly update list with new forms
      console.log("state is in ngonint" , state)
      this.stmrFormsArr = state.stmrForms;
    }
  }   else {
    console.error('TopicsPage ngOnInit - STMR header missing!');
    this.alertService.showAlert('Error', 'STMR header data is missing');
    this.router.navigate(['/forms']);
    return;
  }
  

  // Rest of existing ngOnInit logic
  if (!this.currStmrHeader) {
    console.error('TopicsPage ngOnInit - STMR header missing!');
    this.alertService.showAlert('Error', 'STMR header data is missing');
    return;
  }
  console.log('Topic start date initial value:', this.topicStart);
  // ... rest of existing ngOnInit code
  await this.getTopicsFromDB();
  await this.setSTMRCreateDate();
}





private async initEditTopic(topic: STMR_TOPIC_ENTITY) {
  if (!topic || !topic.topic) {
    console.error('TopicsPage - initEditTopic: Invalid topic entity received', topic);
    this.alertService.showAlert('Error', 'Invalid topic data received');
    return;
  }

  this.topicEntity = topic;
  this.ngZone.run(async () => {
    // For existing topics, set P_MODE to 'M' (Modified) instead of 'A' (Add)
    if (this.topicEntity.topic.P_MODE !== 'A') {
      this.topicEntity.topic.P_MODE = 'M';
    }
    
    // Ensure TOPIC_NO is preserved for existing topics
    console.log('Edit mode - preserving TOPIC_NO:', this.topicEntity.topic.TOPIC_NO);

    if (this.topicEntity.topic.CTA_ID) {
      this.defaultDescription = await this.getCTANameFromDB(this.topicEntity.topic.CTA_ID);
      if (this.topicEntity.topic.TOPIC_NOTE) {
        this.TOPIC_NOTE = this.topicEntity.topic.TOPIC_NOTE.substr(this.defaultDescription.length + 1);
      }
    } else if (this.topicEntity.topic.STD_ID) {
      this.defaultDescription = await this.getHSENameFromDB(this.topicEntity.topic.STD_ID);
      if (this.topicEntity.topic.TOPIC_NOTE) {
        this.TOPIC_NOTE = this.topicEntity.topic.TOPIC_NOTE.substr(this.defaultDescription.length + 1);
      }
    } else {
      this.TOPIC_NOTE = this.topicEntity.topic.TOPIC_NOTE || '';
    }
  });

  if (this.topicEntity.topic.TOPIC_START) {
  const date = new Date(Number(this.topicEntity.topic.TOPIC_START) * 1000);
  this.topicStart = this.toLocalISOString(date); // Change from normalizeTopicStart()
} else {
  this.topicStart = this.toLocalISOString(new Date());
}

  this.topicEntity.forms = this.getFormsWithSyncAndFormStatuses(this.topicEntity);
  this.isEditTopic = AppConstants.BOOL_TRUE;
}



private initNewTopic(existingTopics: STMR_TOPIC_ENTITY[]) {
  const newTopic = new STMR_TOPIC();
  newTopic.LID = this.utilityService.guid32(); // Ensure unique LID
  newTopic.TOPIC_NOTE = '';
  newTopic.CTA_ID = '';
  newTopic.P_MODE = 'A'; // Mark as new/add
  
  this.topicEntity = new STMR_TOPIC_ENTITY();
  this.topicEntity.topic = newTopic;
  
  const safeExistingTopics = existingTopics || [];
  // Filter out deleted topics and get active topic numbers
  const activeTopics = safeExistingTopics.filter(t => t.topic.P_MODE !== 'D');
  const sortedArray = [...activeTopics].sort((a, b) =>
    parseInt(a.topic.TOPIC_NO) - parseInt(b.topic.TOPIC_NO)
  );
  
  // Generate next topic number
  this.topicEntity.topic.TOPIC_NO = sortedArray.length > 0
    ? (parseInt(sortedArray[sortedArray.length - 1].topic.TOPIC_NO) + 1).toString()
    : '1';
  
  const now = new Date();
  this.topicStart = this.toLocalISOString(now); // Use local time instead of UTC
  this.topicEntity.topic.TOPIC_START = Math.floor(now.getTime() / 1000).toString();
  
  console.log('[INIT_NEW_TOPIC] Created new topic with TOPIC_NO:', this.topicEntity.topic.TOPIC_NO, 'and LID:', newTopic.LID);
}

private normalizeTopicStart(startValue: string): string {
  const date = isNaN(Number(startValue))
    ? new Date(startValue)
    : new Date(Number(startValue) * 1000);
  return this.toLocalISOString(date);
}

  ngOnDestroy() {
    // Clean up intervals
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  isSTMRReadonly(stmr: any): boolean {
  if (!stmr || !stmr.STMR_STATUS) {
    return false; // Or true, depending on default logic
  }
  return stmr.STMR_STATUS === 'READONLY';
}

  getFormsWithSyncAndFormStatuses(topicEntity: STMR_TOPIC_ENTITY): any[] {
    return topicEntity.forms.map(form => {
      return {
        ...form,
      formStatus: this.utilityService.getFormStatusObj(form.FORM_STATUS),
        syncStatus: (() => {
          const tmp = this.utilityService.getSyncStatusObj(form.SYNC_STATUS);
          return (tmp.descr && tmp.color) ? tmp : undefined;
        })()
      };
    });
  }

  ionViewDidLoad() {
    console.log('ionViewDidLoad TopicsPage');
    this.utilityService.isAppRunningInTestAutomationMode().then(isTestMode => {
      if (isTestMode) {
        console.log('Test mode enabled');
      }
    })
    this.setupBackButtonHandler();
  }

  private setupBackButtonHandler() {
    // Handle back button logic 
    if (this.isTopicUpdated) {
      let message = this.translate.instant("Your unsaved changes will be lost if you don't save them.")
      this.translate.get(message).subscribe(value => {
        message = value
      })     
      this.showBackConfirmationAlert(message);
    } else {
      this.navCtrl.back();
    }
  }

  private async showBackConfirmationAlert(message: string): Promise<void> {
  const alert = await this.alertCtrl.create({
    header: this.translate.instant('Are you sure you want to go back?'),
    subHeader: message,
    buttons: [
      {
        text: this.translate.instant('Save'),
        handler: async () => {
          try {
            await this.saveTopicInDB();
            this.navigateBackToSTMRDetails();
          } catch (error) {
            console.error('Error saving topic before navigation:', error);
            this.alertService.showAlert('Error', 'Failed to save topic');
          }
        }
      },
      {
        text: this.translate.instant('Don\'t Save'),
        handler: () => {
          this.navigateBackToSTMRDetails();
        }
      },
      {
        text: this.translate.instant('Cancel'),
        role: 'cancel'
      }
    ]
  });
  await alert.present();
}

private navigateBackToSTMRDetails(): void {
  this.router.navigate(['/stmr-details'], {
    state: {
      returnFromTopics: true,
      topicSaved: false, // Indicates no save occurred
      cancelled: true
    }
  });
}

  saveTopicState(): Promise<any> {
  return new Promise(async (resolve) => {
    await this.saveFormData();
    // Topic Start
    const date = new Date(this.topicStart).getTime() / 1000;
    this.topicEntity.topic.TOPIC_START = String(date);
    // Topic Note
    const note = (this.TOPIC_NOTE ?? '').trim();
    this.topicEntity.topic.TOPIC_NOTE = this.defaultDescription
      ? `${this.defaultDescription} ${note}`
      : note;
    // Topic Name
    this.topicEntity.topic.TOPIC_NAME = this.utilityService.getTopicName(
      this.topics,
      this.topicEntity.topic.TOPIC_ID
    );
    // Object Status update
    if (this.topicEntity.topic.OBJECT_STATUS === AppConstants.OBJECT_STATUS.GLOBAL) {
      this.topicEntity.topic.OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;
    }
    // Callback
    this.STMRDetailsPage.callbackAfterUserClosedAppInTopssicsPage(
      this.currStmrHeader,
      JSON.parse(JSON.stringify(this.topicEntity)),
      JSON.parse(JSON.stringify(this.stmrActionArray))
    );
    resolve(this.STMRDetailsPage.saveSTMRState());
  });
}


  saveFormData(): Promise<void> {
    return new Promise(async (resolve, reject) => {
      if (this.indexOfLastOpenedForm == -1 || this.indexOfLastOpenedFormData == -1) {
        console.log("TopicsPage", "saveFormData()", "No STMR form open. Nothing to save in STMR form.")
        return resolve()
      }
      console.log("TopicsPage", "saveFormData()", "STMR Form Index: " + this.indexOfLastOpenedForm + " STMR Data Index: " + this.indexOfLastOpenedFormData)

      // Read Form Data
      try {
        const result = await this.myBrowser.executeScript({
          code: "generateJSON()"
        });
        if (result && result.length > 0) {
          var formData = result[0]
          // Form Header with FORM_STATUS
          // If isSubmit is true the set status as AppConstants.VAL_FORM_STATUS.SUBM
          let status = AppConstants.VAL_FORM_STATUS.INPR
          // Update STMR Form
          this.topicEntity.forms[this.indexOfLastOpenedForm].FORM_STATUS = status
          this.topicEntity.forms[this.indexOfLastOpenedForm].TIME_ZONE = this.utilityService.getTimezone();    
          if (this.topicEntity.forms[this.indexOfLastOpenedForm].OBJECT_STATUS == AppConstants.OBJECT_STATUS.GLOBAL) {
            this.topicEntity.forms[this.indexOfLastOpenedForm].OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY
            this.topicEntity.forms[this.indexOfLastOpenedForm].P_MODE = 'M'
          }
          // Update STMR Form Data
          this.topicEntity.data[this.indexOfLastOpenedFormData].DATA = formData
          if (this.topicEntity.data[this.indexOfLastOpenedFormData].OBJECT_STATUS == AppConstants.OBJECT_STATUS.GLOBAL) {
            this.topicEntity.data[this.indexOfLastOpenedFormData].OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY
            this.topicEntity.data[this.indexOfLastOpenedFormData].P_MODE = 'M'
          }
          return resolve()
        }
        else {
          console.error("TopicsPage", "saveFormData()", "Empty result obtained from inAppBrowser")
          return resolve()
        }
      } catch (error) {
        console.error("TopicsPage", "saveFormData()", "Error while reading JSON from In-App Browser: " + error)
        return resolve()
      }
    })
  }

  async addFormsToTopic() {
    console.log('Adding forms to topic...' , this.currStmrHeader  , this.stmrHeader);
    // Dismiss TopicsPage modal first

    this.router.navigate(['/templates'], { 
      queryParams: { 
        modeOfOperation: 'AddFormToTopic',   // pass string explicitly
        stmrHeader: JSON.stringify(this.currStmrHeader),
        topicEntity: JSON.stringify(this.topicEntity)
      } 
    });
  }

  onDatetimeChange(event: any) {
    this.ngZone.run(() => {
      const selectedDate = new Date(event.detail.value);
      const selDate = Math.floor(selectedDate.getTime() / 1000);
      const createdOn = Math.floor(this.minDate.getTime() / 1000);
      const maxTimestampSeconds = Math.floor(this.maxDate.getTime() / 1000);
      
      if (selDate < createdOn) {
        this.isTopicUpdated = false;
        const displayDate = this.utilityService.returnDisplayDate(this.minDate, "", "MMM DD YYYY HH:mm");
        this.errorMessageForInvalidDateTime = this.translate.instant(
          `Topic time cannot be earlier than STMR's created date: ${displayDate}`
        );
      } else if (selDate > maxTimestampSeconds) {
        this.isTopicUpdated = false;
        const displayDate = this.utilityService.returnDisplayDate(this.maxDate, "", "MMM DD YYYY HH:mm");
        this.errorMessageForInvalidDateTime = this.translate.instant(
          `Topic time cannot be adjusted past the current date: ${displayDate}`
        );
      } else {
        this.errorMessageForInvalidDateTime = '';
        this.isTopicUpdated = true;
        this.topicStart = this.toLocalISOString(selectedDate);     // UI stays local
      this.topicEntity.topic.TOPIC_START = selDate.toString();
      }
    });
  }



  async ionViewWillEnter() {
    console.log('ionViewWillEnter TopicsPage');

  console.log('DB Topics Loaded');
  


    const navigation = this.router.getCurrentNavigation();
  const state = navigation?.extras?.state || history.state;
    console.log('state is after adding form', state)
 if (state?.stmrForms) {
  console.log("Got forms:", state.stmrForms);

    if(state.stmrHeader.STMR_ID == state.stmrForms.STMR_ID){
        this.topicEntity.forms.push(state.stmrForms); // single form
    }
  console.log(this.topicEntity.forms);
} 

  }

  async getTopicsFromDB() {
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_TOPIC_HEADER, {'IS_ACTIVE': 'true'});
      if (ResultType[result.type] !== 'success') {
      throw new Error(`Database query failed: ${result.message || 'Unknown database error'}`);
    }
      console.log('DB Topics Result:', result);
      if (result.data && result.data.length > 0) {
        this.topics = result.data;
        this.ngZone.run(() => {
          this.topics = this.utilityService.sortArray(this.topics, 'TOPIC_ID');
          if (this.topicEntity.topic.TOPIC_ID) this.newTopicDesc = this.utilityService.getTopicName(this.topics, this.topicEntity.topic.TOPIC_ID);
        });
      }
    } catch (error) {
        const errorMessage = this.getErrorMessage(error);
        console.error("TopicsPage", "getTopicsFromDB", "Error while fetching Topics from DB:", error);
        console.error("TopicsPage", "getTopicsFromDB", "Error message:", errorMessage);
        this.alertService.showAlert("Error", errorMessage);
      }
    this.getCTAAssignsFromDB();
    this.getHSEFormDB();
  }


  async getCTAAssignsFromDB() {
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_CTA_ASSIGNMENT, {});
      if (result.data && result.data.length > 0) {
        this.ctaAssign = result.data;
        this.getCTAsFromDB();
      }
    } catch (error) {
      const errorMessage = this.getErrorMessage(error);
      console.error("TopicsPage", "getCTAsFromDB", "Error while fetching CTAs from DB:", error);
      this.alertService.showAlert("Error", errorMessage);
      this.alertService.showAlert("Error", JSON.stringify(error));
    }
    if (this.isEditTopic) {
      this.getDocsAndTemplates(AppConstants.BOOL_TRUE)
    }
    else {
      this.getDocsAndTemplates();
    }
  }

  async getCTAsFromDB() {
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_CTA_HEADER, {});
      if (result.data && result.data.length > 0) {
        // Filter ctas by CTA_ASSGN
        // 1. Group assignments by CTA_ID
        // 2. Match (rig and my rig) or no rig
        // 3. Delete other CTAs this dont match
        var tempCTAs = JSON.parse(JSON.stringify(result.data)),
          temp = [],
          hasOneRig = AppConstants.BOOL_FALSE,
          hasMyRig = AppConstants.BOOL_FALSE;
          this.ctas = result.data;
        this.getRigDetFromDB((rigDet) => {
          for (let i = 0, iLen = tempCTAs.length; i < iLen; i++) {
            temp = [];
            for (let j = 0, jLen = this.ctaAssign.length; j < jLen; j++) {
              if (this.ctaAssign[j].CTA_ID == tempCTAs[i].CTA_ID) {
                temp.push(this.ctaAssign[j]);
              }
            }
            // Check Rig Assignment
            hasOneRig = AppConstants.BOOL_FALSE;
            hasMyRig = AppConstants.BOOL_FALSE;
            for (let l = 0, lLen = temp.length; l < lLen; l++) {
              hasOneRig = AppConstants.BOOL_TRUE; // Has one assignment
              if (temp[l].RIG_TYPE == rigDet.RIG_TYPE && temp[l].RIG_SUB_TYPE == rigDet.RIG_SUB_TYPE) {
                hasMyRig = AppConstants.BOOL_TRUE;
              }
            }
            // If rig then one rig must be logged in user rig
            if (hasOneRig && hasMyRig) {
              // when has rig assignment
              tempCTAs[i].isValidTmplt = AppConstants.BOOL_TRUE;
            } else if (!hasOneRig) {
              // when no rig assignments found - display this template
              tempCTAs[i].isValidTmplt = AppConstants.BOOL_TRUE;
            } else {
              tempCTAs[i].isValidTmplt = AppConstants.BOOL_FALSE;
            }
            if (tempCTAs[i].isValidTmplt == AppConstants.BOOL_FALSE) {
              for (let m = this.ctas.length - 1; m >= 0; --m) {
                if (this.ctas[m].CTA_ID == tempCTAs[i].CTA_ID) {
                  this.ctas.splice(m, 1);
                }
              }
            }
          }
          this.ngZone.run(() => {
            this.ctas = this.utilityService.sortArray(this.ctas, 'DESCR');
            if (this.topicEntity.topic.CTA_ID) this.newCtaDesc = this.utilityService.getCTADesc(this.ctas, this.topicEntity.topic.CTA_ID);
          })
        })
      }
    } catch (error) {
      const errorMessage = this.getErrorMessage(error);
      console.error("TopicsPage", "getCTAsFromDB", "Error while fetching CTAs from DB:", error);
      this.alertService.showAlert("Error", errorMessage);
    }
  }

  async getDocsAndTemplates(onlyThis?: boolean) {
    // Documents
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_CTA_DOC, { 'CTA_ID': this.topicEntity.topic.CTA_ID });
      if (result.data && result.data.length > 0) {
        this.ctaDocs = result.data;
      }
      console.log('TopicsPage - Topic entity:', this.topicEntity); 
    } catch (error) {
      const errorMessage = this.getErrorMessage(error);
      console.error("TopicsPage", "getDocsAndTemplates", "Error while fetching CTA Documents from DB:", error);
      this.alertService.showAlert("Error", errorMessage);
    }
    if (!onlyThis) this.getTemplatesHierarchy();
  }

  // Get Templates Hierarchy
  getTemplatesHierarchy() {
   this.ctaDocs = [];
    this.getTemplatesFromDB((res) => {
      this.ngZone.run(() => {
        this.ctaTmplt = res;
        // Create Topic & Forms from templates.
        this.topicEntity.topic = this.createNewTopic(this.topicEntity.topic)
        this.topicEntity.forms = this.createSTMRForms(this.topicEntity.topic, this.ctaTmplt);
        this.topicEntity.data = this.createSTMRFormdata(this.topicEntity.forms)
      })
    });
  }

  async getHSEFormDB() {
 
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_HSE_STANDARD_HEADER, {});
      if (result.data && result.data.length > 0) {
        this.hses = result.data; 
        this.ngZone.run(() => {
          this.hses = this.utilityService.sortArray(this.hses, 'DESCR');
          if (this.topicEntity.topic.STD_ID) this.newhseDesc = this.utilityService.getHSEDesc(this.hses, this.topicEntity.topic.STD_ID);
        })
      }
    } catch (error) {
      const errorMessage = this.getErrorMessage(error);
      console.error("TopicsPage", "getHSEFormDB", "Error while fetching HSE Standards from DB:", error);
      this.alertService.showAlert("Error", errorMessage);
    }
    this.getHSEDocs()    
  }

  async getHSEDocs() {
    // Documents
    try {
      this.hseDocs = [];
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_HSE_STANDARD_DOC, { 'STD_ID': this.topicEntity.topic.STD_ID });
      console.log('HSE Docs query result:', result); // Add this debug log
      console.log('Querying with STD_ID:', this.topicEntity.topic.STD_ID); // Add this debug log
      console.log('TopicsPage - Topic entity:', this.topicEntity); 
      if (result.data && result.data.length > 0) {
        this.hseDocs = result.data;
      }
      console.log('HSE Docs', this.hseDocs)
    } catch (error) {
      const errorMessage = this.getErrorMessage(error);
      console.error("TopicsPage", "getHSEDocs", "Error while fetching HSE Documents from DB:", error);
      this.alertService.showAlert("Error", errorMessage);
    }
  }


  createSTMRFormdata(STMRForms: STMR_FORM[]): STMR_FORM_DATA[] {
    var formDataArray: STMR_FORM_DATA[] = []
    STMRForms.forEach(element => {
      let formData = new STMR_FORM_DATA()
      formData.LID = this.utilityService.guid32();
      formData.FID = element.FID
      formData.STMR_ID = element.STMR_ID
      formData.TOPIC_NO = element.TOPIC_NO
      formData.FORM_ID = element.FORM_ID
      formData.DATA = ''
      formData.P_MODE = 'A'
      formData.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD
      formDataArray.push(formData)
    });
    return formDataArray
  }

  // DB Call to get Templates
  async getTemplatesFromDB(callback: (data: any[]) => void) {
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_CTA_TMPLT, {});
      if (result.data && result.data.length > 0) {
        this.templates = result.data;
      }
      this.getTemplatesVerFromDB(callback);
    } catch (error) {
      const errorMessage = this.getErrorMessage(error);
      console.error("TopicsPage", "getTemplatesFromDB", "Error while fetching CTA Templates from DB:", error);
      this.alertService.showAlert("Error", errorMessage);
      callback([]);
    }
  }

  // DB Call to get Template Version.
  async getTemplatesVerFromDB(callback: (data: any[]) => void) {
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_TEMPLATE_VERSION, {});
      if (result.data && result.data.length > 0) {
        this.tempVer = result.data;
      }
      this.getTemplatesAssgnFromDB(callback);
    } catch (error) {
      const errorMessage = this.getErrorMessage(error);
      console.error("TopicsPage", "getTemplatesVerFromDB", "Error while fetching Templates Version from DB:", error);
      this.alertService.showAlert("Error", errorMessage);
      callback([]);
    }
  }

  // DB Call to get Template Assign.
  async getTemplatesAssgnFromDB(callback: (data: any[]) => void) {
    try {
      const result = await this.unviredSDK.dbExecuteStatement("SELECT * FROM TMPLT_ASSGN");
      if (result.data && result.data.length > 0) {
        this.tempAssign = result.data;
      }
      this.mapTemplVer(this.tempVer, callback);
    } catch (error) {
      const errorMessage = this.getErrorMessage(error);
      console.error("TopicsPage", "getTemplatesAssgnFromDB", "Error while fetching Templates Assign from DB:", error);
      this.alertService.showAlert("Error", errorMessage);
      callback([]);
    }
  }

  /**
 * Map Template Version to Templates Header
 * matching TMPLT_ID
 */
  mapTemplVer(array: TMPLT_VER[], callback: (data: any[]) => void) {
    var temp = [];
    if (this.templates && this.templates.length) {
      if (array && array.length) {
        for (let i = 0, iLen = this.templates.length; i < iLen; i++) {
          // this.templates[i][AppConstant.TABLE_TEMPLATE_VERSION] = [];
          temp = [];
          for (let j = 0, jLen = array.length; j < jLen; j++) {
            if (array[j].TMPLT_ID == this.templates[i].TEMPLATE_ID) {
              temp.push(array[j]);
            }
          }
          // this.templates[i][AppConstant.TABLE_TEMPLATE_VERSION] = temp;
          //Fetch latest L_VER_NO, L_VER_ID, L_CRTD_BY and L_CRTD_ON
          let latest: { [key: string]: any } = this.utilityService.getReleaseVersionDet(temp, [AppConstants.VER_NO, AppConstants.VER_ID, AppConstants.CRTD_BY, AppConstants.CRTD_ON])
          // Prefix "L_" to the string
          for (let key in latest) {
            this.templates[i]["L_" + key] = latest[key];
          }
        }
      }
      this.mapTemplAssgn(this.tempAssign, callback);
    } else {
      callback([]);
    }
  }

	/**
	 * Map Template Assign to Templates Header
	 * matching TMPLT_ID
	 */
  mapTemplAssgn(array: TMPLT_ASSGN[], callback: (data: any[]) => void) {
     var temp = [];
    if (this.templates && this.templates.length) {
      if (array && array.length) {
        for (let i = 0, iLen = this.templates.length; i < iLen; i++) {
          this.templates[i][AppConstants.TABLE_TEMPLATE_ASSIGN] = [];
          temp = [];
          for (let j = 0, jLen = array.length; j < jLen; j++) {
            if (array[j].TMPLT_ID == this.templates[i].TEMPLATE_ID) {
              temp.push(array[j]);
            }
          }
          this.templates[i][AppConstants.TABLE_TEMPLATE_ASSIGN] = temp;
        }
      }
    }
    // Remove templates without assignment
    for (let j = this.templates.length - 1; j >= 0; j--) {
      if (!this.templates[j].TMPLT_ASSGN || !this.templates[j].TMPLT_ASSGN.length) {
        this.templates.splice(j, 1);
      }
    }
    callback(this.templates);
  }

  
  
async saveTopic() {
  console.log('[TOPIC] Starting saveTopic...');
  
  // Ignore double taps within 'x' seconds.
  let timestamp = new Date().getTime()
  if (timestamp - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
    console.log('[TOPIC] Ignoring double tap');
    this.unviredSDK.logInfo("TemplatesPage", "createSTMR()", "Ignoring double tap")
    return
  }
  this.timestampOfLastTap = timestamp
  try {
    console.log('TOPIC] Saving topic to DB...');
    console.log('[DB DEBUG] Final topic object sent to DB:', JSON.stringify(this.topicEntity, null, 2));
    await this.saveTopicInDB();
    console.log('[TOPIC] Topic saved successfully, dismissing modal...');
    
   this.router.navigate(['/stmr-details'], {
  queryParams: {
    returnFromTopics: true,
    topicSaved: true,
    savedTopic: JSON.stringify(this.topicEntity),
    stmrActions: JSON.stringify(this.stmrActionArray),
    stmrHeader: JSON.stringify(this.currStmrHeader)
  }
});

    console.log('[TOPIC] navigating back')
    
  } catch (error: any) {
    console.error('[TOPIC] Failed to save topic:', error);
  }
}


 saveTopicInDB(): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!this.validateTopic()) {
      reject(new Error('Topic validation failed'));
      return;
    }
    this.prepareTopicForSave();
    const finalData = {
      header: this.currStmrHeader,
      topicEntity: JSON.parse(JSON.stringify(this.topicEntity)),
      actions: JSON.parse(JSON.stringify(this.stmrActionArray))
    }
    // ADD THIS DEBUG LOG
    console.log('[DEBUG_EVENT_PUBLISH] Publishing topicSaved with data:', JSON.stringify(finalData));
    this.eventsService.publish('topicSaved', finalData);
    resolve(); // Temporarily resolve immediately for testing
  });
}

// method to prepare topic data before saving
private prepareTopicForSave() {
  // Set topic start time
  const date = new Date(this.topicStart).getTime() / 1000;
  this.topicEntity.topic.TOPIC_START = String(date);
  // Set topic note with default description
  const note = (this.TOPIC_NOTE ?? '').trim();
  this.topicEntity.topic.TOPIC_NOTE = this.defaultDescription
    ? `${this.defaultDescription} ${note}`
    : note;
  // Set topic name from selected topic
  this.topicEntity.topic.TOPIC_NAME = this.utilityService.getTopicName(
    this.topics,
    this.topicEntity.topic.TOPIC_ID
  );
  // If topic name is empty, use the description as fallback
  if (!this.topicEntity.topic.TOPIC_NAME && this.newTopicDesc) {
    this.topicEntity.topic.TOPIC_NAME = this.newTopicDesc;
  }
  // Set other required fields
  if (this.topicEntity.topic.OBJECT_STATUS === AppConstants.OBJECT_STATUS.GLOBAL) {
    this.topicEntity.topic.OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;
  }
  console.log('Topic prepared for save:', {
    TOPIC_NAME: this.topicEntity.topic.TOPIC_NAME,
    TOPIC_NOTE: this.topicEntity.topic.TOPIC_NOTE,
    TOPIC_ID: this.topicEntity.topic.TOPIC_ID
  });
    console.log('[DEBUG_TOPIC_SAVE] Complete topic entity being prepared:', JSON.stringify(this.topicEntity, null, 2));
    console.log('[DEBUG_TOPIC_SAVE] Topic entity forms:', JSON.stringify(this.topicEntity.forms, null, 2));
    console.log('[DEBUG_TOPIC_SAVE] Topic entity data:', JSON.stringify(this.topicEntity.data, null, 2));
}



  createNewTopic(tempTopic: STMR_TOPIC) {
  if (!this.currStmrHeader) {
    console.warn("TopicsPage", "createNewTopic", "currStmrHeader is null → skipping");
    return {} as STMR_TOPIC;
  }
  return {
    STMR_ID: this.currStmrHeader.STMR_ID,
    CTA_ID: tempTopic.CTA_ID,
    TOPIC_NO: tempTopic.TOPIC_NO + "",
    TOPIC_ID: tempTopic.TOPIC_ID,
    TOPIC_START: tempTopic.TOPIC_START,
    TOPIC_NOTE: tempTopic.TOPIC_NOTE,
    TOPIC_NAME: tempTopic.TOPIC_NAME,
    P_MODE: tempTopic.P_MODE,
    FID: this.currStmrHeader.LID,
    LID: tempTopic.LID ?? "",
    // required fields
    STD_TYPE: tempTopic.STD_TYPE ?? "",
    STD_ID: tempTopic.STD_ID ?? "",
    LAST_SYNC_USER: tempTopic.LAST_SYNC_USER ?? "",
    LAST_SYNC_TIME: tempTopic.LAST_SYNC_TIME ?? "",
    TOPIC_STATUS: tempTopic.TOPIC_STATUS ?? "",
    // sync status logic
    SYNC_STATUS: this.isEditTopic ? undefined : AppConstants.SYNC_STATUS.NONE,
    OBJECT_STATUS: this.isEditTopic
      ? AppConstants.OBJECT_STATUS.MODIFY
      : AppConstants.OBJECT_STATUS.ADD,
  } as STMR_TOPIC;
}

  // Create or update form header
  createSTMRForms(newTopic: STMR_TOPIC, ctaTmplt: string | any[]): STMR_FORM[] {

    var stmrForms: STMR_FORM[] = []
    // 1. Check if CTA and create/update form
    // 2. Check for associated forms.....
    if (newTopic.CTA_ID) {
      if (ctaTmplt && ctaTmplt.length) {
        for (let i = 0, iLen = ctaTmplt.length; i < iLen; i++) {
          if (ctaTmplt[i].CTA_ID == newTopic.CTA_ID) {
            if (this.prefillData) {
              // Create form for this template with ctaTmplt[i].L_VER_ID
              let newForm: STMR_FORM = <STMR_FORM>{};
              newForm.LID = this.utilityService.guid32();
              newForm.STMR_ID = this.currStmrHeader.STMR_ID;
              newForm.TOPIC_NO = newTopic.TOPIC_NO;
              newForm.FORM_ID = 'New' + this.utilityService.guid32();
              newForm.VER_ID = ctaTmplt[i].L_VER_ID;
              newForm.CRTD_BY = this.prefillData.USER_ID;
              newForm.CRTD_ON = moment.utc().unix();
              newForm.SUBM_BY = this.prefillData.USER_ID;
              newForm.DATE_COMP = moment.utc().unix();
              newForm.COMPANY = this.prefillData.COMP_CODE;
              newForm.RIG_NO = this.prefillData.RIG_NO;
              newForm.COMMENTS = "";
              newForm.FORM_STATUS = AppConstants.VAL_FORM_STATUS.OPEN;
              newForm.FID = this.currStmrHeader.LID;
              newForm.NAME = ctaTmplt[i].NAME;
              newForm.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
              newForm.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
              newForm.P_MODE = "A";
              newForm.LAST_SYNC_USER = this.prefillData.USER_ID
              newForm.SUBM_BY = this.prefillData.USER_ID
              // decoration handled at render time
              stmrForms.push(newForm)
            }
            else {
              this.unviredSDK.logError("TopicsPage", "createOrUpdateFormHeader", "Error while fetching Prefill values stored in memory");
            }
          }
        }
      } else {
        // No Templates associated
        this.unviredSDK.logInfo("TopicsPage", "createOrUpdateFormHeader", "No Template associated with this CTA: " + newTopic.CTA_ID);
      }
    } else {
      this.unviredSDK.logInfo("TopicsPage", "createOrUpdateFormHeader", "This topic is not a CTA: " + JSON.stringify(newTopic, null, 2));
    }
    return stmrForms
  }

  // Validate inputs if create
  validateTopic(): boolean {
    if (this.isEditTopic == AppConstants.BOOL_FALSE) {
      if (!this.topicEntity.topic.TOPIC_ID) {
        this.alertService.showAlert("Alert", "Please select the topic.");
      } else if (this.checkIfCTA() && !this.topicEntity.topic.CTA_ID) {
        this.alertService.showAlert("Alert", "Please select the CTA type.");
      } else if (this.checkIfHSE() && !this.topicEntity.topic.STD_ID) {
        this.alertService.showAlert("Alert", "Please select the HSE type.");
      } else if (!this.topicStart) {
        this.alertService.showAlert("Alert", "Please select the topic start time.");
      } else if (!this.TOPIC_NOTE) {
        this.alertService.showAlert("Alert", "Please enter the topic description.");
      } else if (!this.TOPIC_NOTE.trim()) {
        this.alertService.showAlert("Alert", "Please enter valid topic description.");
      } else if (this.errorMessageForInvalidDateTime.length > 0) {
        this.alertService.showAlert("Alert", this.errorMessageForInvalidDateTime)
      }
      else {
        return true;
      }
    } else {
      if (!this.TOPIC_NOTE) {
        this.alertService.showAlert("Alert", "Please enter the topic description.");
      } else if (!this.TOPIC_NOTE.trim()) {
        this.alertService.showAlert("Alert", "Please enter valid topic description.");
      } else if (this.errorMessageForInvalidDateTime.length > 0) {
        this.alertService.showAlert("Alert", this.errorMessageForInvalidDateTime)
      }
      else {
        return true;
      }
    }
    return false
  }

  // Delete Topic 
  async deleteTopic() {
    // Ask for confirmation
    const alert = await this.alertController.create({
      header: "Delete Topic",
      subHeader: AppConstants.DELETE_FORMS_MSG,
      buttons: [
        {
          text: 'Delete',
          handler: () => {
            // Mark the fields with P_MODE.
            this.topicEntity.topic.P_MODE = "D";
            this.topicEntity.topic.OBJECT_STATUS = AppConstants.OBJECT_STATUS.DELETE
            // Update Forms
            this.topicEntity.forms = this.topicEntity.forms.map(element => {
              element.P_MODE = 'D'
              element.OBJECT_STATUS = AppConstants.OBJECT_STATUS.DELETE
              return element
            })
            // Update Form Data
            this.topicEntity.data = this.topicEntity.data.map(element => {
              element.P_MODE = 'D'
              element.OBJECT_STATUS = AppConstants.OBJECT_STATUS.DELETE
              return element
            })
            this.STMRDetailsPage.callbackAfterTopic(this.currStmrHeader, this.topicEntity, this.stmrActionArray);
            this.navCtrl.back();
          }
        },
        {
          text: 'Cancel',
          handler: () => {
            console.log('Disagree clicked');
          }
        }
      ],
    });
    await alert.present();
  }

  // If Topic selected is CTA then display CTA types
  checkIfCTA() {
    if (this.topicEntity.topic.TOPIC_ID == AppConstants.VAL_CTA_TOPIC_ID) {
      return AppConstants.BOOL_TRUE;
    }
    else {
      this.topicEntity.topic.CTA_ID = "";
      return AppConstants.BOOL_FALSE;
    }
  }

  // If Topic selected is HSE then display HSE types
  checkIfHSE() {
    if (this.topicEntity.topic.TOPIC_ID == AppConstants.VAL_HSE_TOPIC_ID) {
      return AppConstants.BOOL_TRUE;
    }
    else {
      this.hseDocs = []
      this.topicEntity.topic.STD_ID = "";
      return AppConstants.BOOL_FALSE;
    }
  }

  isValidDoc(doc: any) {
    if (typeof (doc.FILE_NAME) == 'undefined' || doc.FILE_NAME == null || doc.FILE_NAME.length == 0) {
      return false
    }
    return true
  }

  // Open Doc 
  openDoc(doc: any) {
    (async() => {
      // Ignore double taps within 'x' seconds.
      let timestamp = new Date().getTime()
      if (timestamp - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
        return
      }
      this.timestampOfLastTap = timestamp
      console.log('TopicsPage', 'openDoc()', 'Opening CTA Document: ' + JSON.stringify(doc, null, 2))
      if (typeof (doc.FILE_NAME) == 'undefined' || doc.FILE_NAME == null || doc.FILE_NAME.length == 0) {
        this.alertService.showAlert(this.translate.instant("Error"), this.translate.instant("This document is not associated with a valid filename. Please contact your adminstrator."))
        console.error("TopicsPage", "openDoc()", "Error while opening Doc: " + doc.NAME + " File name: " + doc.FILE_NAME)
        return
      }
      console.log("TopicsPage", "openDoc()", "Opening Doc: " + doc.NAME + " File name: " + doc.FILE_NAME)         
      try {
        const finalPath = await this.utilityService.getCompleteDocPath(true, doc.CTA_ID, doc.DOC_ID);
        // Decode the URL to handle any special characters
        const decodedPath = decodeURI(finalPath);
        console.log('Opening document with path:', decodedPath);
        await this.externalLinkService.openDocument(decodedPath);
      }
      catch (error: any) {
        const errorMessage = error.message || 'Error while opening document';
        console.error("TopicsPage", "openDoc()", errorMessage);
        this.unviredSDK.logError("TopicsPage", "openDoc()", errorMessage);
        this.alertService.showAlert(
          this.translate.instant("Error"), 
          this.translate.instant("Failed to open document. The file may not exist or you may not have permission to access it.")
        );
      }
    })()
  }


   openDocHSE(doc: any) {
    (async() => {
    // Ignore double taps within 'x' seconds.
    let timestamp = new Date().getTime()
    if (timestamp - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
      return
    }
    this.timestampOfLastTap = timestamp
    console.log('TopicsPage', 'openDocHSE()', 'Opening HSE Document: ' + JSON.stringify(doc, null, 2))
    if (typeof (doc.FILE_NAME) == 'undefined' || doc.FILE_NAME == null || doc.FILE_NAME.length == 0) {
      this.alertService.showAlert(this.translate.instant("Error"), this.translate.instant("This document is not associated with a valid filename. Please contact your adminstrator."))
      console.error("TopicsPage", "openDocHSE()", "Error while opening Doc: " + doc.NAME + " File name: " + doc.FILE_NAME)
      return
    }
    try {
      const finalPath = await this.utilityService.getCompleteDocPath(false, doc.STD_ID, doc.DOC_ID)
      const decodedPath = decodeURI(finalPath);
      console.log('Opening document with path:', decodedPath);
      await this.externalLinkService.openDocument(decodedPath);
    }
    catch (error: any) {
      const errorMessage = error.message || 'Error while opening document';
      console.error("TopicsPage", "openDocHSE()", errorMessage);
      this.unviredSDK.logError("TopicsPage", "openDocHSE()", errorMessage);
      this.alertService.showAlert(
        this.translate.instant("Error"), 
        this.translate.instant("Failed to open document. The file may not exist or you may not have permission to access it.")
      );
    }
    })()
  }



  onError(error: any) {
    console.log("Error while viewing doc:", this.getErrorMessage(error));
  }



  // Move to forms Page
  goToFormsPage() {
    this.homePages = FormsPage;
    this.navCtrl.navigateRoot('/forms');
  }

  // Get Rig Details
  async getRigDetFromDB(callback: (data: any) => void) {
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_RIG_HEADER, {});
      if (result.data && result.data.length > 0) {
        callback(result.data[0]);
      } else {
        callback({});
      }
    } catch (error) {
      const errorMessage = this.getErrorMessage(error);
      console.error("TopicsPage", "getRigDetFromDB", "Error while fetching Rig Header from DB:", error);
      this.alertService.showAlert("Error", errorMessage);
    }
  }

  async openSelectList(isCTA?: boolean) {
  // Prefill data not yet loaded
  if (!this.prefillData) {
    this.alertService.showAlert(
      this.translate.instant('Information'),
      this.translate.instant('Please wait while data is loading...')
    );
    return;
  }

  if (this.isEditTopic || this.isSTMRReadonly(this.currStmrHeader)) {
    return;
  }

  this.confirmWithUser(() => {
    this.ngZone.run(() => {
      if (this.TOPIC_NOTE.length == 0) {
        this.topicEntity.topic.TOPIC_NOTE = "";
      }

      if (isCTA) {
        this.openSelectGenericList(this.ctas, "CTA", "DESCR", "newCtaDesc", "CTA_ID");
      } else {
        this.openSelectGenericList(this.topics, "Topic", "TOPIC_NAME", "newTopicDesc", "TOPIC_ID");
      }
    });
  });
}


  openSelectListHSE() {
    if (this.isEditTopic || this.isSTMRReadonly(this.currStmrHeader)) {
      return;
    }
    this.confirmWithUser(() => {
      
      this.ngZone.run(() => {
        if (this.TOPIC_NOTE.length == 0) {
          this.topicEntity.topic.TOPIC_NOTE = "";
        }
        this.openSelectGenericList(this.hses, "HSE Standards", "DESCR", "newhseDesc", "STD_ID")
    });
    })
  }

  async confirmWithUser(successCallback: () => void) {
    if (this.topicEntity.forms.length == 0) { // No Confirmation Required.
      successCallback()
      return
    }
    // Ask for confirmation
    const alert = await this.alertController.create({
      header: this.translate.instant("Warning"),
      subHeader: this.translate.instant("Changing the topic name / CTA type will delete all the forms associated with this topic. Are you sure you want to continue?"),
      buttons: [
        {
          text: this.translate.instant('Continue'),
          handler: () => {
            successCallback()
          }
        },
        {
          text: this.translate.instant('Cancel'),
          handler: () => {
            console.log('Disagree clicked');
          }
        }
      ],
    });
    await alert.present();
  }


async openSelectGenericList(
  selectArr: any[],
  srchStr: string,
  dispKey: string,
  localDescKey: 'newCtaDesc' | 'newTopicDesc' | 'newhseDesc',
  valueKey: string
) {
  if (!this.prefillData) {
    this.prefillData = await firstValueFrom(
      this.store.select(selectPrefilledData).pipe(
        filter(data => !!data) 
      )
    );
  }
  const listData: any[] = [];
  if (selectArr) {
    for (let i = 0, iLen = selectArr.length; i < iLen; i++) {
      listData.push({
        DisplayString: selectArr[i][dispKey],
        object: selectArr[i],
        isSelected:
          selectArr[i][dispKey] === (this as any)[localDescKey]
            ? AppConstants.BOOL_TRUE
            : AppConstants.BOOL_FALSE
      });
    }
  }
  const rigType = this.prefillData?.RIG_TYPE || '';
  const rigSubType = this.prefillData?.RIG_SUB_TYPE || '';
  const rigNumber = this.prefillData?.RIG_NO || '';
  if (listData.length === 0) {
    let msg = '';
    switch (srchStr) {
      case 'CTA':
        msg = `No CTAs are configured for your site: ${rigNumber} (${rigType}: ${rigSubType})`;
        break;
      case 'Topic':
        msg = `No topics are configured for your site: ${rigNumber} (${rigType}: ${rigSubType})`;
        break;
      case 'HSE STANDARD':
        msg = `No HSE Standards are configured for your site: ${rigNumber} (${rigType}: ${rigSubType})`;
        break;
    }
    if (msg) {
      this.alertService.showAlert(this.translate.instant('Information'), this.translate.instant(msg));
    }
    return;
  }
  const modal = await this.modalController.create({
    component: SelectListPage,
     cssClass: 'full-screen-modal',
    componentProps: {
      listData,
      listDetails: {
        title: 'Select ' + srchStr,
        searchPlaceHolder: 'Search ' + srchStr,
        selectKey: 'isSelected',
        multiSelect: AppConstants.BOOL_FALSE,
        eventName: 'itemSelected',
        hideCloseBtn: AppConstants.BOOL_TRUE
      },
      theme: this.styleTheme
    }
  });
  await modal.present();
  const { data } = await modal.onDidDismiss();
  if (data && data.DisplayString && data.object) {
    this.ngZone.run(() => {
      (this as any)[localDescKey] = data.DisplayString;
      (this.topicEntity.topic as any)[valueKey] = data.object[valueKey];
      this.topicEntity.forms = [];
      this.topicEntity.data = [];
    });
    if (data.object.CTA_ID) {
      this.defaultDescription = data.object.NAME;
      this.getDocsAndTemplates();
    } else if (data.object.STD_ID) {
      this.defaultDescription = data.object.NAME;
      this.getHSEDocs();
    } else {
      this.newCtaDesc = '';
      this.newhseDesc = '';
      this.topicEntity.topic.CTA_ID = '';
      this.ctaDocs = [];
      this.hseDocs = [];
      this.topicEntity.forms = [];
      this.topicEntity.data = [];
      this.defaultDescription = '';
    }
  }
}


  displayNoDataErr() {
    this.alertService.showAlert("Error reading data!", "There was an error reading data. Please contact your administrator.");
  }

  getFormIDForDisplay(formId: string) {
    return formId.startsWith('New') ? this.translate.instant('New') : formId
  }

  async getCTANameFromDB(id: string): Promise<string> {
    return new Promise(async (resolve, reject) => {
      this.ngZone.run(async () => {
        let query = "SELECT DISTINCT NAME FROM CTA_HEADER WHERE CTA_ID='" + id + "'";

        try {
          const result = await this.unviredSDK.dbExecuteStatement(query);
          console.log("TopicsPage", "getCTANameFromDB()", "CTA Name: " + JSON.stringify(result.data));
          if (result.data && result.data.length > 0) {
            for (let i = 0; i < result.data.length; i++) {
              return resolve(result.data[i].NAME);
            }
          } else {
            console.error("TopicsPage", "getCTANameFromDB", "Error while fetching CTA ID from DB : " + JSON.stringify(result.error));
            this.alertService.showAlert("Error", JSON.stringify(result.error));
          }
        } catch (error) {
          const errorMessage = this.getErrorMessage(error);
          console.error("TopicsPage", "getCTANameFromDB", "Error while fetching CTA ID from DB:", error);
          this.alertService.showAlert("Error", errorMessage);
        }
      });
    });
  }


    async getHSENameFromDB(id: string): Promise<string> {
      return new Promise(async (resolve, reject) => {
        this.ngZone.run(async () => {
          let query = "SELECT DISTINCT NAME FROM HSE_STANDARD_HEADER WHERE STD_ID='" + id + "'";
  
          try {
            const result = await this.unviredSDK.dbExecuteStatement(query);
            console.log("TopicsPage", "getHSENameFromDB()", "HSE Name: " + JSON.stringify(result.data));
            if (result.data && result.data.length > 0) {
              for (let i = 0; i < result.data.length; i++) {
                return resolve(result.data[i].NAME);
              }
            } else {
              console.error("TopicsPage", "getHSENameFromDB", "Error while fetching HSE ID from DB : " + JSON.stringify(result.error));
              this.alertService.showAlert("Error", JSON.stringify(result.error));
            }
          } catch (error) {
            const errorMessage = this.getErrorMessage(error);
            console.error("TopicsPage", "getHSENameFromDB", "Error while fetching HSE ID from DB:", error);
            this.alertService.showAlert("Error", errorMessage);
          }
        });
      });
    }

  
  computeMaxDate() {
  const nowUtcSeconds = moment.utc().unix(); 
  const expiryUtcSeconds = this.currStmrHeader.CRTD_ON + (24 * 3600);

  if (nowUtcSeconds < expiryUtcSeconds) {
    this.maxDate = new Date(nowUtcSeconds * 1000);
  } else {
    this.maxDate = new Date(expiryUtcSeconds * 1000);
  }

  this.maxDateStr = this.toLocalISOString(this.maxDate);
  this.maxAllowedTopicTime = moment(this.maxDate).format('MMM DD YYYY HH:mm');

  console.log('Computed maxDate (local):', this.maxDate.toString());
  console.log('Computed maxDateStr (for ion-datetime):', this.maxDateStr);
  console.log('Max allowed time display:', this.maxAllowedTopicTime);
}

  isCurrentTimePast24HrsFromSTMRCreatedTime() {
  const currentDate = moment.utc().unix();
  const twentyFourHoursFromSTMRCreatedTime =
    this.currStmrHeader.CRTD_ON + (24 * 3600);
  return currentDate > twentyFourHoursFromSTMRCreatedTime;
}

setSTMRCreateDate() {
  this.ngZone.run(() => {
    if (!this.currStmrHeader?.CRTD_ON) return;
    this.stmrCreateDate = new Date(this.currStmrHeader.CRTD_ON * 1000);
    if (isNaN(this.stmrCreateDate().getTime())) return;
    if (this.topicStart === undefined) return;
    // Format creation time display
    this.stmrCreatedTime =
      this.monthNames[this.stmrCreateDate.getMonth()] + ' ' +
      this.stmrCreateDate.getDate() + ' ' +
      this.stmrCreateDate.getFullYear() + ' ' +
      this.stmrCreateDate.getHours() + ':' +
      this.stmrCreateDate.getMinutes();
  });
}

  setMaxTopicTime() {
  this.ngZone.run(() => {
    // Recompute max date to ensure we have the latest value
    this.computeMaxDate();
    
    // Create a new Date object from maxDate to avoid reference issues
    const maxDateCopy = new Date(this.maxDate.getTime());
    
    // Set the topic start time using the corrected local ISO string
    this.topicStart = this.toLocalISOString(maxDateCopy);
    
    // Store as Unix timestamp for backend
    this.topicEntity.topic.TOPIC_START = Math.floor(maxDateCopy.getTime() / 1000).toString();
    
    this.isTopicUpdated = true;
    this.errorMessageForInvalidDateTime = '';
    
    console.log('Set max topic time:', {
      maxDate: maxDateCopy.toISOString(),
      topicStart: this.topicStart,
      topicStartTimestamp: this.topicEntity.topic.TOPIC_START
    });
  });
}

// Inside TopicsPage class
public toLocalISOString(d: Date): string {
  // Return "YYYY-MM-DDTHH:mm:ss" in local time (no Z suffix)
  return [
    d.getFullYear(),
    ('0' + (d.getMonth() + 1)).slice(-2),
    ('0' + d.getDate()).slice(-2),
  ].join('-') +
    'T' +
    [
      ('0' + d.getHours()).slice(-2),
      ('0' + d.getMinutes()).slice(-2),
      ('0' + d.getSeconds()).slice(-2),
    ].join(':');
}

  isAllowedToUpdateTopicTime() {
  return !this.currentTimePast24HrsFromSTMRCreatedTime &&
         !this.isSTMRReadonly(this.currStmrHeader);
}

goBack(): void {
  if (this.isTopicUpdated) {
    this.showBackConfirmationAlert('Your unsaved changes will be lost if you don\'t save them.');
  } else {
    this.navigateBackToSTMRDetails();
  }
}


private getErrorMessage(error: any): string {
  if (!error) return 'Unknown error occurred';
  if (typeof error === 'string') return error;
  if (error.message) return error.message;
  if (error.error) return error.error;
  if (error.code && error.message) return `${error.code}: ${error.message}`;
  if (error.data && error.type) return `${error.type}: ${error.data}`;
  const keys = Object.keys(error);
  if (keys.length > 0) {
    return keys.map(key => `${key}: ${error[key]}`).join(', ');
  }
  return 'Error details not available';
}

/** Add these methods inside your component class */
getActiveFormsCount(): number {
  return (this.topicEntity?.forms || []).filter((f: any) => f.P_MODE !== 'D').length;
}

getDocumentsCount(): number {
  const c = this.ctaDocs?.length || 0;
  const h = this.hseDocs?.length || 0;
  return c + h;
}

async getSTMRFormFromDb(){

     let stmrFormData = await this.unviredSDK.dbSelect(AppConstants.TABLE_STMR_FORM ,`STMR_ID = ${this.currStmrHeader.STMR_ID}`);
  if(stmrFormData.data && stmrFormData.data.length > 0){
    let formPresent =  stmrFormData.data
    console.log('formPresent is' , formPresent)

  }else{
    console.log('form not present');
  
  }
}


}

