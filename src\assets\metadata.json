{"name": "FORMS", "description": "Digital Forms", "version": "1.93", "CATEGORY": {"description": "Form Category", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "CATEGORY_HEADER": {"className": "com.pd.forms.be.CATEGORY_HEADER", "header": true, "field": [{"name": "CAT_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCR", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "L_TMPCNT", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "COMPANY": {"description": "Company", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "COMPANY_HEADER": {"className": "com.pd.forms.be.COMPANY_HEADER", "header": true, "field": [{"name": "CODE", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCR", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOGO", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "CONTACT": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "CONTACT_HEADER": {"className": "com.pd.forms.be.CONTACT_HEADER", "header": true, "field": [{"name": "USER_ID", "isGid": true, "length": "50", "mandatory": true, "sqlType": "TEXT"}, {"name": "CONTACT_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "CONTACT_PERSON", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "PHONE_NO", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMAIL_ID", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "CREW": {"description": "Crew", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "CREW_HEADER": {"className": "com.pd.forms.be.CREW_HEADER", "header": true, "field": [{"name": "USER_ID", "isGid": false, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "USER_NAME", "isGid": false, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "PERSON_NO", "description": "Personnel Number", "isGid": true, "length": "8", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESIGNATION", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMP_CODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RIG_NO", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMAIL", "description": "E-Mail Address", "isGid": false, "length": "241", "mandatory": false, "sqlType": "TEXT"}, {"name": "MAINPH", "description": "Telephone Number", "isGid": false, "length": "14", "mandatory": false, "sqlType": "TEXT"}, {"name": "CELPH", "description": "Telephone Number", "isGid": false, "length": "14", "mandatory": false, "sqlType": "TEXT"}, {"name": "GLOBALID", "description": "External Person ID", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "SOURCE", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_MANAGER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "CTA": {"description": "Critical Task Assessment", "attachments": true, "onConflict": "SERVER_WINS", "save": true, "CTA_HEADER": {"className": "com.pd.forms.be.CTA_HEADER", "header": true, "field": [{"name": "CTA_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCR", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "CRTD_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CRTD_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "PBLSH_BY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "PBLSH_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "CTA_TMPLT": {"className": "com.pd.forms.be.CTA_TMPLT", "field": [{"name": "CTA_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "TEMPLATE_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "CTA_ASSGN": {"className": "com.pd.forms.be.CTA_ASSGN", "field": [{"name": "CTA_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "RIG_TYPE", "isGid": true, "length": "20", "mandatory": true, "sqlType": "TEXT"}, {"name": "RIG_SUB_TYPE", "isGid": true, "length": "20", "mandatory": true, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "CTA_DOC": {"className": "com.pd.forms.be.CTA_DOC", "field": [{"name": "DOC_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "CTA_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "CTA_ATTACHMENT": {"description": "Attachment", "className": "com.pd.forms.be.CTA_ATTACHMENT", "attachment": true, "field": [{"name": "UID", "description": "UID", "isGid": true, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "File Name", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "Mime Type", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "description": "Download URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_URL", "description": "External or Internal URL", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL_REQUIRES_AUTH", "description": "External URL Requires Authentication", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCAL_PATH", "description": "Path to the file on the device", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "NO_CACHE", "description": "Do not cache", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SERVER_TIMESTAMP", "description": "Server timestamp", "isGid": false, "length": "20", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TAG1", "description": "Tag 1", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG2", "description": "Tag 2", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG3", "description": "Tag 3", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG4", "description": "Tag 4", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG5", "description": "Tag 5", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTACHMENT_STATUS", "description": "Status", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "AUTO_DOWNLOAD", "description": "Auto Download Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARAM", "description": "Name of the param", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "description": "Message from User", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "C_CAT_PROFILE": {"description": "Catalog Profile", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "C_CAT_PROFILE_HEADER": {"className": "com.pd.forms.be.C_CAT_PROFILE_HEADER", "header": true, "field": [{"name": "CAT_PROFILE", "description": "Catalog Profile", "isGid": true, "length": "9", "mandatory": true, "sqlType": "TEXT"}, {"name": "CAT_PROFILE_TXT", "description": "Catalog Profile Text", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}, {"name": "CATALOG_TYPE", "description": "Catalog", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CODE_GROUP", "description": "Code Group", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}]}}, "C_CODE": {"description": "Code", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "C_CODE_HEADER": {"className": "com.pd.forms.be.C_CODE_HEADER", "header": true, "field": [{"name": "CATALOG_TYPE", "description": "Catalog", "isGid": true, "length": "1", "mandatory": true, "sqlType": "TEXT"}, {"name": "CODE_GROUP", "description": "Code Group", "isGid": true, "length": "8", "mandatory": true, "sqlType": "TEXT"}, {"name": "CODE", "description": "Code", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "CODE_TEXT", "description": "Short Text for Code (Up to 40 Characters in Length)", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}]}}, "C_CODE_GROUP": {"description": "Catalog Code Group", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "C_CODE_GROUP_HEADER": {"className": "com.pd.forms.be.C_CODE_GROUP_HEADER", "header": true, "field": [{"name": "CATALOG_TYPE", "description": "Catalog", "isGid": true, "length": "1", "mandatory": true, "sqlType": "TEXT"}, {"name": "CODE_GROUP", "description": "Code Group", "isGid": true, "length": "8", "mandatory": true, "sqlType": "TEXT"}, {"name": "CODE_GROUP_TEXT", "description": "Short Description of the Code Group", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}]}}, "C_NOTIF_TYPE": {"description": "Notification Type", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "C_NOTIF_TYPE_HEADER": {"className": "com.pd.forms.be.C_NOTIF_TYPE_HEADER", "header": true, "field": [{"name": "NOTIF_TYPE", "description": "Notification Type", "isGid": true, "length": "2", "mandatory": true, "sqlType": "TEXT"}, {"name": "SHORT_TEXT", "description": "Notification Type Texts", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "NOTIF_CATEGORY", "description": "Notification Category", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "CATALOG_PROFILE", "description": "Catalog Profile", "isGid": false, "length": "9", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRIORITY_TYPE", "description": "Priority Type", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "STATUS_PROFILE_NOTIF", "description": "Status Profile", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "STATUS_PROFILE_TASK", "description": "Status Profile", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "CAT_TYPE_DAMAGE", "description": "Catalog Type - Problems/Defects", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CAT_TYPE_CAUSE", "description": "Catalog Type - Causes", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CAT_TYPE_TASK", "description": "Catalog Type - Tasks", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CAT_TYPE_ACTIVITY", "description": "Catalog Type - Activities", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CAT_TYPE_OBJECT_PARTS", "description": "Catalog Type - Object Parts", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CAT_TYPE_CODING", "description": "Catalog Type - Coding", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "C_PRIORITY": {"description": "Priority", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "C_PRIORITY_HEADER": {"className": "com.pd.forms.be.C_PRIORITY_HEADER", "header": true, "field": [{"name": "PRIORITY_TYPE", "description": "Priority Type", "isGid": true, "length": "2", "mandatory": true, "sqlType": "TEXT"}, {"name": "PRIORITY", "description": "Priority", "isGid": true, "length": "1", "mandatory": true, "sqlType": "TEXT"}, {"name": "PRIORITY_DESC", "description": "Priority Text", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}]}}, "C_RIG_WRK_CNTR": {"description": "Rig Work Center", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "C_RIG_WRK_CNTR_HEADER": {"className": "com.pd.forms.be.C_RIG_WRK_CNTR_HEADER", "header": true, "field": [{"name": "RIG_NO", "description": "Functional Location Label", "isGid": true, "length": "40", "mandatory": true, "sqlType": "TEXT"}, {"name": "WORK_CENTER", "description": "Work Center", "isGid": true, "length": "8", "mandatory": true, "sqlType": "TEXT"}, {"name": "WORK_CENTER_DESC", "description": "Object Name", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}]}}, "C_WORK_CENTER": {"description": "Work Center Profile and Details", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "C_WORK_CENTER_HEADER": {"className": "com.pd.forms.be.C_WORK_CENTER_HEADER", "header": true, "field": [{"name": "WORK_CENTER", "description": "Work center", "isGid": true, "length": "8", "mandatory": true, "sqlType": "TEXT"}, {"name": "WORK_CENTER_DESC", "description": "Object Name", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANT", "description": "Plant", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "PLANT_DESC", "description": "Name", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}]}}, "C_WRK_CNTR": {"description": "Work Center - For staging only", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "C_WRK_CNTR_HEADER": {"className": "com.pd.forms.be.C_WRK_CNTR_HEADER", "header": true, "field": [{"name": "FLOC_CAT", "description": "Functional location category", "isGid": true, "length": "1", "mandatory": true, "sqlType": "TEXT"}, {"name": "PLANT", "description": "Plant", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "WORK_CENTER", "description": "Work Center", "isGid": true, "length": "8", "mandatory": true, "sqlType": "TEXT"}, {"name": "WORK_CENTER_DESC", "description": "Object Name", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "IGNORE", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "FORM": {"description": "Form", "attachments": true, "onConflict": "SERVER_WINS", "save": true, "FORM_HEADER": {"className": "com.pd.forms.be.FORM_HEADER", "header": true, "field": [{"name": "FORM_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "VER_ID", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "CRTD_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CRTD_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "SUBM_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "DATE_COMP", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "COMPANY", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "RIG_NO", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "OPERATOR", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMMENTS", "description": "", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "FORM_STATUS", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_SYNC_USER", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_SYNC_TIME", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TIME_ZONE", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_ARCHIVED", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "FORM_DATA": {"className": "com.pd.forms.be.FORM_DATA", "field": [{"name": "FORM_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "DATA", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "FORM_ACTION": {"className": "com.pd.forms.be.FORM_ACTION", "field": [{"name": "FORM_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "ACTION_CODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "FORM_ATTACHMENT": {"description": "Attachment", "className": "com.pd.forms.be.FORM_ATTACHMENT", "attachment": true, "field": [{"name": "UID", "description": "UID", "isGid": true, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "File Name", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "Mime Type", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "description": "Download URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_URL", "description": "External or Internal URL", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL_REQUIRES_AUTH", "description": "External URL Requires Authentication", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCAL_PATH", "description": "Path to the file on the device", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "NO_CACHE", "description": "Do not cache", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SERVER_TIMESTAMP", "description": "Server timestamp", "isGid": false, "length": "20", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TAG1", "description": "Tag 1", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG2", "description": "Tag 2", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG3", "description": "Tag 3", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG4", "description": "Tag 4", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG5", "description": "Tag 5", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTACHMENT_STATUS", "description": "Status", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "AUTO_DOWNLOAD", "description": "Auto Download Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARAM", "description": "Name of the param", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "description": "Message from User", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "FORM_SCHD_ALERT": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "FORM_SCHD_ALERT_HEADER": {"className": "com.pd.forms.be.FORM_SCHD_ALERT_HEADER", "header": true, "field": [{"name": "ALERT_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "ALERT_CRTD_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "COMPANY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "RIG_NO", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "TMPLT_ID", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_COMP", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "NEXT_DUE", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "FORM_ID", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "DATE_COMP", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "IS_READ", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "FORM_SCHD_LOG_V": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "FORM_SCHD_LOG_V_HEADER": {"className": "com.pd.forms.be.FORM_SCHD_LOG_V_HEADER", "header": true, "field": [{"name": "COMPANY", "isGid": true, "length": "50", "mandatory": true, "sqlType": "TEXT"}, {"name": "RIG_NO", "isGid": true, "length": "50", "mandatory": true, "sqlType": "TEXT"}, {"name": "TMPLT_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "FORM_ID", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "SCHD_FREQ", "isGid": false, "length": "10", "mandatory": false, "sqlType": "INTEGER"}, {"name": "DATE_COMP", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "OVERDUE", "isGid": false, "length": "10", "mandatory": false, "sqlType": "INTEGER"}]}}, "FORM_SCHEDULE": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "FORM_SCHEDULE_HEADER": {"className": "com.pd.forms.be.FORM_SCHEDULE_HEADER", "header": true, "field": [{"name": "SCHD_FREQ", "isGid": true, "length": "10", "mandatory": true, "sqlType": "INTEGER"}, {"name": "DESCR", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}]}}, "HSE_STANDARD": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "HSE_STANDARD_HEADER": {"className": "com.pd.forms.be.HSE_STANDARD_HEADER", "header": true, "field": [{"name": "STD_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCR", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHGD_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHGD_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "PBLSH_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "PBLSH_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "HSE_STANDARD_DOC": {"className": "com.pd.forms.be.HSE_STANDARD_DOC", "field": [{"name": "STD_ID", "isGid": true, "length": "20", "mandatory": true, "sqlType": "TEXT"}, {"name": "DOC_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_ARCHIVE_CONTEXT": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": false, "INPUT_ARCHIVE_CONTEXT_HEADER": {"className": "com.pd.forms.be.INPUT_ARCHIVE_CONTEXT_HEADER", "header": true, "field": [{"name": "FORM_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "STMR_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_FORM_CONTEXT": {"description": "Form Context", "attachments": false, "onConflict": "SERVER_WINS", "save": false, "INPUT_FORM_CONTEXT_HEADER": {"className": "com.pd.forms.be.INPUT_FORM_CONTEXT_HEADER", "header": true, "field": [{"name": "FORM_ID", "description": "Form ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "VER_ID", "description": "Template Version ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_NOTIF_CONTEXT": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INPUT_NOTIF_CONTEXT_HEADER": {"className": "com.pd.forms.be.INPUT_NOTIF_CONTEXT_HEADER", "header": true, "field": [{"name": "COMP_CODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RIG_NO", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TMPLT_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "FORM_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CRTD_BY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_PDF_REQUEST": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": false, "INPUT_PDF_REQUEST_HEADER": {"className": "com.pd.forms.be.INPUT_PDF_REQUEST_HEADER", "header": true, "field": [{"name": "FORM_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "DOWNLOAD", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMAIL", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "REPLY_TO", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "SUBJECT", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_REPORT_CONTEXT": {"description": "Report Context", "attachments": false, "onConflict": "SERVER_WINS", "save": false, "INPUT_REPORT_CONTEXT_HEADER": {"className": "com.pd.forms.be.INPUT_REPORT_CONTEXT_HEADER", "header": true, "field": [{"name": "START_DATE", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "END_DATE", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "COMPANY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "RIG_NO", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "TMPLT_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "FORM_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CRTD_BY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "SUBM_BY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_COMP", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "OPERATOR", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_SKIP", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_RIG_CONTEXT": {"description": "<PERSON><PERSON> Context", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INPUT_RIG_CONTEXT_HEADER": {"className": "com.pd.forms.be.INPUT_RIG_CONTEXT_HEADER", "header": true, "field": [{"name": "RIG_NO", "description": "RIg No", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RIG_TYPE", "description": "Rig Type", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RIG_SUB_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMP_CODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "DEVICE_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_SP_META_CONTEXT": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INPUT_SP_META_CONTEXT_HEADER": {"className": "com.pd.forms.be.INPUT_SP_META_CONTEXT_HEADER", "header": true, "field": [{"name": "SP_SITE_ID", "isGid": false, "length": "36", "mandatory": false, "sqlType": "TEXT"}, {"name": "DOC_LIBRARY", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "DEVICE_NAME", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "USER_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_STMR_CONTEXT": {"description": "STMR context", "attachments": false, "onConflict": "SERVER_WINS", "save": false, "INPUT_STMR_CONTEXT_HEADER": {"className": "com.pd.forms.be.INPUT_STMR_CONTEXT_HEADER", "header": true, "field": [{"name": "STMR_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "INPUT_TMPLT_CONTEXT": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INPUT_TMPLT_CONTEXT_HEADER": {"description": "Description", "className": "com.pd.forms.be.INPUT_TMPLT_CONTEXT_HEADER", "header": true, "field": [{"name": "TMPLT_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "VER_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "LOCATION": {"description": "Location (Rig, Office, Shop)", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "LOCATION_HEADER": {"className": "com.pd.forms.be.LOCATION_HEADER", "header": true, "field": [{"name": "LOC_ID", "isGid": true, "length": "40", "mandatory": true, "sqlType": "TEXT"}, {"name": "LOC_TYPE", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOC_SUB_TYPE", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMP_CODE", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMP_NAME", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "WORK_CENTER", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANT", "description": "Plant", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}]}}, "LOCATION_CREW": {"description": "Location Crew", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "LOCATION_CREW_HEADER": {"className": "com.pd.forms.be.LOCATION_CREW_HEADER", "header": true, "field": [{"name": "LOC_ID", "isGid": true, "length": "40", "mandatory": true, "sqlType": "TEXT"}, {"name": "GLOBALID", "isGid": true, "length": "20", "mandatory": true, "sqlType": "TEXT"}, {"name": "USER_ID", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "USER_NAME", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "PERSON_NO", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESIGNATION", "isGid": false, "length": "25", "mandatory": false, "sqlType": "TEXT"}, {"name": "EMAIL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MAINPH", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "CELPH", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "SOURCE", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_MANAGER", "description": "General Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "LOCATION_OPERATOR": {"description": "Location Operator", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "LOCATION_OPERATOR_HEADER": {"className": "com.pd.forms.be.LOCATION_OPERATOR_HEADER", "header": true, "field": [{"name": "LOC_ID", "isGid": true, "length": "40", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "50", "mandatory": true, "sqlType": "TEXT"}, {"name": "PHONE", "isGid": false, "length": "30", "mandatory": true, "sqlType": "TEXT"}, {"name": "REP", "description": "Location Operator", "isGid": false, "length": "50", "mandatory": true, "sqlType": "TEXT"}, {"name": "LOCATION", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "UWI", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "WELL_NAME", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "BUPA_ID", "description": "Customer Number", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}]}}, "MASTER_DATA": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "MASTER_DATA_HEADER": {"className": "com.pd.forms.be.MASTER_DATA_HEADER", "header": true, "field": [{"name": "DATA_TYPE", "isGid": true, "length": "50", "mandatory": true, "sqlType": "TEXT"}, {"name": "DATA_VALUE", "isGid": false, "length": "2147483647", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "NOTIF": {"description": "Notification Header Details", "attachments": true, "onConflict": "SERVER_WINS", "save": true, "NOTIF_HEADER": {"className": "com.pd.forms.be.NOTIF_HEADER", "header": true, "field": [{"name": "NOTIF_NO", "description": "Notification No", "isGid": true, "length": "12", "mandatory": true, "sqlType": "TEXT"}, {"name": "NOTIF_TYPE", "description": "Notification Type", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "NOTIF_TYPE_TEXT", "description": "Notification Type Texts", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "USER_RESPONSIBLE", "description": "Partner", "isGid": false, "length": "12", "mandatory": false, "sqlType": "TEXT"}, {"name": "SHORT_TEXT", "description": "Short Text", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRIORITY", "description": "Priority", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRIORITY_DESC", "description": "Priority Text", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "FUNC_LOC_ID", "description": "Functional Location", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}, {"name": "FUNC_LOC_DESC", "description": "Description of functional location", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "EQUIP_ID", "description": "Equipment Number", "isGid": false, "length": "18", "mandatory": false, "sqlType": "TEXT"}, {"name": "EQUIP_DESC", "description": "Description of technical object", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSEMBLY", "description": "Assembly", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "ASSEMBLY_DESC", "description": "Description of PM Assembly", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "MALFN_START_DAT", "description": "Start of Malfunction (Date)", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "MALFN_START_TIME", "description": "Start of Malfunction (Time)", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "MALFN_END_DAT", "description": "End of Malfunction (Date)", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "MALFN_END_TIM", "description": "End of Malfunction (Time)", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "REVISION", "description": "Revision for Plant Maintenance and Customer Service", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "REVISION_DESC", "description": "Revision description", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "BREAKDWN_FLAG", "description": "Breakdown Indicator", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "BREAKDWN_DURTN", "description": "Breakdown Duration", "isGid": false, "length": "6", "mandatory": false, "sqlType": "REAL"}, {"name": "BREAKDWN_DURTN_UNIT", "description": "Unit for Breakdown Duration", "isGid": false, "length": "3", "mandatory": false, "sqlType": "TEXT"}, {"name": "BRKDWN_DUR_UNIT_COD", "description": "ISO code for unit of measurement", "isGid": false, "length": "3", "mandatory": false, "sqlType": "TEXT"}, {"name": "NOTIF_DAT", "description": "Date of Notification", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "NOTIF_TIM", "description": "Time of Notification", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "REQ_START_DAT", "description": "Required start date", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "REQ_START_TIM", "description": "Required Start Time", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "REQ_END_DAT", "description": "Required End Date", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "REQ_END_TIM", "description": "Requested End Time", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "ORDR_ID", "description": "Order Number", "isGid": false, "length": "12", "mandatory": false, "sqlType": "TEXT"}, {"name": "MAINTAIN_PLANT", "description": "Maintenance Plant", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "MAINTAIN_PLANT_DESC", "description": "Name", "isGid": false, "length": "30", "mandatory": false, "sqlType": "TEXT"}, {"name": "MAINTAIN_PLNG_PLANT", "description": "Maintenance Planning Plant", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANNER_GRP", "description": "Planner Group for Customer Service and Plant Maintenance", "isGid": false, "length": "3", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANNER_GRP_DESC", "description": "Name of the Maintenance Planner Group", "isGid": false, "length": "18", "mandatory": false, "sqlType": "TEXT"}, {"name": "MAIN_WORK_CNTR", "description": "Main work center for maintenance tasks", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "MAIN_WORK_CNTR_DESC", "description": "Short Text for Work Center", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "MAIN_WRK_CNTR_PLANT", "description": "Plant associated with main work center", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "PERSON_NO", "description": "Personnel Number", "isGid": false, "length": "8", "mandatory": false, "sqlType": "INTEGER"}, {"name": "PERSON_NAME", "description": "Name of Person who Created the Object", "isGid": false, "length": "12", "mandatory": false, "sqlType": "TEXT"}, {"name": "REPRTD_BY", "description": "Name of Person Reporting Notification", "isGid": false, "length": "12", "mandatory": false, "sqlType": "TEXT"}, {"name": "CAT_TYPE", "description": "Catalog Type - Coding", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CODE_GRP", "description": "Code Group - Coding", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "CODE_GRP_DESC", "description": "Short Description of the Code Group", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "CODE", "description": "Coding", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "CODE_DESC", "description": "Short Text for Code (Up to 40 Characters in Length)", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "EFFECT", "description": "Effect on Operation", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "AVL_BEFORE_MALFN", "description": "Availability Before Malfunction", "isGid": false, "length": "3", "mandatory": false, "sqlType": "INTEGER"}, {"name": "COND_BEFORE_MALFN", "description": "System Condition Before Malfunction", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "AVL_AFTER_MALFN", "description": "Availability After Malfunction", "isGid": false, "length": "3", "mandatory": false, "sqlType": "INTEGER"}, {"name": "COND_AFTER_MALFN", "description": "System Condition After Malfunction", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SYST_STAT", "description": "System Status", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "USER_STATUS", "description": "Field for user status display", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "HISTORY_FLAG", "description": "General Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "ALT_WORK_CNTR", "description": "Work Center", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "ALT_WORK_CNTR_DESC", "description": "Short Text for Work Center", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "ALT_WRK_CNTR_PLANT", "description": "Plant", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "ALT_WORK_CNTR3", "description": "Work Center", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "ALT_WORK_CNTR3_DESC", "description": "Short Text for Work Center", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "ALT_WRK_CNTR3_PLANT", "description": "Plant", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "ALT_WORK_CNTR4", "description": "Work Center", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "ALT_WORK_CNTR4_DESC", "description": "Short Text for Work Center", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "ALT_WRK_CNTR4_PLANT", "description": "Plant", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "description": "Processing mode for incoming values", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "NOTIF_LONG_TEXT_ADD": {"className": "com.pd.forms.be.NOTIF_LONG_TEXT_ADD", "field": [{"name": "NOTIF_NO", "description": "Notification No", "isGid": true, "length": "12", "mandatory": true, "sqlType": "TEXT"}, {"name": "OBJ_KEY", "description": "Item Key: Defect; Cause, Action...", "isGid": true, "length": "8", "mandatory": true, "sqlType": "INTEGER"}, {"name": "NOTIF", "description": "Notification Header Details", "isGid": true, "length": "10", "mandatory": true, "sqlType": "TEXT"}, {"name": "LONG_TEXT_LENGTH", "description": "Long Text Length", "isGid": false, "length": "2", "mandatory": false, "sqlType": "INTEGER"}, {"name": "LONG_TXT", "description": "Long text", "isGid": false, "length": "9999", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "description": "Processing mode for incoming values", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "NOTIF_LONG_TEXT_VIEW": {"className": "com.pd.forms.be.NOTIF_LONG_TEXT_VIEW", "field": [{"name": "NOTIF_NO", "description": "Notification No", "isGid": true, "length": "12", "mandatory": true, "sqlType": "TEXT"}, {"name": "OBJ_KEY", "description": "Item Key: Defect; Cause, Action...", "isGid": true, "length": "8", "mandatory": true, "sqlType": "INTEGER"}, {"name": "OBJ_TYPE", "description": "Object Type", "isGid": true, "length": "10", "mandatory": true, "sqlType": "TEXT"}, {"name": "LONG_TEXT_LENGTH", "description": "Long Text Length", "isGid": false, "length": "2", "mandatory": false, "sqlType": "INTEGER"}, {"name": "LONG_TXT", "description": "Long text", "isGid": false, "length": "9999", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "description": "Processing mode for incoming values", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}, "NOTIF_ATTACHMENT": {"description": "Attachment", "className": "com.pd.forms.be.NOTIF_ATTACHMENT", "attachment": true, "field": [{"name": "UID", "description": "UID", "isGid": true, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "File Name", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "Mime Type", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "description": "Download URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_URL", "description": "External or Internal URL", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL_REQUIRES_AUTH", "description": "External URL Requires Authentication", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCAL_PATH", "description": "Path to the file on the device", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "NO_CACHE", "description": "Do not cache", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SERVER_TIMESTAMP", "description": "Server timestamp", "isGid": false, "length": "20", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TAG1", "description": "Tag 1", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG2", "description": "Tag 2", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG3", "description": "Tag 3", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG4", "description": "Tag 4", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG5", "description": "Tag 5", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTACHMENT_STATUS", "description": "Status", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "AUTO_DOWNLOAD", "description": "Auto Download Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARAM", "description": "Name of the param", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "description": "Message from User", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "NOTIFICATION": {"description": "SAP Notificaiton", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "NOTIFICATION_HEADER": {"className": "com.pd.forms.be.NOTIFICATION_HEADER", "header": true, "field": [{"name": "NOTIF_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "COMPANY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "RIG_NO", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "FORM_ID", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "NOTIF_NO", "isGid": false, "length": "12", "mandatory": false, "sqlType": "TEXT"}, {"name": "NOTIF_TYPE", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRIORITY", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}, {"name": "WRKTYP_GRP", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "WRKTYP_CODE", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "CRTD_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CRTD_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CHGD_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHGD_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CMP_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "REASON", "isGid": false, "length": "1000", "mandatory": false, "sqlType": "TEXT"}, {"name": "ACTION", "isGid": false, "length": "1000", "mandatory": false, "sqlType": "TEXT"}, {"name": "STATUS", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "MAIN_WRK_CTR", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "ALT_WRK_CTR2", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "ALT_WRK_CTR3", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "ALT_WRK_CTR4", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "REQ_START_DATE", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "REQ_END_DATE", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TIME_ZONE", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "ERROR", "isGid": false, "length": "1000", "mandatory": false, "sqlType": "TEXT"}]}}, "OPERATOR": {"description": "Operator", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "OPERATOR_HEADER": {"description": "", "className": "com.pd.forms.be.OPERATOR_HEADER", "header": true, "field": [{"name": "NAME", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "PHONE", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "REP", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "LOCATION", "description": "Text (100 characters)", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "UWI", "description": "Text (100 characters)", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "WELL_NAME", "description": "Text (100 characters)", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "BUPA_ID", "description": "Customer Number", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}]}}, "PDF_REQUEST": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "PDF_REQUEST_HEADER": {"className": "com.pd.forms.be.PDF_REQUEST_HEADER", "header": true, "field": [{"name": "REQUEST_ID", "isGid": true, "length": "19", "mandatory": true, "sqlType": "INTEGER"}, {"name": "REQUEST_STATUS", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "USER_ID", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTACHMENT_ID", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "ERROR_MSG", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "RELEASE_NOTE": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "RELEASE_NOTE_HEADER": {"description": "", "className": "com.pd.forms.be.RELEASE_NOTE_HEADER", "header": true, "field": [{"name": "NOTE_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "NOTE_TYPE", "description": "APP, TMPLT", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "TITLE", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCRIPTION", "isGid": false, "length": "1000", "mandatory": false, "sqlType": "TEXT"}, {"name": "TMPLT_ID", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "VER_NO", "isGid": false, "length": "10", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CHGD_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CHGD_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "PBLSH_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "PBLSH_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "TIME_ZONE", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_ACTIVE", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_READ", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "REPORT": {"description": "Report", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "REPORT_HEADER": {"className": "com.pd.forms.be.REPORT_HEADER", "header": true, "field": [{"name": "REPORT_ID", "description": "", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "STMR_ID", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FORM_ID", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMPANY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RIG_NO", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CRTD_BY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CRTD_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "SUBM_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "SUBM_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "NAME", "description": "", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TIME_ZONE", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_SELECTED", "isGid": false, "length": "0", "mandatory": false, "sqlType": "INTEGER"}]}, "REPORT_ITEM": {"className": "com.pd.forms.be.REPORT_ITEM", "field": [{"name": "REPORT_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "ITEM_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "STMR_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TOPIC_NO", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TOPIC_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TOPIC_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TOPIC_NOTE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TOPIC_START", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CTA_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "CTA_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "STD_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "STD_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "RIG": {"description": "<PERSON><PERSON>", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "RIG_HEADER": {"className": "com.pd.forms.be.RIG_HEADER", "header": true, "field": [{"name": "RIG_NO", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCR", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMP_CODE", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "RIG_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "RIG_SUB_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "WORK_CENTER", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "RIG_MANAGER": {"className": "com.pd.forms.be.RIG_MANAGER", "field": [{"name": "RIG_NO", "description": "Functional Location Label", "isGid": true, "length": "40", "mandatory": true, "sqlType": "TEXT"}, {"name": "COMP_CODE", "description": "Company Code", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "ROLE", "description": "Characteristic Value", "isGid": true, "length": "70", "mandatory": true, "sqlType": "TEXT"}, {"name": "USER_ID", "description": "User Name", "isGid": false, "length": "12", "mandatory": false, "sqlType": "TEXT"}, {"name": "PERSON_NO", "description": "Personnel Number", "isGid": true, "length": "8", "mandatory": true, "sqlType": "INTEGER"}, {"name": "USER_NAME", "description": "Text (100 characters)", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "RIG_TYPE": {"description": "Rig Type", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "RIG_TYPE_HEADER": {"className": "com.pd.forms.be.RIG_TYPE_HEADER", "header": true, "field": [{"name": "RIG_TYPE", "isGid": true, "length": "20", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCR", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}]}}, "SETTING": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "SETTING_HEADER": {"description": "Description", "className": "com.pd.forms.be.SETTING_HEADER", "header": true, "field": [{"name": "NAME", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "VALUE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "SHOP_TYPE": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "SHOP_TYPE_HEADER": {"className": "com.pd.forms.be.SHOP_TYPE_HEADER", "header": true, "field": [{"name": "SHOP_TYPE", "isGid": true, "length": "20", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCR", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}]}}, "SP_CONFIG": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "SP_CONFIG_HEADER": {"description": "Description", "className": "com.pd.forms.be.SP_CONFIG_HEADER", "header": true, "field": [{"name": "NAME", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "VALUE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "SP_DEVICE_FOLDER": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "SP_DEVICE_FOLDER_HEADER": {"className": "com.pd.forms.be.SP_DEVICE_FOLDER_HEADER", "header": true, "field": [{"name": "FOLDER_ID", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "FOLDER_NAME", "isGid": false, "length": "300", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONTEXT", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHGD_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "SP_DEVICE_STATE": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "SP_DEVICE_STATE_HEADER": {"description": "", "className": "com.pd.forms.be.SP_DEVICE_STATE_HEADER", "header": true, "field": [{"name": "DEVICE_NAME", "isGid": true, "length": "50", "mandatory": true, "sqlType": "TEXT"}]}, "SP_DEVICE_STATE_ITEM": {"description": "", "className": "com.pd.forms.be.SP_DEVICE_STATE_ITEM", "field": [{"name": "DEVICE_NAME", "isGid": true, "length": "50", "mandatory": true, "sqlType": "TEXT"}, {"name": "SP_SITE_ID", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "DOC_LIBRARY", "isGid": true, "length": "100", "mandatory": true, "sqlType": "TEXT"}, {"name": "DOC_ID", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "DOC_NAME", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "VERSION_C", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "VERSION_S", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_FOLDER", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "MODIFIED_BY_C", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MODIFIED_BY_S", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MODIFED_AT_C", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "MODIFED_AT_S", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "IN_SYNC", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "SP_DEVICE_USER": {"description": "", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "SP_DEVICE_USER_HEADER": {"className": "com.pd.forms.be.SP_DEVICE_USER_HEADER", "header": true, "field": [{"name": "DEVICE_NAME", "isGid": true, "length": "50", "mandatory": true, "sqlType": "TEXT"}, {"name": "USER_ID", "isGid": true, "length": "50", "mandatory": true, "sqlType": "TEXT"}, {"name": "SYNC_DATE", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}]}}, "SP_SITE": {"description": "SharePoint Site", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "SP_SITE_HEADER": {"description": "", "className": "com.pd.forms.be.SP_SITE_HEADER", "header": true, "field": [{"name": "SP_SITE_ID", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "SP_SITE_NAME", "isGid": false, "length": "500", "mandatory": false, "sqlType": "TEXT"}, {"name": "SP_SITE_DESC", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "LIB_COUNT", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "SP_SITE_LIB": {"className": "com.pd.forms.be.SP_SITE_LIB", "field": [{"name": "SP_SITE_ID", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "DOC_LIBRARY", "isGid": true, "length": "100", "mandatory": true, "sqlType": "TEXT"}, {"name": "FOLDER_NAME", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "SP_SITE_META": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "SP_SITE_META_HEADER": {"description": "Description", "className": "com.pd.forms.be.SP_SITE_META_HEADER", "header": true, "field": [{"name": "SP_SITE_ID", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "DOC_LIBRARY", "isGid": true, "length": "100", "mandatory": true, "sqlType": "TEXT"}, {"name": "DEVICE_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "SP_SITE_DOC": {"className": "com.pd.forms.be.SP_SITE_DOC", "field": [{"name": "SP_SITE_ID", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "DOC_LIBRARY", "isGid": true, "length": "100", "mandatory": true, "sqlType": "TEXT"}, {"name": "DOC_ID", "isGid": true, "length": "36", "mandatory": true, "sqlType": "TEXT"}, {"name": "VERSION", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "DOC_NAME", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_FOLDER", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "MODIFIED_BY", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MODIFED_AT", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCAL_PATH", "isGid": false, "length": "1000", "mandatory": false, "sqlType": "TEXT"}]}}, "STMR": {"description": "STMR", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "STMR_HEADER": {"description": "", "className": "com.pd.forms.be.STMR_HEADER", "header": true, "field": [{"name": "STMR_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "RIG_NO", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "RIG_TYPE", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "RIG_SUB_TYPE", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "RIG_MGR_EMAIL", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMPANY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CRTD_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CRTD_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "SUBM_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "DATE_COMP", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "WELL_LOC", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "OPERATOR", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "PROVINCE", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "SHIFT", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "SHIFT_TIME", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "CHAIRED_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ONSITE_MGR", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ONSITE_MGR_SIGN", "isGid": false, "length": "2147483647", "mandatory": false, "sqlType": "TEXT"}, {"name": "ONSITE_SUP", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "ONSITE_SUP_SIGN", "isGid": false, "length": "2147483647", "mandatory": false, "sqlType": "TEXT"}, {"name": "STMR_STATUS", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_SYNC_USER", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_SYNC_TIME", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TIME_ZONE", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "STMR_CREW": {"className": "com.pd.forms.be.STMR_CREW", "field": [{"name": "STMR_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "CREW_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "CREW_POS", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREW_NAME", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREW_SIGN", "isGid": false, "length": "2147483647", "mandatory": false, "sqlType": "TEXT"}, {"name": "CREW_TYPE", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "STMR_TOPIC": {"className": "com.pd.forms.be.STMR_TOPIC", "field": [{"name": "STMR_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "TOPIC_NO", "isGid": true, "length": "10", "mandatory": true, "sqlType": "INTEGER"}, {"name": "TOPIC_ID", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "TOPIC_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TOPIC_NOTE", "isGid": false, "length": "2147483647", "mandatory": false, "sqlType": "TEXT"}, {"name": "TOPIC_START", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "CTA_ID", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "CTA_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "STD_ID", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "STD_TYPE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TOPIC_STATUS", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_SYNC_USER", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_SYNC_TIME", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "STMR_FORM": {"className": "com.pd.forms.be.STMR_FORM", "field": [{"name": "STMR_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "TOPIC_NO", "isGid": true, "length": "10", "mandatory": true, "sqlType": "INTEGER"}, {"name": "FORM_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "VER_ID", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CRTD_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CRTD_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "SUBM_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "DATE_COMP", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "COMPANY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "RIG_NO", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMMENTS", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "FORM_STATUS", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_SYNC_USER", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "LAST_SYNC_TIME", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "TIME_ZONE", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "STMR_FORM_DATA": {"className": "com.pd.forms.be.STMR_FORM_DATA", "field": [{"name": "STMR_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "TOPIC_NO", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "FORM_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "DATA", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "STMR_ACTION": {"className": "com.pd.forms.be.STMR_ACTION", "field": [{"name": "STMR_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "FORM_ID", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "ACTION_CODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "TMPLT": {"description": "Template", "attachments": true, "onConflict": "SERVER_WINS", "save": true, "TMPLT_HEADER": {"className": "com.pd.forms.be.TMPLT_HEADER", "header": true, "field": [{"name": "TMPLT_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "NAME", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "DESCR", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "CAT_ID", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "DB_SCHEMA", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRIMARY_TABLE", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "FORM_ID_PREFIX", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_ACTIVE", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHGD_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHGD_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "PBLSH_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "PBLSH_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "SCHD_FREQ", "isGid": false, "length": "10", "mandatory": false, "sqlType": "INTEGER"}, {"name": "L_VER_NO", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "L_VER_ID", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "L_CRTD_BY", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "L_CRTD_ON", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "L_READ_FLAG", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_DATA_ENH", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "ARCHIVE_AFTER", "isGid": false, "length": "10", "mandatory": false, "sqlType": "INTEGER"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "TMPLT_ASSGN": {"className": "com.pd.forms.be.TMPLT_ASSGN", "field": [{"name": "ASSGN_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "TMPLT_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "ASSGN_TYPE", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "VAL1", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "VAL2", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "VAL3", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "VAL4", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "TMPLT_VER": {"className": "com.pd.forms.be.TMPLT_VER", "field": [{"name": "VER_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "VER_NO", "isGid": false, "length": "10", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TMPLT_ID", "isGid": true, "length": "32", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCR", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "CRTD_BY", "isGid": false, "length": "10", "mandatory": true, "sqlType": "TEXT"}, {"name": "CRTD_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "STATUS", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}, "TMPLT_ATTACHMENT": {"description": "Attachment", "className": "com.pd.forms.be.TMPLT_ATTACHMENT", "attachment": true, "field": [{"name": "UID", "description": "UID", "isGid": true, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "FILE_NAME", "description": "File Name", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "MIME_TYPE", "description": "Mime Type", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "description": "Download URL", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXTERNAL_URL", "description": "External or Internal URL", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL_REQUIRES_AUTH", "description": "External URL Requires Authentication", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "LOCAL_PATH", "description": "Path to the file on the device", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "NO_CACHE", "description": "Do not cache", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "SERVER_TIMESTAMP", "description": "Server timestamp", "isGid": false, "length": "20", "mandatory": false, "sqlType": "INTEGER"}, {"name": "TAG1", "description": "Tag 1", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG2", "description": "Tag 2", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG3", "description": "Tag 3", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG4", "description": "Tag 4", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "TAG5", "description": "Tag 5", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "ATTACHMENT_STATUS", "description": "Status", "isGid": false, "length": "32", "mandatory": false, "sqlType": "TEXT"}, {"name": "AUTO_DOWNLOAD", "description": "Auto Download Flag", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "PARAM", "description": "Name of the param", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}, {"name": "MESSAGE", "description": "Message from User", "isGid": false, "length": "100", "mandatory": false, "sqlType": "TEXT"}]}}, "TOPIC": {"description": "Topic", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "TOPIC_HEADER": {"className": "com.pd.forms.be.TOPIC_HEADER", "header": true, "field": [{"name": "TOPIC_ID", "description": "", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "TOPIC_NAME", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}, {"name": "IS_ACTIVE", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHGD_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "CHGD_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "PBLSH_BY", "isGid": false, "length": "50", "mandatory": false, "sqlType": "TEXT"}, {"name": "PBLSH_ON", "isGid": false, "length": "19", "mandatory": false, "sqlType": "INTEGER"}, {"name": "P_MODE", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "Index": []}