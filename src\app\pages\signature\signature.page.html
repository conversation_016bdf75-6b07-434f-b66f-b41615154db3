<ion-header>
  <ion-toolbar color="primary" >
    <ion-title>{{ 'Signature' | translate }}</ion-title>
    <ion-buttons slot="start">
      <ion-button class="button-header" fill="solid" color="primary" (click)="dismiss()">{{ 'Close' | translate }}</ion-button>
    </ion-buttons>
    <ion-buttons slot="end">
      <ion-button class="button-header" fill="solid" color="primary" (click)="clearSign()">{{ 'Clear' | translate }}</ion-button>
      <ion-button class="button-header" fill="solid" color="primary" (click)="saveSign()" >{{ 'Save' | translate }}</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [ngClass]="{'contrast': styleTheme === 'contrast'}" class="ion-padding">
  <ion-list>
    <ion-item class="crew-info">
      <div class="crew-text">
        <h2 class="crew-name">{{ crewDet.CREW_NAME }}</h2>
        <p class="crew-pos" *ngIf="crewDet.CREW_POS">{{ crewDet.CREW_POS }}</p>
      </div>
    </ion-item>
  </ion-list>

  <div class="sign-container">
    <canvas #canvas></canvas>
  </div>

  <ion-list lines="none">
    <ion-item *ngIf="isSignDrawn">
      <p>Note: Please clear and redraw the signature to overwrite it.</p>
    </ion-item>
  </ion-list>
</ion-content>
