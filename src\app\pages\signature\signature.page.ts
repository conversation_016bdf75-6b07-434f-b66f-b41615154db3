import { <PERSON><PERSON>nent, ViewChild, ElementRef, NgZone, AfterViewInit, OnDestroy } from '@angular/core';
import { ModalController, NavParams, Platform } from '@ionic/angular/standalone';
import SignaturePad from 'signature_pad';
import { IonButtons, IonButton, IonContent, IonHeader, IonTitle, IonToolbar, IonList, IonItem } from '@ionic/angular/standalone';
import { AppConstants } from '../../constants/appConstants';
import { UtilityService } from '../../services/utility.service';
import { TranslateService, TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';

declare var cordova: any;

@Component({
  selector: 'app-signature',
  templateUrl: './signature.page.html',
  styleUrls: ['./signature.page.scss'],
  imports: [IonButtons, IonButton, IonContent, IonHeader, IonTitle, IonToolbar, IonList, IonItem, TranslateModule, CommonModule],
  standalone: true,
})

export class SignaturePage implements AfterViewInit, OnDestroy {

  @ViewChild('canvas', { static: true }) canvasRef!: ElementRef<HTMLCanvasElement>;
  signaturePad!: SignaturePad;

  signatureType: string = '';
  crewDet: any = {};
  isSignDrawn: boolean = false;
  styleTheme: string = 'normal';
  private resizeListener?: () => void;

  signaturePadOptions = {
    minWidth: 2,
    penColor: 'rgb(0, 81, 255)',
  };

  constructor(
    private modalCtrl: ModalController,
    private navParams: NavParams,
    private ngZone: NgZone,
    private utilityService: UtilityService,
    private translate: TranslateService,
    private platform: Platform
  ) {
    try {
      console.log('SignaturePage - Constructor started');
      console.log('SignaturePage - NavParams:', this.navParams);

      this.signatureType = this.navParams.get('signatureType');
      this.crewDet = this.navParams.get('crew') || {};
      this.styleTheme = this.navParams.get('theme') || 'normal';

      console.log('SignaturePage - Constructor data:');
      console.log('  - signatureType:', this.signatureType);
      console.log('  - crewDet:', this.crewDet);
      console.log('  - styleTheme:', this.styleTheme);

      // Add global error handler for this component
      window.addEventListener('error', (event) => {
        console.error('SignaturePage - Global Error:', event.error);
        console.error('SignaturePage - Error Stack:', event.error?.stack);
      });

      window.addEventListener('unhandledrejection', (event) => {
        console.error('SignaturePage - Unhandled Promise Rejection:', event.reason);
      });

    } catch (error) {
      console.error('SignaturePage - Constructor Error:', error);
    }
  }

  ngAfterViewInit() {
    try {
      console.log('SignaturePage - ngAfterViewInit - crewDet:', this.crewDet);
      console.log('SignaturePage - Canvas element:', this.canvasRef?.nativeElement);

      if (!this.canvasRef?.nativeElement) {
        console.error('SignaturePage - Canvas element not found!');
        return;
      }

      // Initialize canvas and signature pad
      this.initializeSignaturePad();

      // Setup resize listener for Electron and window resize events
      this.setupResizeListener();

    } catch (error) {
      console.error('SignaturePage - Error in ngAfterViewInit:', error);
    }
  }

  ngOnDestroy() {
    // Clean up resize listener
    if (this.resizeListener) {
      window.removeEventListener('resize', this.resizeListener);
    }
  }

  private initializeSignaturePad() {
    // First resize the canvas to proper dimensions
    this.resizeCanvas();

    // Wait a bit for layout to stabilize, especially important in Electron
    setTimeout(() => {
      this.resizeCanvas();
      this.createSignaturePad();
      
      // Additional resize for Electron to ensure proper sizing
      if (this.isElectronEnvironment()) {
        setTimeout(() => {
          this.resizeCanvas();
          // After final resize, load the signature data
          this.loadSignatureData();
        }, 200);
      } else {
        // For non-Electron, load signature data immediately after creation
        setTimeout(() => {
          this.loadSignatureData();
        }, 50);
      }
    }, 100);
  }

  private createSignaturePad() {
    console.log('SignaturePage - Creating SignaturePad with options:', this.signaturePadOptions);
    this.signaturePad = new SignaturePad(this.canvasRef.nativeElement, this.signaturePadOptions);
    console.log('SignaturePage - SignaturePad initialized successfully');
    console.log('SignaturePage - SignaturePad instance:', this.signaturePad);

    // Add event listeners to track signature drawing
    this.signaturePad.addEventListener('beginStroke', () => {
      console.log('SignaturePage - User started drawing');
    });

    this.signaturePad.addEventListener('endStroke', () => {
      console.log('SignaturePage - User finished drawing stroke');
      this.isSignDrawn = !this.signaturePad.isEmpty();
    });
  }

  private async loadSignatureData() {
    if (!this.signaturePad) {
      console.error('SignaturePage - Cannot load signature data: SignaturePad not initialized');
      return;
    }

    this.ngZone.run(async () => {
      try {
        console.log('SignaturePage - Loading signature data...');
        console.log('SignaturePage - crewDet:', this.crewDet);
        console.log('SignaturePage - crewDet.CREW_SIGN exists:', !!this.crewDet?.CREW_SIGN);
        
        if (this.crewDet && this.crewDet.CREW_SIGN && this.crewDet.CREW_SIGN.trim().length > 0) {
          console.log('SignaturePage - Loading existing signature');
          console.log('SignaturePage - Signature data length:', this.crewDet.CREW_SIGN.length);
          console.log('SignaturePage - Signature preview:', this.crewDet.CREW_SIGN.substring(0, 100) + '...');
          
          this.isSignDrawn = true;
          
          try {
            this.signaturePad.fromDataURL(this.crewDet.CREW_SIGN);
            this.signaturePad.off(); // Disable editing for existing signature
            console.log('SignaturePage - Existing signature loaded successfully');
          } catch (signatureError) {
            console.error('SignaturePage - Error loading signature from dataURL:', signatureError);
            console.error('SignaturePage - Invalid signature data:', this.crewDet.CREW_SIGN);
            // Fall back to empty signature
            this.isSignDrawn = false;
            this.signaturePad.on();
            this.signaturePad.clear();
          }
        } else {
          const isTestMode = await this.utilityService.isAppRunningInTestAutomationMode();
          if (isTestMode) {
            console.log('SignaturePage - Loading test signature');
            this.isSignDrawn = false;
            try {
              this.signaturePad.fromDataURL(AppConstants.SETTING_TEST_SIGNATURE);
              this.signaturePad.off();
            } catch (testSignatureError) {
              console.error('SignaturePage - Error loading test signature:', testSignatureError);
              this.signaturePad.on();
              this.signaturePad.clear();
            }
          } else {
            console.log('SignaturePage - Initializing empty signature pad');
            this.isSignDrawn = false;
            this.signaturePad.on();
            this.signaturePad.clear();
          }
        }
      } catch (error) {
        console.error('SignaturePage - Error in loadSignatureData:', error);
        // Ensure signature pad is in a usable state
        try {
          this.isSignDrawn = false;
          this.signaturePad.on();
          this.signaturePad.clear();
        } catch (fallbackError) {
          console.error('SignaturePage - Error in fallback signature initialization:', fallbackError);
        }
      }
    });
  }

  private setupResizeListener() {
    this.resizeListener = () => {
      // Debounce resize events
      clearTimeout((this as any).resizeTimeout);
      (this as any).resizeTimeout = setTimeout(() => {
        const wasSignatureDrawn = this.isSignDrawn;
        const existingSignatureData = this.crewDet?.CREW_SIGN;
        
        this.resizeCanvas();
        
        // If we had a signature and it was cleared during resize, reload it
        if (wasSignatureDrawn && existingSignatureData && this.signaturePad && this.signaturePad.isEmpty()) {
          console.log('SignaturePage - Reloading signature after resize');
          setTimeout(() => {
            this.loadSignatureData();
          }, 50);
        }
      }, 150);
    };

    window.addEventListener('resize', this.resizeListener);
  }

  private isElectronEnvironment(): boolean {
    return cordova.platformId == 'electron';
  }

  dismiss() {
    this.modalCtrl.dismiss({});
  }

  saveSign() {
    try {
      console.log('SignaturePage - Saving signature');
      const signImg = this.signaturePad.isEmpty() ? '' : this.signaturePad.toDataURL();
      console.log('SignaturePage - Signature data length:', signImg.length);

      this.modalCtrl.dismiss({
        CREW_SIGN: signImg,
        signatureType: this.signatureType,
        crew: this.crewDet
      });
    } catch (error) {
      console.error('SignaturePage - Error saving signature:', error);
    }
  }

  clearSign() {
    try {
      this.ngZone.run(() => {
        console.log('SignaturePage - Clearing signature');
        this.isSignDrawn = false;
        this.signaturePad.on();
        this.signaturePad.clear();
        
        // Ensure canvas is properly sized after clearing, especially for Electron
        if (this.isElectronEnvironment()) {
          setTimeout(() => {
            this.resizeCanvas();
          }, 50);
        }
        
        console.log("SignaturePage - Signature cleared. Is empty?", this.signaturePad.isEmpty());
      });
    } catch (error) {
      console.error('SignaturePage - Error clearing signature:', error);
    }
  }

  // Helper method to check if signature pad is empty (for template binding)
  isSignaturePadEmpty(): boolean {
    return this.signaturePad ? this.signaturePad.isEmpty() : true;
  }

  // Debug method to check component state
  debugSignaturePage() {
    console.log('=== SIGNATURE PAGE DEBUG INFO ===');
    console.log('signatureType:', this.signatureType);
    console.log('crewDet:', this.crewDet);
    console.log('crewDet.CREW_SIGN exists:', !!this.crewDet?.CREW_SIGN);
    console.log('crewDet.CREW_SIGN length:', this.crewDet?.CREW_SIGN?.length || 0);
    console.log('crewDet.CREW_SIGN preview:', this.crewDet?.CREW_SIGN?.substring(0, 100) || 'N/A');
    console.log('styleTheme:', this.styleTheme);
    console.log('isSignDrawn:', this.isSignDrawn);
    console.log('platform is Electron:', this.isElectronEnvironment());
    console.log('canvasRef:', this.canvasRef);
    console.log('canvasRef.nativeElement:', this.canvasRef?.nativeElement);
    console.log('signaturePad:', this.signaturePad);
    console.log('signaturePadOptions:', this.signaturePadOptions);

    if (this.canvasRef?.nativeElement) {
      const canvas = this.canvasRef.nativeElement;
      console.log('Canvas width:', canvas.width);
      console.log('Canvas height:', canvas.height);
      console.log('Canvas clientWidth:', canvas.clientWidth);
      console.log('Canvas clientHeight:', canvas.clientHeight);
      console.log('Canvas style.width:', canvas.style.width);
      console.log('Canvas style.height:', canvas.style.height);
    }

    if (this.signaturePad) {
      console.log('SignaturePad isEmpty:', this.signaturePad.isEmpty());
      try {
        const dataURL = this.signaturePad.toDataURL();
        console.log('SignaturePad toDataURL length:', dataURL.length);
        console.log('SignaturePad toDataURL preview:', dataURL.substring(0, 100));
      } catch (e) {
        console.log('SignaturePad toDataURL error:', e);
      }
    }
    console.log('=== END DEBUG INFO ===');
  }

  private resizeCanvas() {
    const canvas = this.canvasRef.nativeElement;
    if (!canvas || !canvas.parentElement) {
      console.warn('SignaturePage - Canvas or parent element not available for resizing');
      return;
    }

    const ratio = Math.max(window.devicePixelRatio || 1, 1);
    const parentElement = canvas.parentElement;
    
    // Get available width, accounting for platform differences
    let availableWidth: number;
    let availableHeight: number;

    if (this.isElectronEnvironment()) {
      // For Electron, we need to be more careful about getting the real available space
      // Wait for the parent to have actual dimensions
      availableWidth = parentElement.offsetWidth || parentElement.clientWidth || window.innerWidth - 80;
      availableHeight = Math.min(window.innerHeight * 0.6, 400); // Max 60% of window height or 400px
      
      // Ensure minimum dimensions for Electron
      availableWidth = Math.max(availableWidth, 300);
      
      console.log('SignaturePage - Electron environment detected');
      console.log('SignaturePage - Parent offsetWidth:', parentElement.offsetWidth);
      console.log('SignaturePage - Parent clientWidth:', parentElement.clientWidth);
      console.log('SignaturePage - Available width:', availableWidth);
    } else {
      // For mobile devices, use the existing logic
      availableWidth = parentElement.clientWidth || window.innerWidth - 40;
      availableHeight = window.innerHeight * 0.4; // 40% of screen height on mobile
    }

    // Maintain aspect ratio (original 575x420 ≈ 1.37)
    const aspectRatio = 575 / 420;
    
    // Calculate dimensions respecting both width and height constraints
    const widthBasedHeight = availableWidth / aspectRatio;
    const heightBasedWidth = availableHeight * aspectRatio;
    
    let desiredWidth: number;
    let desiredHeight: number;
    
    if (widthBasedHeight <= availableHeight) {
      // Width is the limiting factor
      desiredWidth = availableWidth;
      desiredHeight = widthBasedHeight;
    } else {
      // Height is the limiting factor
      desiredWidth = heightBasedWidth;
      desiredHeight = availableHeight;
    }

    // Ensure minimum dimensions
    desiredWidth = Math.max(desiredWidth, 250);
    desiredHeight = Math.max(desiredHeight, desiredWidth / aspectRatio);

    console.log('SignaturePage - Resizing canvas:');
    console.log('  - Desired width:', desiredWidth);
    console.log('  - Desired height:', desiredHeight);
    console.log('  - Device pixel ratio:', ratio);
    console.log('  - Platform:', this.isElectronEnvironment() ? 'Electron' : 'Mobile');

    // Set canvas dimensions
    canvas.width = desiredWidth * ratio;
    canvas.height = desiredHeight * ratio;
    canvas.style.width = desiredWidth + 'px';
    canvas.style.height = desiredHeight + 'px';

    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.scale(ratio, ratio);
    }

    // Force a repaint for Electron
    if (this.isElectronEnvironment()) {
      canvas.style.display = 'none';
      canvas.offsetHeight; // Trigger reflow
      canvas.style.display = 'block';
    }
  }

}
