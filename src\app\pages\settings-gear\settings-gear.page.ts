import { Component, OnInit, NgZone } from '@angular/core';
import { LoadingController, ToastController, AlertController, } from '@ionic/angular';
import { LogLevel, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { Router } from '@angular/router';
import { faInfoCircle, faBolt, faSyncAlt, faSpinner, faTrashAlt,faGear, faEnvelope, faCloudUploadAlt, faBell, faCloudDownloadAlt, faR, faFileLines, faCircleInfo } from '@fortawesome/free-solid-svg-icons';
import { AppConstants } from 'src/app/constants/appConstants';
import { HttpClient } from '@angular/common/http';
import { DataService } from 'src/app/services/data.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { FontAwesomeModule, FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ModalController } from '@ionic/angular/standalone';
import { ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-settings-gear',
  templateUrl: './settings-gear.page.html',
  styleUrls: ['./settings-gear.page.scss'],
  imports: [IonicModule, CommonModule, FormsModule, FontAwesomeModule],
  standalone:true,
  providers:[ ModalController]
})
export class SettingsGearPage implements OnInit {
  public userData: any;
  public processbar = true;
  public connection = true;
  public userDetails = { username: 'Not Found', url: 'Not Found', userid: 'Not Found' };
  public syncState = false;
  public syncStatus = '';
  logLevel = [
    { value: 8, viewValue: 'Error' },
    { value: 7, viewValue: 'Important' },
    { value: 9, viewValue: 'Debug' }
  ];
  loglevel: any;
  public showLoading: HTMLIonLoadingElement | null = null;
  public version: any;
  public release: any;
  public infoErrorCount : number=0;
  public infoOtherCount : number=0;
  public infoConnection = true;
  public dataSenderFlag = false;
  public getMessageFlag = false;
  public loadingGifFlag = false;
  public offline = true;
  public server = false;
  constructor(
    private unviredSDK: UnviredCordovaSDK,
    private loadingController: LoadingController,
    private ngZone: NgZone,
    private router: Router,
    private httpClient: HttpClient,
    public dataService: DataService,
    private toastController: ToastController,
    public device: Device,
    private alertController: AlertController,
    private faIconLibrary:FaIconLibrary,
    private modalCtrl: ModalController,
    private cdr: ChangeDetectorRef,
  ) {
    this.faIconLibrary.addIcons(faInfoCircle, faBolt,faR, faGear,faSyncAlt, faSpinner, faTrashAlt, faEnvelope, faCloudUploadAlt, faBell, faCloudDownloadAlt, faFileLines, faCircleInfo);
    this.loadUserDetails();
    this.getLogLevel();
    this.getInfoMessages();
   
    // this.version = AppConstants.VERSION_NO;
    // this.release = AppConstants.BUILD_DATE;
    this.sync();
    if (this.dataService.getNetworkStatus() == true) {
      this.offline = false
    } else {
      this.offline = true
    }
  }
  async loadUserDetails() {
    await this.presentLoading('Loading...');
    await this.getUserData();
    await this.showLoading?.dismiss();
  }
  async presentLoading(msg:string) {
    this.showLoading = await this.loadingController.create({
      message: msg,
      spinner: 'crescent',
      animated: true,
      showBackdrop: true,
      translucent: true
    });
    await this.showLoading.present();
  }
  async getUserData() {
    this.processbar = true;
    try{
    const data=await this.unviredSDK.userSettings()
      console.log('getUserData: success', data);
        this.ngZone.run(() => {
          this.connection = true;
          this.userDetails = { username: data.data.FULL_NAME, url: data.data.SERVER_URL, userid: data.data.USER_ID };
        
      this.processbar = false;
      this.cdr.detectChanges(); 
    });
  }catch(err) {
      console.error('getUserData: failed', err);
        this.ngZone.run(() => {
          this.connection = false;
          this.userDetails = { username: 'Not Found', url: 'Not Found', userid: 'Not Found' };
       this.processbar = false;
       this.cdr.detectChanges(); 
        });
    }
  }
  async getConnectionStatus() {
    if (this.dataService.getNetworkStatus() == true) {
      this.processbar = true;
      this.offline = false

      try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 seconds timeout
      const response = await fetch(this.userDetails.url + '/API/v2/ping', {
          headers: {
              "Accept": "application/json"
          },
          signal: controller.signal
      });
      clearTimeout(timeoutId);
       this.ngZone.run(() => {
      if (response.status === 200) {
          
          console.log('server reachable');
          this.server = true;
      }
      else {
          console.log('server not reachable');
          this.server = false;
      }
    });
  } catch (error) {
    console.log('server not reachable');
     this.ngZone.run(() => {
      this.server = true;
    });
  }
  finally {
    // ✅ Always hide the progress bar
    this.ngZone.run(() => {
      this.processbar = false;
      console.log('processbar hidden after ping');
    });
  }
    } else {
      this.offline = true
    }
  }
  connect() {
    this.ngZone.run(() => {
      this.getConnectionStatus();
    });
  }
  sync() {
    this.ngZone.run(() => {
      this.getMessageFlag = false;
      this.dataSenderFlag = false;
      this.loadingGifFlag = true;
      this.unviredSDK.getSynchronizationState().subscribe(result => {
        this.syncStatus = result;
        if (this.syncStatus === 'idle') {
          this.unviredSDK.outBoxItemCount().then((count) => {
            const outCout = parseInt(count, 10);
            if (outCout === 0) {
              this.loadingGifFlag = false;
              this.getMessageFlag = true;
            } else {
              this.loadingGifFlag = false;
              this.dataSenderFlag = true;
            }
          }).catch((error) => {
            alert(error);
            this.getMessageFlag = false;
            this.dataSenderFlag = false;
            this.loadingGifFlag = false;
          });
        } else {
          this.getMessageFlag = false;
          this.dataSenderFlag = false;
          this.loadingGifFlag = false;
        }
      });
    });
  }
  async info() {
    this.router.navigate(['infomessages']);
  }
  logs(event:Event) {
    event.preventDefault();
    event.stopPropagation();
    this.router.navigate(['logs']);
  }
  trash() {
    this.trashConfirm();
  }
  email(event:Event) {
    event.preventDefault();
    event.stopPropagation();
    this.unviredSDK.sendLogViaEmail().then(() => {
    }).catch((error) => {
    });
  }
  upload(event:Event) {
    event.preventDefault();
    event.stopPropagation();
    this.clearDataToast('Please wait while sending logs to server.');
    this.unviredSDK.sendLogToServer().then(async (result) => {
      console.log('upload result: ', result);
    }).catch(() => {
    });
  }

  ngOnInit() {
    setInterval(() => {
      this.ngZone.run(() => {
        this.unviredSDK.getSynchronizationState().subscribe(result => {
          this.syncStatus = result;
          if (this.syncStatus === 'idle') {
            this.unviredSDK.outBoxItemCount().then((count) => {
              const outCout = parseInt(count, 10);
              if (outCout === 0) {
                this.getMessageFlag = true;
              } else {
                this.dataSenderFlag = true;
              }
            }).catch((error) => {
              this.getMessageFlag = false;
              this.dataSenderFlag = false;
              this.loadingGifFlag = false;
            });
          } else {
            this.getMessageFlag = false;
            this.dataSenderFlag = false;
            this.loadingGifFlag = false;
          }
        });
      });
    }, 20 * 1000);
  }
  async clearDataToast(msg:string) {
    const toast = await this.toastController.create({
      message: msg,
      mode: 'ios',
      color: 'dark',
      duration: 2000
    });
    toast.present();
  }
  async trashConfirm() {
    const alert = await this.alertController.create({
      header: 'Confirmation!',
      mode: 'ios',
      message: 'Are you sure you want to reset the app?',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {
            console.log('Confirm Cancel: blah');
          }
        }, {
          text: 'Okay',
          handler: async () => {
            await this.presentLoading('Please wait...');
            try {
              const result = await this.unviredSDK.clearData();
              await this.showLoading?.dismiss();
              this.closeAppAlert(result.data);
            } catch (error) {
              await this.showLoading?.dismiss();
              this.clearDataToast((error as any).message);
            }
          }
        }
      ]
    });

    await alert.present();
  }
  async closeAppAlert(msg: any) {
    const alert = await this.alertController.create({
      header: 'Alert!',
      message: msg,
      mode: 'ios',
      backdropDismiss: false
    });

    await alert.present();
  }
 async getLogLevel() {
  try {
    const result:any = await this.unviredSDK.getLogLevel(); 
    console.log("SDK raw log level:", result);

    // Convert to number if needed
    const numericValue = parseInt(result, 10);

    this.loglevel = numericValue;
    console.log("Mapped log level (this.loglevel):", this.loglevel);
  } catch (error) {
    console.error("getLogLevel failed:", error);
  }
}

  async setLogLevel(event: any) {
  var levell = Number(event.target.value);
  try {
    await this.unviredSDK.setLogLevel(levell).then((result) => {
      this.loglevel = levell;
    });
    console.log("Log level set to:", this.loglevel);
  } catch (error) {
    console.error("setLogLevel failed:", error);
  }
}

  async getInfoMessages() {
    await this.unviredSDK.getInfoMessages('', '').then((result) => {
      if (result.data.length === 0) {
        this.infoErrorCount = 0;
        this.infoOtherCount = 0;
        this.infoConnection = true;
      } else {
        if (result.data.length > 0) {
          for (var i1 = 0; i1 < result.data.length; i1++) {
            if (result.data.CATEGORY === 'ERROR') {
              this.infoConnection = false;
              this.infoErrorCount++;
            } else {
              this.infoConnection = true;
              this.infoOtherCount++;
            }
          }
        }
      }
    }).catch((error) => {
      this.infoConnection = false;
    });
  }
  dataSender() {
    this.ngZone.run(() => {
      this.getMessageFlag = false;
      this.dataSenderFlag = false;
      this.loadingGifFlag = true;
      this.unviredSDK.startDataSender().then((data) => {
        this.loadingGifFlag = false;
        this.dataSenderFlag = true;
      }).catch((error) => {
        this.loadingGifFlag = false;
        this.dataSenderFlag = true;
        alert(error);
      });
    });
  }
  getMessage() {
    this.ngZone.run(() => {
      this.getMessageFlag = false;
      this.dataSenderFlag = false;
      this.loadingGifFlag = true;
      this.unviredSDK.getMessages();
      setTimeout(() => {
        this.ngZone.run(() => {
          this.loadingGifFlag = false;
          this.getMessageFlag = true;
        });
      }, 3000);
    });
  }
  pushNotification() {
    this.unviredSDK.testPushNotification().then((result) => {
      console.log('pushNotification: success', result);
    }).catch((error) => {
      console.error('pushNotification: failed', error);
    });
  }
  close() {
    this.modalCtrl.dismiss(); // 👈 closes the modal
  }
}
