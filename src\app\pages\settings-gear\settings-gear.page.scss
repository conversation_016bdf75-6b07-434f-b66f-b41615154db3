.connectionsuccess {
    color: #008000
}

.connectionerror {
    color: rgba(167, 24, 24, 0.863)
}

.main-header {
    --color: #ffffff;
    --background: #303F9F;
}

ion-select {
    &::part(container) {
        width: 100% !important;
    }
    .select-wrapper > .select-wrapper-inner {
        width: 100% !important;
    }
}

ion-select::part(container) {
    width: 100%;
    .select-wrapper-inner {
        width: 100% !important;
    }
  }

.select-wrapper > .select-wrapper-inner {
    width: 100% !important;
}
.logs-item {
  .log-level-row {
    display: flex;
    align-items: center;
    justify-content: center; // centers the whole line
    gap: 10px;
    margin-top: 4px;
  }

  .log-level-label {
    font-size: 13px;
    color: black;
  }

  .log-select {
    border: 1px solid gray;
    border-radius: 6px;
    padding: 3px 10px;
    width:90px;
    height:24px;
    outline-color: rgba(0, 0, 0, 0)
  }
}
ion-select::part(icon) {
  position: absolute;
  right: 8px;   // 👈 push caret to the right end
  left: auto;   // 👈 prevent it sticking to the left
}



