import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';

@Injectable({ providedIn: 'root' })
export class AttachmentHelper {
  constructor(private platform: Platform, private unviredSDK: UnviredCordovaSDK) { }

  isElectron(): boolean {
    try {
      const userAgent = navigator.userAgent || '';
      const hasElectronProcess = !!(window as any).process?.versions?.electron;
      const hasElectronInUserAgent = userAgent.toLowerCase().includes('electron');
      const result = hasElectronProcess || hasElectronInUserAgent;

      try {
        this.unviredSDK.logInfo("AttachmentHelper", "isElectron", `Electron detection result: ${result}`);
      } catch { }

      return result;
    } catch (error) {
      try {
        this.unviredSDK.logError("AttachmentHelper", "isElectron", `Error detecting Electron: ${error}`);
      } catch { }
      return false;
    }
  }

  getBasePath(): string {
    if (this.isElectron()) {
      try {
        this.unviredSDK.logInfo("AttachmentHelper", "getBasePath", "Detected Electron platform");
      } catch { }
      return '';
    }

    // Cordova/Ionic Native File plugin
    if ((window as any).cordova?.file?.documentsDirectory) {
      try {
        this.unviredSDK.logInfo("AttachmentHelper", "getBasePath", "Detected Cordova platform");
      } catch { }

      const base = (window as any).cordova.file.documentsDirectory;
      try {
        this.unviredSDK.logInfo("AttachmentHelper", "getBasePath", `Mobile base path: ${base}`);
      } catch { }
      return base;
    }

    // Fallback (browser, etc)
    try {
      this.unviredSDK.logInfo("AttachmentHelper", "getBasePath", "Fallback to default path");
    } catch { }
    return '/tmp/RigDocs/';
  }

  // Normalizes a local path for the current platform
  normalizeLocalPath(localPath: string): string {
    // Input validation
    if (!localPath || typeof localPath !== 'string') {
      try {
        this.unviredSDK.logError("AttachmentHelper", "normalizeLocalPath", `Invalid input: ${localPath}`);
      } catch { }
      return '';
    }

    if (this.isElectron()) {
      // For Electron, return the path as-is, just normalize slashes
      const normalized = localPath.replace(/\\/g, '/');
      return normalized;
    } else {
      // For mobile platforms, strip drive letter and root folder
      const normalizedPath = localPath.replace(/\\/g, '/');

      // Remove drive letter if present (e.g., "C:/")
      let pathWithoutDrive = normalizedPath.replace(/^[a-zA-Z]:\/?/, '');

      // Remove leading slashes
      pathWithoutDrive = pathWithoutDrive.replace(/^\/+/, '');

      // Split by path separator and find the root folder (usually "RigDocs" or "Documents")
      const pathParts = pathWithoutDrive.split('/');
      const rootFolderIndex = pathParts.findIndex(part =>
        part.toLowerCase() === 'rigdocs' ||
        part.toLowerCase() === 'documents'
      );

      if (rootFolderIndex >= 0) {
        // Return everything after the root folder
        const result = pathParts.slice(rootFolderIndex).join('/');
        try {
          this.unviredSDK.logInfo("AttachmentHelper", "normalizeLocalPath", `Mobile path normalized: ${localPath} -> ${result}`);
        } catch { }
        return result;
      }

      // If no root folder found, return the path as-is
      try {
        this.unviredSDK.logInfo("AttachmentHelper", "normalizeLocalPath", `Mobile path (no root folder): ${localPath} -> ${pathWithoutDrive}`);
      } catch { }
      return pathWithoutDrive;
    }
  }

    /**
 * Normalize a path but preserve the file:// or file:/// protocol prefix.
 * - Keeps the file://( / / ) prefix intact.
 * - Collapses duplicate slashes only in the path portion.
 */
  public sanitizePathPreserveFileScheme(path: string): string {
    if (!path || typeof path !== 'string') return path;

    const fileSchemeMatch = path.match(/^(file:\/\/\/|file:\/\/)/i);
    let prefix = '';
    let rest = path;

    if (fileSchemeMatch) {
      prefix = fileSchemeMatch[0];
      rest = path.slice(prefix.length);
    }
    rest = rest.replace(/\/{2,}/g, '/');

    // Rebuild and return
    return prefix + rest;
  }

  /**
   * Ensures a directory exists. If not, creates it recursively.
   * If no path is specified, uses getBasePath().
   */
  async ensureDirectory(folderPath?: string): Promise<string> {
    await this.platform.ready();
    let path = folderPath && folderPath.trim() ? folderPath : this.getBasePath();
    await this.createDirectoriesRecursively(path);
    return path;
  }

  // Read file as ArrayBuffer
  async readExternalFile(filePath: string): Promise<ArrayBuffer> {
    await this.platform.ready();
    return new Promise((resolve, reject) => {
      (window as any).resolveLocalFileSystemURL(filePath,
        (fileEntry: any) => {
          // File exists, proceed to read
          fileEntry.file((file: any) => {
            const reader = new FileReader();
            reader.onloadend = function () {
              resolve(this.result as ArrayBuffer);
            };
            reader.onerror = (err: any) => {
              try {
                this.unviredSDK.logError("AttachmentHelper", "readExternalFile", `Read error: ${err}`);
              } catch { }
              reject(err);
            };
            reader.readAsArrayBuffer(file);
          }, (error: any) => {
            this.unviredSDK.logError("AttachmentHelper", "readExternalFile", `Error reading file: ${error}`);
            reject(error);
          });
        },
        (error: any) => {
          reject(error);
        }
      );
    });
  }

  // Write file (create/update)
  async writeExternalFile(fullFilePath: string, data: ArrayBuffer): Promise<void> {
    await this.platform.ready();
    return new Promise((resolve, reject) => {
      const folderPath = fullFilePath.substring(0, fullFilePath.lastIndexOf('/'));
      const fileName = fullFilePath.substring(fullFilePath.lastIndexOf('/') + 1);
      (window as any).resolveLocalFileSystemURL(folderPath, (dirEntry: any) => {
        dirEntry.getFile(fileName, { create: true }, (fileEntry: any) => {
          fileEntry.createWriter((fileWriter: any) => {
            fileWriter.onwriteend = () => {
              this.unviredSDK.logInfo("AttachmentHelper", "writeExternalFile", `Successfully wrote file: ${fullFilePath}`);
              resolve();
            };
            fileWriter.onerror = (e: any) => {
              this.unviredSDK.logError("AttachmentHelper", "writeExternalFile", `Error writing file: ${fullFilePath}, ${e}`);
              reject(e);
            };
            fileWriter.write(new Blob([data]));
          }, reject);
        }, reject);
      }, reject);
    });
  }

  async deleteExternalFile(filePath: string): Promise<void> {
    await this.platform.ready();
    return new Promise((resolve, reject) => {
      (window as any).resolveLocalFileSystemURL(filePath,
        (fileEntry: any) => fileEntry.remove(resolve, reject),
        (error: any) => {
          this.unviredSDK.logError("AttachmentHelper", "deleteExternalFile", `Error deleting file: ${filePath}, ${error}`);
          reject(error);
        }
      );
    });
  }

  /**
   * Creates directories recursively.
   * 
   * - For Electron: supports absolute path from drive root.
   * - For Cordova/iOS/Android: starts from basePath and creates nested dirs.
   */
  async createDirectoriesRecursively(dirPath: string): Promise<string> {
    await this.platform.ready();
    if (this.isElectron()) {
      return new Promise((resolve, reject) => {
        const pathParts = dirPath.replace(/\\/g, '/').split('/').filter(p => p.length > 0);
        let currentPath = '';
        if (pathParts.length > 0 && /^[a-zA-Z]:$/.test(pathParts[0])) {
          currentPath = pathParts[0] + '/';
          pathParts.shift();
        }
        const createNextDirectory = (index: number) => {
          if (index >= pathParts.length) return resolve(dirPath.endsWith('/') ? dirPath : dirPath + '/');
          const part = pathParts[index].replace(/[<>:"/\\|?*\x00-\x1F]/g, '').trim();
          currentPath += part + '/';
          (window as any).resolveLocalFileSystemURL(
            currentPath,
            () => createNextDirectory(index + 1),
            () => {
              const parent = currentPath.replace(/\/$/, '').substring(0, currentPath.replace(/\/$/, '').lastIndexOf('/')) + '/';
              (window as any).resolveLocalFileSystemURL(parent,
                (parentDir: any) => {
                  parentDir.getDirectory(part, { create: true },
                    () => createNextDirectory(index + 1),
                    (err2: any) => {
                      // Ignore "already exists" error (code 12)
                      const code2 = err2?.code;
                      if (code2 === 12 ||
                        code2 === null ||
                        code2 === undefined ||
                        (typeof code2 === 'object' && Object.keys(code2).length === 0)) {
                        this.unviredSDK.logInfo("AttachmentHelper", "createDirectoriesRecursively", `Directory already exists (parent fallback): ${currentPath}`);
                        return createNextDirectory(index + 1);
                      }
                      this.unviredSDK.logError("AttachmentHelper", "createDirectoriesRecursively", `Error creating directory: ${part}, ${JSON.stringify(err2)}`);
                      reject(err2);
                    }
                  );
                },
                (err2: any) => {
                  this.unviredSDK.logError("AttachmentHelper", "createDirectoriesRecursively", `Error resolving parent directory: ${parent}, ${JSON.stringify(err2)}`);
                  reject(err2);
                }
              );
            }
          );
        };
        createNextDirectory(0);
      });
    } else {
      return new Promise((resolve, reject) => {
        const basePath = this.getBasePath().replace(/\\/g, '/').replace(/\/+$/, '') + '/';
        const fullPath = dirPath.replace(/\\/g, '/').replace(/\/+$/, '');
        const baseParts = basePath.split('/').filter(p => p.length > 0);
        const pathParts = fullPath.split('/').filter(p => p.length > 0);
        const toCreate = pathParts.slice(baseParts.length);
        if (!toCreate.length) return resolve(basePath);
        (window as any).resolveLocalFileSystemURL(basePath,
          (rootDir: any) => {
            let currentDir = rootDir, idx = 0;
            const createNext = () => {
              if (idx >= toCreate.length)
                return resolve(basePath + toCreate.join('/') + '/');
              const safe = decodeURIComponent(
                toCreate[idx++].replace(/[<>:"/\\|?*\x00-\x1F]/g, '').trim()
              );
              currentDir.getDirectory(safe, { create: true },
                (newDir: any) => {
                  currentDir = newDir;
                  createNext();
                },
                (err: any) => {
                  this.unviredSDK.logError("AttachmentHelper", "createDirectoriesRecursively", `Error creating directory: ${safe}, ${err}`);
                  reject(err);
                }
              );
            };
            createNext();
          },
          (err: any) => {
            this.unviredSDK.logError("AttachmentHelper", "createDirectoriesRecursively", `Error resolving base directory: ${basePath}, ${err}`);
            reject(err);
          }
        );
      });
    }
  }
}
