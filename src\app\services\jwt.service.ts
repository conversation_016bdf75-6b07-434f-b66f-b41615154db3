import { Injectable ,Optional} from '@angular/core';
import { AlertController, Platform } from '@ionic/angular/standalone';
import { Router } from '@angular/router';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AppConstants } from '../constants/appConstants';
import { Deeplinks } from '@awesome-cordova-plugins/deeplinks/ngx';

declare var cordova: any;
declare var ump: any;

@Injectable({
  providedIn: 'root'
})
export class JwtService {
  private currentUrl = '';
  private devicetype = '';
  private reauthAlertOpen = false;

  constructor(
    private alertController: AlertController,
    private router: Router,
    private unviredSDK: UnviredCordovaSDK,
    private platform: Platform,
    private deeplinks: Deeplinks
  ) {
    this.setupDeeplinks();
    this.devicetype = cordova.platformId === 'electron' ? 'WINDOWS' : 
                     this.platform.is('ipad') ? 'IPAD' : 'IPHONE';
  }

  async checkAndPromptReauth(): Promise<void> {
    ump.getAuthTokenValidity((remainingValidityInSeconds: number) => {
      console.log('Token validity:', remainingValidityInSeconds);
      this.unviredSDK.logDebug('JwtService', 'checkAndPromptReauth', `Token validity: ${remainingValidityInSeconds} seconds`);
      if (remainingValidityInSeconds < 86400) { // 24 hours in seconds
        this.currentUrl = this.router.url;
        this.showReauthDialog();
      }
    }, (error: any) => {
      console.error('Token validity check failed:', error);
      this.unviredSDK.logError('JwtService', 'checkAndPromptReauth', `Token validity check failed: ${error}`);
    });
  }

  private async showReauthDialog(): Promise<void> {
    if (this.reauthAlertOpen) {
    return; // prevent duplicate dialogs
  }
  this.reauthAlertOpen = true;
    const alert = await this.alertController.create({
      header: 'Re-authentication Required',
      message: 'You are required re-authenticate since the application is not used for a long time.',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
           handler: () => {
          this.reauthAlertOpen = false;
        }
        },
        {
          text: 'Re-Authenticate',
          handler: () =>{ 
            this.reauthAlertOpen = false;
            this.initiateReauth()
          }
        }
      ],
      backdropDismiss: false
    });
    await alert.present();
     alert.onDidDismiss().then(() => {
    this.reauthAlertOpen = false;
  });
  }

  private async getBaseUrl(): Promise<string> {
  try {
    const data = await this.unviredSDK.userSettings();
    console.log('getUserData: success', data);
    
    // Extract SERVER_URL from the response data
    const serverUrl = data?.data?.SERVER_URL;
     console.log('SERVER_URL:', serverUrl);
    this.unviredSDK.logDebug('JwtService', 'getBaseUrl', `SERVER_URL: ${serverUrl}`);
    return serverUrl || 'https://ump.pd.com:8443/UMP';
   
    
  } catch (error) {
    console.error('Error getting user settings:', error);
    this.unviredSDK.logError('JwtService', 'getBaseUrl', `Error getting user settings: ${error}`);
    
    return 'https://ump.pd.com:8443/UMP';
  }
}


  private async initiateReauth(): Promise<void> {
    const baseUrl = await this.getBaseUrl();;
    const redirectUrl = 'pdforms://callback';
    const url = `${baseUrl}/sso/saml/dologin?company=PD&application=${AppConstants.APPLICATION_NAME}&device=${this.devicetype}&redirect=${encodeURIComponent(redirectUrl)}`;
    
    if (cordova.platformId === 'electron') {
      (window as any).electronAPI?.openExternal(url);
    } else {
      window.location.href = url;
    }
  }

  private setupDeeplinks(): void {
    if (cordova.platformId === 'electron') {
      (window as any).electronAPI?.onDeepLink(async (url: string) => {
        const token = new URL(url).searchParams.get('token');
        if (token && this.currentUrl) {
         ump.setAuthToken(token, (result: any) => {
          this.unviredSDK.logDebug('JwtService', 'setupDeeplinks', `Token updated successfully via deeplink ${result}`);
          console.log('Token updated successfully:', result);
          this.router.navigate([this.currentUrl]);
          this.currentUrl = '';
        }, (error: any) => {
          this.unviredSDK.logError('JwtService', 'setupDeeplinks', `Failed to update token via deeplink ${error}`);
          console.error('Failed to update token:', error);
        });
      }
    });
    } else {
      this.deeplinks.route({
        '/callback': 'callback'
      }).subscribe((match: any) => {
        if (match.$args?.token && this.currentUrl) {
         ump.setAuthToken(match.$args.token, (result: any) => {
          this.unviredSDK.logDebug('JwtService', 'setupDeeplinks', `Token updated successfully via deeplink ${result}`);
          console.log('Token updated successfully:', result);
          this.router.navigate([this.currentUrl]);
          this.currentUrl = '';
        }, (error: any) => {
          this.unviredSDK.logError('JwtService', 'setupDeeplinks', `Failed to update token via deeplink ${error}`);
          console.error('Failed to update token:', error);
        });
      }
    });
  }
}
  // In jwt.service.ts - add this method
setupForegroundCheck(): void {
  this.platform.ready().then(async () => {

    await this.checkAndPromptReauth();

    document.addEventListener('resume', async () => {
      await this.checkAndPromptReauth();
    });
    
    document.addEventListener('visibilitychange', async () => {
      if (!document.hidden) {
        await this.checkAndPromptReauth();
      }
    });
  });
}

}
