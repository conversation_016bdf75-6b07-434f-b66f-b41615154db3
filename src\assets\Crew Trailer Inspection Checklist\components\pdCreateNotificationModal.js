angular.module('PDTest').component('pdCreateNotificationComponent', {

  templateUrl: 'components/pdCreateNotificationModal.html',
  bindings: {
    model: '<'
  },

  controller: function ($scope) {
    var ctrl = this;
    console.log('Create Notification Model', JSON.stringify(ctrl.model, null, 2));

    $scope.filterCodeGroupsByNotificationType = function (codeGroup) {

      // Retreive by matching notification type's CATALOG_TYPE
      let catalogTypeForSelectedNotificationType = $scope.getCatalogTypeForNotificationType(ctrl.model.addNotificationItem.NotificationType)
      if (ctrl.model.addNotificationItem && codeGroup.CATALOG_TYPE == catalogTypeForSelectedNotificationType) {
        return true
      }
      return false
    }

    $scope.filterCodesBySelectedCodeGroup = function (code) {

      // Retreive by matching code group's CATALOG_TYPE
      let catalogTypeForSelectedCodeGroup = $scope.getCatalogTypeForCodeGroup(ctrl.model.addNotificationItem.CodeGroup)
      
      if (ctrl.model.addNotificationItem && code.CATALOG_TYPE == catalogTypeForSelectedCodeGroup) {
        return true
      }
      return false
    }

    $scope.filterPriorityBySelectedSelectedNotifType = function (priority) {

      // Retreive by matching notification type's PRIORITY_TYPE
      let priorityTypeForSelectedNotifType = $scope.getPriorityTypeForNotifType(ctrl.model.addNotificationItem.NotificationType)
      
      if (ctrl.model.addNotificationItem && priority.PRIORITY_TYPE == priorityTypeForSelectedNotifType) {
        return true
      }
      return false
    }

    $scope.handleAdvancedOptionClick = function () {
      ctrl.model.hideAdvancedOptions = !ctrl.model.hideAdvancedOptions
    }

    $scope.updateRequiredEndDate = function (event) {
      let increment = 0

      switch (parseInt(ctrl.model.addNotificationItem.Priority)) {
        case 1: increment = 30 // Emergency
          break

        case 2: increment = 60 // High
          break

        case 3: increment = 90 // Low
          break
      }
      ctrl.model.addNotificationItem.RequiredEndDate = $scope.incrementDate(ctrl.model.addNotificationItem.RequiredStartDate, increment)
    }

    $scope.incrementDate = function (dateInput, increment) {
      var dateFormatTotime = new Date(dateInput);
      var increasedDate = new Date(dateFormatTotime.getTime() + (increment * 86400000));
      return increasedDate;
    }

    $scope.saveNotification = function () {
      $scope.$emit('notificationUpdated', ctrl.model)
    }

    $scope.notificationTypeUpdated = function() {
      if (ctrl.model.addNotificationItem.NotificationType == 'S0') {
        ctrl.model.addNotificationItem.SecondaryWorkCenter = undefined
      }
    }

    $scope.getCatalogTypeForNotificationType = function (notifType) {
      if (!ctrl.model.lists.NOTIFICATION_TYPE) {
        return ''
      }

      const matchingNotifs = ctrl.model.lists.NOTIFICATION_TYPE.filter(function (element) {
        if (element.NOTIF_TYPE == notifType) {
          return true
        }
      })

      if (matchingNotifs.length > 0) {
        return matchingNotifs[0].CAT_TYPE_CODING
      }
      return ''
    }

    $scope.getCatalogTypeForCodeGroup = function (codeGroup) {

      if (!ctrl.model.lists.NOTIFICATION_CODE_GROUP) {
        return ''
      }

      const matchingCodeGroups = ctrl.model.lists.NOTIFICATION_CODE_GROUP.filter(function (element) {
        if (element.CODE_GROUP == codeGroup) {
          return true
        }
      })

      if (matchingCodeGroups.length > 0) {
        return matchingCodeGroups[0].CATALOG_TYPE
      }
      return ''
    }

    $scope.getPriorityTypeForNotifType = function (notifType) {

      if (!ctrl.model.lists.NOTIFICATION_TYPE) {
        return ''
      }

      const matchingNotifs = ctrl.model.lists.NOTIFICATION_TYPE.filter(function (element) {
        if (element.NOTIF_TYPE == notifType) {
          return true
        }
      })

      if (matchingNotifs.length > 0) {
        return matchingNotifs[0].PRIORITY_TYPE
      }
      return ''
    }
  }
});