import { Injectable, NgZone } from '@angular/core';
import { NotificationListenerType, NotifResult, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { addNotification, loadProgressBar  } from './store/store.actions';
import { Store } from '@ngrx/store';
import { DataService } from './services/data.service';
import { selectProgressPercentage } from './store/store.selector';
import { filter, Observable, take } from 'rxjs';
import  * as RigActions from './store/store.actions';
import { ReleaseNotesService } from './services/release-notes.service';
import { Platform } from '@ionic/angular';
declare var ump: any;
@Injectable({
  providedIn: 'root'
})
export class NotificationService {

  jsonData: any;
  progress$!: Observable<any>;
  dataDownloadPercentage : number = 0; 

  
  constructor(private unviredSdk: UnviredCordovaSDK , private ngZone: NgZone , private store : Store , private dataService: DataService,
    private releaseNotesService: ReleaseNotesService,private platform:Platform
  ) { }


  init() {
    this.unviredSdk.registerNotifListener().subscribe((result: NotifResult) => {
      this.ngZone.run(() => {
        console.log('Notification result:', result);

        this.store.dispatch(addNotification({ notification: result }));
        if(result.type != NotificationListenerType.attachmentDownloadSuccess){
          let downloadProgress =  ump.updateSyncItemsCount((percentage: any) => {
            console.log('downloadProgress:', percentage);
            this.dataDownloadPercentage = percentage;
          }, (error: any) => {
            console.log('Error in updateSyncItemsCount:', error);
          })
       
          console.log('downloadProgress',downloadProgress);
        }
        switch (result.type) {
          case NotificationListenerType.dataReceived:
            console.log('Data received:', result.data);
            break;
          case NotificationListenerType.dataSend:
            console.log('Data sent:', result.data);
            break;
          case NotificationListenerType.dataChanged:
            console.log('Data changed:', result.data);
            if (result.data === 'RELEASE_NOTE_HEADER') {
            this.store.dispatch(RigActions.loadReleaseNotesFromDb());
            }
            break;
          case NotificationListenerType.JWTTokenReceived:
            console.log('JWT Token received:', result.data);
            break;
          case NotificationListenerType.attachmentDownloadSuccess:
            this.store.dispatch(loadProgressBar());
            break;
          case NotificationListenerType.incomingDataProcessingFinished:
            console.log('[Store-Fixes][NotificationService] Initial data processing finished - setting completion flag');
            console.log('[Store-Fixes] Dispatching loadAllTemplatesFromDb action');
            this.store.dispatch(RigActions.loadAllTemplatesFromDb());
            console.log('[Store-Fixes] Dispatching loadPrefilledData action');
            this.store.dispatch(RigActions.loadPrefilledData());
            console.log('[Store-Fixes] Dispatching loadAllFormsFromDb action');
            this.store.dispatch(RigActions.loadAllFormsFromDb());
            console.log('[Store-Fixes] Dispatching loadStmrDataAndTopicsFromDb action');
            this.store.dispatch(RigActions.loadStmrDataAndTopicsFromDb());
            console.log('[Store-Fixes] Dispatching loadSpConfig action');
            this.store.dispatch(RigActions.loadSpConfig());
            this.dataService.processSpConfigAndSaveToSettings();
            // TODO: Add dispatch for loading RELEASE NOTES

           this.store.dispatch(RigActions.loadReleaseNotesFromDb());
            // this.releaseNotesService.updateCount();

            // Set flag that initial data download is complete
            console.log('[NotificationService] Dispatching initialDataDownloadComplete action');
            this.store.dispatch(RigActions.initialDataDownloadComplete());
            break;
        }
      });
    });
  }
}
