angular.module('PDTest')
.directive('pdNotification', function() {

    function getHTML(model) {
        var html='<div ng-show="form.allowNotifications">';
        html +='    <hr id="notifications">';
        html +='    <h4>Corrective Actions</h4>';
        html +='    <div class="alert alert-info" ng-if="'+model+'.length===0">';
        html +='        <div class="pull-right">';
        html +='            <span class="icon-selected" ng-click="addNotificationRow()" ng-hide="true">';
        html +='                <svg class="icon icon-ion-plus-circled"><use xlink:href="#icon-ion-plus-circled"></use></svg>';
        html +='            </span>';
        html +='        </div>';
        html +='        <div>No potential incidents or hazards reported.</div>';
        html +='    </div>';
        html +='    <div class="table-responsive" ng-hide="'+model+'.length === 0">';
        html +='        <table class="table table-bordered">';
        html +='            <thead class="thead-inverse">';
        html +='                <td class="col-sm-2">Created By</td>';
        html +='                <td class="col-sm-2">Date</td>';
        html +='                <td class="col-sm-2">Status</td>';
        html +='                <td class="col-sm-1">SAP ID</td>';
        html +='                <td class="col-sm-7">Reason for Notification</td>';
        html +='                <td class="col-sm-5">Comments</td>';
        html +='                <td class="col-sm-1 text-center" ng-hide="submitted">';
        html +='                    <span class="icon-light" ng-click="addNotificationRow()" ng-hide="true">';
        html +='                        <svg class="icon icon-ion-plus-circled"><use xlink:href="#icon-ion-plus-circled"></use></svg>';
        html +='                    </span>';
        html +='                </td>';
        html +='            </thead>  ';
        html +='            <tbody>';
        html +='                <tr ng-repeat="item in '+model+'">';
		html +='                    <td class="col-sm-2 responsive-cell" data-label="Created By" ng-class="{\'empty-cell\': !item.CreatedBy}" empty-text="None">{{item.CreatedBy}}</td>';
		html +='                    <td class="col-sm-2 responsive-cell" data-label="Date" ng-class="{\'empty-cell\': !item.CreateDate}" empty-text="None">{{item.CreateDate|checkUNIX: \'MMM dd yyyy H:mm\': \''+model+'.CreateDateTzOffset\'}} {{ data[form.mainTable].CreateDateTzAbbr }}  (24Hrs)</td>';
		html +='                    <td class="col-sm-2 responsive-cell" data-label="Status" ng-class="{\'empty-cell\': !item.Status}" empty-text="None" ng-if="item.Status === \'NEW\'">     <svg class="icon icon-ion-close"><use xlink:href="#icon-ion-close"></use></svg>&nbsp;{{item.Status}}</td>';
        html +='                    <td class="col-sm-2 responsive-cell" data-label="Status" ng-class="{\'empty-cell\': !item.Status}" empty-text="None" ng-if="item.Status === \'CLOSED\'">  <svg class="icon icon-ion-wrench"><use xlink:href="#icon-ion-wrench"></use></svg>&nbsp;{{item.Status}}</td>';
		html +='                    <td class="col-sm-2 responsive-cell" data-label="Status" ng-class="{\'empty-cell\': !item.Status}" empty-text="None" ng-if="item.Status !== \'NEW\' && item.Status !== \'CLOSED\'">{{item.Status}}</td>'
        html +='                    <td class="col-sm-1 responsive-cell" data-label="SAP ID" ng-class="{\'empty-cell\': !item.NotificationNo}" empty-text="None">{{item.NotificationNo}}</td>';
        html +='                    <td class="col-sm-7" data-label="Reason for Notification" ng-class="{ \'has-error\': contentForm[\'reason\'+$index].$invalid }">';
        html +='                        <textarea name="reason{{$index}}" class="form-control" ng-disabled="true" rows="6"';
		html +='							ng-model="item.Reason" maxlength="1000" ng-maxlength="1000">'
		html +='                        </textarea>';
        html +='                        <span ng-if="contentForm[\'reason\'+$index].$valid" class="text-muted">{{1000-item.Reason.length}} characters remaining</span>';
        html +='                        <div class="help-block" ng-messages="contentForm[\'reason\'+$index].$error" role="alert">';
        html +='                            <div ng-message="required">A reason is required</div>';
        html +='                            <div ng-message="maxlength">Maximum of 1000 characters allowed only.</div>';
        html +='                        </div>';
        html +='                    </td>';
        html +='                    <td class="col-sm-5 responsive-cell" data-label="Comments" ng-class="{\'empty-cell\': !item.Action}" empty-text="None">{{item.Action}}</td>';
        html +='                    <td class="col-sm-1 text-center" ng-hide="submitted">';
        html +='                        <span class="icon-muted" ng-click="deleteNotificationRow(item)" ng-hide="true">';
        html +='                            <svg class="icon icon-ion-android-remove-circle"><use xlink:href="#icon-ion-android-remove-circle"></use></svg>';
        html +='                        </span> ';
        html +='                    </td>';
        html +='                </tr>';
        html +='            </tbody>';
        html +='        </table>';
        html +='    </div>';
        html +='</div>';
        return html;
    }

    return {
        restrict: 'EA',
        template: function(elem, attr){
            return getHTML(attr.model);
         },
    };

});