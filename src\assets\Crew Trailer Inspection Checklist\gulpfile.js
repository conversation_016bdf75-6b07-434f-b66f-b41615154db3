var gulp = require('gulp'),
    uglify = require('uglify-es'),
    composer = require('gulp-uglify/composer'),
    sourcemaps = require('gulp-sourcemaps'),
    smoosher = require('gulp-smoosher');
minifier = composer(uglify, console);
replace = require('gulp-string-replace');

gulp.task('smoosh', function () {
    gulp.src('index.html')
        .pipe(smoosher())
        .pipe(replace('..\/fonts', '.\/fonts'))
        .pipe(gulp.dest('dist'));
});

gulp.task('copyschema', function () {
    gulp.src('schema.csv')
        .pipe(gulp.dest('./dist'));
});

gulp.task('copyfonts', function () {
    gulp.src('fonts/*')
        .pipe(gulp.dest('./dist/fonts'));
});

gulp.task('copycomponents', function () {
    return gulp.src('components/**/*')
        .pipe(gulp.dest('./dist/components'));
});

gulp.task('default', ['smoosh', 'copyschema', 'copyfonts', 'copycomponents']);