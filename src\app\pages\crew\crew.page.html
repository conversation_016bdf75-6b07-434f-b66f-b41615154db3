<ion-header class="crew-header">
  <ion-toolbar color="primary" class="crew-toolbar" mode="md">
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="closeModal()">
        <ion-icon name="arrow-back-outline" style="color: white;"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title class="crew-title">{{ 'Select Crew Members' | translate }}</ion-title>
    <ion-buttons slot="end">
      <ion-button id="saveButton" fill="primary" (click)="saveCrewMember()" class="save-button">
        {{ 'SAVE' | translate }}
      </ion-button>
      <ion-button id="addCrewButton" fill="primary" (click)="addCrewMember()" class="edit-crew-button">
        {{ 'EDIT' | translate }}
      </ion-button>
    </ion-buttons>
  </ion-toolbar>

  <!-- Searchbar -->
  <div class="search-container">
    <ion-searchbar
      class="crew-searchbar"
      placeholder="{{ 'Search Crew Members' | translate }}"
      [(ngModel)]="filter.searchText"
      (ionInput)="filterList($event.detail.value || '')"
      (ionCancel)="filterList('')"
    >
    </ion-searchbar>
  </div>
</ion-header>

<ion-content class="crew-content ion-padding" [ngClass]="{ contrast: styleTheme == 'contrast' }">
  <!-- Crew members list -->
  <div class="crew-list" *ngIf="displayCrewMembers.length > 0">
    <ion-card
      *ngFor="let crew of displayCrewMembers"
      class="crew-card"
      [ngClass]="{
        'selected-card': crew.isSelected,
        'has-error': hasCrew
      }"
      [id]="crew.index"
      [class.selected]="crew.isSelected"
      (click)="onCardClick(crew, $event)"
    >
      <ion-card-content class="crew-card-content">
        <!-- Checkbox positioned at top right -->
        <div class="checkbox-container">
          <ion-checkbox
             [checked]="crew.isSelected" 
            (ionChange)="onCheckboxChange(crew, $event)"
            (click)="$event.stopPropagation()"
            class="selection-checkbox"
          ></ion-checkbox>
        </div>

        <!-- Crew member details -->
        <div class="crew-details">
          <div class="crew-field" *ngIf="crew.USER_NAME">
            <span class="field-label">Name:</span>
            <span class="field-value" [ngClass]="{ 'mandatory-text': !hasAnySelection() }">
              {{ crew.USER_NAME }}
            </span>
          </div>

          <div class="crew-field" *ngIf="crew.DESIGNATION">
            <span class="field-label">Position:</span>
            <span class="field-value" [ngClass]="{ 'mandatory-text': !hasAnySelection() }">
              {{ crew.DESIGNATION }}
            </span>
          </div>

          <div class="crew-field" *ngIf="crew.SOURCE !== 'LOCAL' && getUserId(crew)">
            <span class="field-label">User ID:</span>
            <span class="field-value" [ngClass]="{ 'mandatory-text': !hasAnySelection() }">
              {{ getUserId(crew) }}
            </span>
          </div>

          <div class="crew-field" *ngIf="getGlobalId(crew)">
            <span class="field-label">Global ID:</span>
            <span class="field-value" [ngClass]="{ 'mandatory-text': !hasAnySelection() }">
              {{ getGlobalId(crew) }}
            </span>
          </div>

          <div class="crew-field" *ngIf="crew.SOURCE !== 'LOCAL' && getSource(crew)">
            <span class="field-label">Personnel No:</span>
            <span class="field-value" [ngClass]="{ 'mandatory-text': !hasAnySelection() }">
              {{ getSource(crew) }}
            </span>
          </div>

          <div class="crew-field" *ngIf="crew.SOURCE === 'LOCAL'">
            <span class="field-label">Personnel No:</span>
            <span class="field-value" [ngClass]="{ 'mandatory-text': !hasAnySelection() }">
              LOCAL
            </span>
          </div>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Empty state -->
  <div class="empty-state" *ngIf="displayCrewMembers.length === 0">
    <p>No crew members found</p>
  </div>
</ion-content>
