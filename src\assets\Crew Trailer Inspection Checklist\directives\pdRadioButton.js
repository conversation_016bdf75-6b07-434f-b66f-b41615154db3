angular.module('PDTest')
.directive('pdRadioButton', function() {
    function getHTML(model, value, label, classname) {
        var html='<div class="vertical-align">';
        html +='<span class="'+classname+'">';
        html +='    <svg ng-if="'+model+'!=='+value+'" class="icon icon-ion-android-radio-button-off"><use xlink:href="#icon-ion-android-radio-button-off"></use></svg>';
        html +='    <svg ng-if="'+model+'==='+value+'" class="icon icon-ion-android-radio-button-on"><use xlink:href="#icon-ion-android-radio-button-on"></use></svg>';
        html +='</span>';
        html +='<label class="control-label">&nbsp;'+label+'</label>';
        html +='</div>';
        return html;
    }

    return {
        restrict: 'EA',
        template: function(elem, attr){
            return getHTML(attr.model, attr.value, attr.label, attr.classname);
         },
    };

});