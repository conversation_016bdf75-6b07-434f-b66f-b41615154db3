import { Injectable, NgZone } from '@angular/core';
import { RequestType, ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AppConstants } from '../constants/appConstants';
import { RIG_HEADER } from 'src/models/RIG_HEADER';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { SiteRigModalComponent } from '../components/site-rig-modal/site-rig-modal.component';
import { PopoverController } from '@ionic/angular/standalone';
import { ReplaySubject, take, firstValueFrom, filter } from 'rxjs';
import { selectAllForms, selectPrefilledData, selectRigData } from '../store/store.selector';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import * as StoreActions from '../store/store.actions';
import { TEMPLATE_HEADER } from 'src/models/TEMPLATE_HEADER';
import { HttpClient } from '@angular/common/http';
import { FORM_HEADER } from 'src/models/FORM_HEADER';
import * as RigActions from '../store/store.actions';
import moment from 'moment';
import { UtilityService } from './utility.service';
import { FORM_ACTION } from 'src/models/FORM_ACTION';
import { STMR_FORM } from 'src/models/STMR_FORM';
import { STMR_ACTION } from 'src/models/STMR_ACTION';
import { STMR_FORM_DATA } from 'src/models/STMR_FORM_DATA';
import { STMR_CREW } from 'src/models/STMR_CREW';
import { STMR_TOPIC } from 'src/models/STMR_TOPIC';
import { STMR_HEADER } from 'src/models/STMR_HEADER';
import { RELEASE_NOTE_HEADER } from 'src/models/RELEASE_NOTE_HEADER';
import { MASTER_DATA_HEADER } from 'src/models/MASTER_DATA_HEADER';
import { FORM_SCHEDULE_HEADER } from 'src/models/FORM_SCHEDULE_HEADER';
import { BusyIndicatorService } from './busy-indicator.service';
import { Actions } from '@ngrx/effects';
import { Router } from '@angular/router';
import { TMPLT_ATTACHMENT } from 'src/models/TMPLT_ATTACHMENT';
import { AttachmentHelper } from '../helpers/attachment-helper';
import { RIG_MANAGER } from 'src/models/RIG_MANAGER';
import { C_RIG_WRK_CNTR_HEADER } from 'src/models/C_RIG_WRK_CNTR_HEADER';
import { CTA_TMPLT } from 'src/models/CTA_TMPLT';
import { STMR_TOPIC_ENTITY } from 'src/models/STMR_TOPIC_ENTITY';


 declare var ump: any;
@Injectable({
  providedIn: 'root'
})
export class DataService {
  // Direct forms storage to bypass store issues
  private directFormsData: any[] = [];

     rigData$!: Observable<RIG_HEADER| null>;
     templates$!: Observable<TEMPLATE_HEADER[]| null>;
      isConnectedToNetwork: boolean = false;
private releaseNotesFetched = false;
newReleaseNoteAvailable: number = 0;
private isPathSaved: boolean = false;

 private deviceId =''
  rigResult: any
  selectedServer : string = '';
  functionNames = [
  "FORMS_PA_CATEGORY_GET",
  "FORMS_PA_CUSTOMIZING_GET_V2",
  "FORMS_PA_HISTORY_GET",
  "FORMS_PA_HSE_STANDARD_GET",
  "FORMS_PA_TEMPLATE_GET",
  "FORMS_PA_CREW_GET_V2",
  "FORMS_PA_CTA_GET",
  "FORMS_PA_RELEASE_NOTE_GET"
];
isDeleting: boolean = false;

  inputArray = [
    {
      "name": "FORMS_PA_CATEGORY_GET",
      "input": {
        "INPUT_RIG_CONTEXT": [
          {
            "INPUT_RIG_CONTEXT_HEADER": {
              "RIG_NO": "CAN701O",
              "RIG_TYPE": "SHOP",
              "RIG_SUB_TYPE": "SHOP_OFFICE",
              "COMP_CODE": "1000",
              "DEVICE_NAME": "0300b83d-0800-7a87-26b3-000522780005"
            }
          }
        ]
      }
    },
    {
      "name": "FORMS_PA_CUSTOMIZING_GET_V2",
      "input": {
        "INPUT_RIG_CONTEXT": [
          {
            "INPUT_RIG_CONTEXT_HEADER": {
              "RIG_NO": "CAN701O",
              "RIG_TYPE": "SHOP",
              "RIG_SUB_TYPE": "SHOP_OFFICE",
              "COMP_CODE": "1000",
              "DEVICE_NAME": "0300b83d-0800-7a87-26b3-000522780005"
            }
          }
        ]
      }
    },

 {
      "name": "FORMS_PA_CATEGORY_GET",
      "input": {
        "INPUT_RIG_CONTEXT": [
          {
            "INPUT_RIG_CONTEXT_HEADER": {
              "RIG_NO": "CAN701O",
              "RIG_TYPE": "SHOP",
              "RIG_SUB_TYPE": "SHOP_OFFICE",
              "COMP_CODE": "1000",
              "DEVICE_NAME": "0300b83d-0800-7a87-26b3-000522780005"
            }
          }
        ]
      }
    },

    
 {
  "name": "FORMS_PA_HISTORY_GET",
  "input": {
    "INPUT_RIG_CONTEXT": [
      {
        "INPUT_RIG_CONTEXT_HEADER": {
          "RIG_NO": "CAN701O",
          "RIG_TYPE": "SHOP",
          "RIG_SUB_TYPE": "SHOP_OFFICE",
          "COMP_CODE": "1000",
          "DEVICE_NAME": "0300b83d-0800-7a87-26b3-000522780005"
        }
      }
    ]
  }
},
{
  "name": "FORMS_PA_HSE_STANDARD_GET",
  "input": {
    "INPUT_RIG_CONTEXT": [
      {
        "INPUT_RIG_CONTEXT_HEADER": {
          "RIG_NO": "CAN701O",
          "RIG_TYPE": "SHOP",
          "RIG_SUB_TYPE": "SHOP_OFFICE",
          "COMP_CODE": "1000",
          "DEVICE_NAME": "0300b83d-0800-7a87-26b3-000522780005"
        }
      }
    ]
  }
},
{
  "name": "FORMS_PA_TEMPLATE_GET",
  "input": {
    "INPUT_RIG_CONTEXT": [
      {
        "INPUT_RIG_CONTEXT_HEADER": {
          "RIG_NO": "CAN701O",
          "RIG_TYPE": "SHOP",
          "RIG_SUB_TYPE": "SHOP_OFFICE",
          "COMP_CODE": "1000",
          "DEVICE_NAME": "0300b83d-0800-7a87-26b3-000522780005"
        }
      }
    ]
  }
}
  ]

  inputData =[
    {
      "name": "FORMS_PA_CATEGORY_GET",
      "input": {
        "INPUT_RIG_CONTEXT": [
          {
            "INPUT_RIG_CONTEXT_HEADER": {
              "RIG_NO": "CAN701O",
              "RIG_TYPE": "SHOP",
              "RIG_SUB_TYPE": "SHOP_OFFICE",
              "COMP_CODE": "1000",
              "DEVICE_NAME": "0300b83d-0800-7a87-26b3-000522780005"
            }
          }
        ]
      }
    }
    
    
  ]

  


  constructor(private utilityService: UtilityService,
    private http: HttpClient ,
    private unviredSDK: UnviredCordovaSDK ,
    private busyIndicatorService: BusyIndicatorService,
    private ngZone: NgZone,
    private router: Router,
    private devicePlugin: Device,
     private popoverController: PopoverController,
      private store: Store,
       private actions$: Actions,
      private attachmentHelper: AttachmentHelper, ) { 
    this.rigData$ = this.store.select(selectRigData); 
       this.isConnectedToNetwork = navigator.onLine
       this.deviceId = this.devicePlugin.uuid

  }

  public initialDataDownloaded$ = new ReplaySubject<void>(1);


    async getRigHeader(rigNo: string , deviceId: string) {
    let inputRigContext = {
      "INPUT_RIG_CONTEXT": [
        {
          "INPUT_RIG_CONTEXT_HEADER": {
            "RIG_NO": rigNo,
            "DEVICE_NAME": deviceId,
            // "LID": this.unviredSDK.guid()
          }
        }
      ]
    }

    console.log('inputrigcontext' , inputRigContext)
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.PULL,

        '',
         inputRigContext,
        AppConstants.FORMS_PA_RIG_GET,
        true
      );
      if (result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
    
          this.unviredSDK.logInfo(
          'DataService',
          'getRigHeader',
          'RigHeader Data has downloaded successfully.'
        );

      
      } else if (result.code && result.code === 401) {
  
        this.unviredSDK.logError(
          'DataService',
          'getRigHeader',
          'Error while downloading righeader data .'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getRigHeader',
          'Error while downloading righeader data : ' + result.message
        );
      }
      return result.data;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getRigHeader',
        'Error while downloading righeader catch data : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }



  async getRigHeaderFromDB(): Promise<RIG_HEADER> {
    console.log('checkRigInSettingsTable')
    const query = `SELECT * FROM ${AppConstants.TABLE_RIG_HEADER} WHERE RIG_NO IS NOT NULL AND RIG_NO != ''`;


    try {
      const result = await this.unviredSDK.dbExecuteStatement(query);
      console.log('getRigHeader result after db call:', JSON.stringify(result.data[0], null, 2));
      if (result?.data?.length > 0) {
        this.rigResult = result.data[0]

        // Set flag that initial data download is starting
        console.log('Starting initial data download - setting start flag');
        return result.data[0]; // Return first matching row
      } else {
        console.log('No rig data found in DB');
        return new RIG_HEADER()

      }
    } catch (error) {
      this.unviredSDK.logError(
        "HomePage",
        "checkRigHeaderTable",
        `Error while fetching site number from ${AppConstants.TABLE_RIG_HEADER}: ${JSON.stringify(error)}`
      );
      throw error;
    }
  }


  inputDataForCustomization(rigResult: RIG_HEADER){
    this.deviceId = this.devicePlugin.uuid
    console.log('the device id and rig no in input is ' , this.deviceId , rigResult.RIG_NO)
    let rigContextHeader = {
      RIG_NO: rigResult.RIG_NO,
      RIG_TYPE: rigResult.RIG_TYPE,
      RIG_SUB_TYPE: rigResult.RIG_SUB_TYPE,
      COMP_CODE: rigResult.COMP_CODE,
      DEVICE_NAME: this.deviceId
    };

    const inputArrayForCustomization = this.functionNames.map(name => ({
      name,
      input: {
        INPUT_RIG_CONTEXT: [
          {
            INPUT_RIG_CONTEXT_HEADER: rigContextHeader
          }
        ]
      }
    }));
    return inputArrayForCustomization;

  }

  async openSiteNumberPopup() {
    console.log('openSiteNumberPopup called from templates page');
    const modalRef = await this.popoverController.create({
      component: SiteRigModalComponent,
      cssClass: 'custom-site-modal',
      backdropDismiss: false,
      mode: 'md'
    });
  
    modalRef.onDidDismiss().then((result: any) => {
      console.log('Modal dismissed with data:', result);
      if (result.data) {
        console.log('Site number selected:', result.data);
        // Optional: reload rig data here or navigate
      }
    });
  
    await modalRef.present();
  }


  async getAllTemplatesFromDB(company: string , rigNo: string , rigType: string , rigSubType: string): Promise<any> {
    console.log('get templates from db called')
    const query = ` SELECT TH.*, CH.NAME AS CATEGORY_DESC, TV.VER_ID AS L_VER_ID, MAX(TV.VER_NO)  AS L_VER_NO, TH.PBLSH_ON AS L_CRTD_ON, TA.ATTACHMENT_STATUS AS ATT_STATUS, FSA.NEXT_DUE AS NEXT_DUE  FROM TMPLT_HEADER AS TH, CATEGORY_HEADER AS CH, TMPLT_VER AS TV, TMPLT_ATTACHMENT AS TA, TMPLT_ASSGN AS TA, (Select ALERT_HEADER.NEXT_DUE, ALERT_HEADER.COMPANY, ALERT_HEADER.RIG_NO, TMPLT_HEADER.TMPLT_ID from TMPLT_HEADER left join ( SELECT * from FORM_SCHD_ALERT_HEADER  WHERE FORM_SCHD_ALERT_HEADER.COMPANY = ${company} AND FORM_SCHD_ALERT_HEADER.RIG_NO = '${rigNo}')  AS ALERT_HEADER on TMPLT_HEADER.TMPLT_ID = ALERT_HEADER.TMPLT_ID) AS FSA WHERE FSA.TMPLT_ID = TH.TMPLT_ID AND TH.CAT_ID = CH.CAT_ID AND TV.STATUS = 'REL' AND TH.IS_ACTIVE = 'true' AND TH.TMPLT_ID = TV.TMPLT_ID AND TA.TAG1 = TV.VER_ID AND TA.TMPLT_ID = TH.TMPLT_ID AND TA.TMPLT_ID IN (SELECT TMPLT_ID FROM TMPLT_ASSGN WHERE ASSGN_TYPE = '${rigType}' and VAL1 = '${rigSubType}') AND TA.TMPLT_ID NOT IN (SELECT TMPLT_ID FROM TMPLT_ASSGN WHERE ASSGN_TYPE = 'COMPANY' AND VAL1 != ${company} EXCEPT SELECT TMPLT_ID FROM TMPLT_ASSGN WHERE ASSGN_TYPE = 'COMPANY' AND VAL1 = ${company}) GROUP BY TH.TMPLT_ID ORDER BY CATEGORY_DESC`;

  console.log('getAllTemplatesFromDB query:', company , rigNo);
    try {
      const result = await this.unviredSDK.dbExecuteStatement(query);
      console.log('get table Header result after db call:', result.data);
      if (result?.data?.length > 0) {

     
        return result.data; // Return first matching row
      } else {
     
        throw new Error("No Template data");
       
      }
    } catch (error) {
     this.unviredSDK.logError(
        "HomePage",
        "checkRigInSettingsTable",
        `Error while fetching site number from ${AppConstants.TABLE_SETTING_HEADER}: ${JSON.stringify(error)}`
      );
      throw error;
    }
  }

  async getCountOfTemplateAttachmentFromDB(){
 const query =   `SELECT COUNT(*) AS total_attachments FROM ${AppConstants.TABLE_TMPLT_ATTACHMENT};`
//  console.log('getAllTemplatesFromDB query:', company , rigNo);
 try {
   const result = await this.unviredSDK.dbExecuteStatement(query);
   console.log('get table Header result after db call:', result.data);
   if (result?.data?.length > 0) {

  
     return result.data; // Return first matching row
   } else {
   
     throw new Error("No Template Attachment data");
    
   }
 } catch (error) {
  this.unviredSDK.logError(
     "HomePage",
     "checkRigInSettingsTable",
     `Error while fetching site number from ${AppConstants.TABLE_TMPLT_ATTACHMENT}: ${JSON.stringify(error)}`
   );
   throw error;
 }
  }


 async getProgressBarPercentage(){
    const query = `SELECT 
      COUNT(*) as total_attachments,
      SUM(CASE WHEN ATTACHMENT_STATUS = 'DOWNLOADED' THEN 1 ELSE 0 END) as downloaded_attachments
      FROM TMPLT_ATTACHMENT WHERE AUTO_DOWNLOAD = 'true';`

     try {
       const result = await this.unviredSDK.dbExecuteStatement(query);
       console.log('Progress query result:', result.data);
       
       if (result?.data?.length > 0) {
        const data = result.data[0];
        const total = parseInt(data.total_attachments) || 0;
        const downloaded = parseInt(data.downloaded_attachments) || 0;
        
        if (total === 0) {
          return 100; // No attachments to download means 100% complete
        }
        
        // Calculate percentage and ensure it reaches 100% when all are downloaded
        const percent = (downloaded / total) * 100;
        
        // If all attachments are downloaded, return exactly 100%
        if (downloaded >= total) {
          return 100;
        }
        
        // Round to 2 decimal places for display
        return Math.round(percent * 100) / 100;
       } else {
         return 100; // No data means no downloads needed, so 100% complete
       }
     } catch (error) {
      this.unviredSDK.logError(
         "Templates page",
         "getProgressBarPercentage",
         `Error while fetching percentage progress from ${AppConstants.TABLE_TMPLT_ATTACHMENT}: ${JSON.stringify(error)}`
       );
       return 100; // Return 100% on error to avoid stuck progress bar
     }
  }


  getPrefillData(): Observable<any> {
 
      return this.http.get('assets/prefill-data.json');
  }

  async getCompanyHeaderData(): Promise<any>{

      let query = `SELECT C.* FROM (SELECT COMP_CODE FROM RIG_HEADER) AS R LEFT JOIN (SELECT * FROM COMPANY_HEADER) AS C ON R.COMP_CODE = C.CODE`
      try {
        const companyData = await this.unviredSDK.dbExecuteStatement(query);
     if (companyData?.data?.length > 0) {
      console.log('get company result after db call:', companyData.data[0])
      return companyData.data[0]
     } else {
       throw new Error("No company data is present");
     }
   } catch (error) {
    this.unviredSDK.logError(
       "Company table ",
       "getCompanyHeaderData",
       `Error while fetching company data from ${AppConstants.TABLE_COMPANY_HEADER}: ${JSON.stringify(error)}`
     );
     throw error;
   }
    }



    async getCrewHeaderData(): Promise<any>{
      let query = `SELECT * FROM ${AppConstants.TABLE_CREW_HEADER} ORDER BY USER_NAME`
      try {
        const crewData = await this.unviredSDK.dbExecuteStatement(query);
        console.log('crew daata' , crewData)
     if (crewData?.data?.length > 0) {
      console.log('get Crew result after db call:', crewData.data)
      return crewData.data
     } else {
       throw new Error("No Crew data is present");
     }
   } catch (error) {
    this.unviredSDK.logError(
       "Crew table ",
       "getCrewHeaderData",
       `Error while fetching company data from ${AppConstants.TABLE_CREW_HEADER}: ${JSON.stringify(error)}`
     );
     throw error;
   }
    }


    async getOperatorHeaderData(): Promise<any>{
      let query = `SELECT * FROM ${AppConstants.TABLE_OPERATOR_HEADER} ORDER BY NAME`
      try {
        const operatorData = await this.unviredSDK.dbExecuteStatement(query);
     if (operatorData?.data?.length > 0) {
      return operatorData.data
     } else {
       throw new Error("No operatorData is present");
     }
   } catch (error) {
    this.unviredSDK.logError(
       "Operator table ",
       "getOperatorHeaderData",
       `Error while fetching operatorData from ${AppConstants.TABLE_OPERATOR_HEADER}: ${JSON.stringify(error)}`
     );
     throw error;
   }
    }


    async getNotifTypeHeaderData():Promise<any>{
      let query = `SELECT * FROM ${AppConstants.TABLE_C_NOTIF_TYPE_HEADER}`
      try {
        const notifTypeData = await this.unviredSDK.dbExecuteStatement(query);
     if (notifTypeData?.data?.length > 0) {
      console.log('get notifTypeData after db call:', notifTypeData.data)
      return notifTypeData.data
     } else {
       throw new Error("No notifTypeData is present");
     }
   } catch (error) {
    this.unviredSDK.logError(
       "Notif type table ",
       "getNotifTypeHeaderData",
       `Error while fetching TABLE_C_NOTIF_TYPE_HEADER from ${AppConstants.TABLE_C_NOTIF_TYPE_HEADER}: ${JSON.stringify(error)}`
     );
     throw error;
   }
    }

        async getPriorityHeaderData():Promise<any>{
      let query = `SELECT * FROM ${AppConstants.TABLE_C_PRIORITY_HEADER}`
      try {
        const priorityData = await this.unviredSDK.dbExecuteStatement(query);
     if (priorityData?.data?.length > 0) {
      console.log('get priorityData after db call:', priorityData.data)
      return priorityData.data
     } else {
       throw new Error("No priorityData is present");
     }
   } catch (error) {
    this.unviredSDK.logError(
       "Priority type table ",
       "getPriorityHeaderData",
       `Error while fetching TABLE_C_PRIORITY_HEADER from ${AppConstants.TABLE_C_PRIORITY_HEADER}: ${JSON.stringify(error)}`
     );
     throw error;
   }
    }


    


        async getCodeGroupHeaderData():Promise<any>{
      let query = `SELECT * FROM ${AppConstants.TABLE_C_CODE_GROUP_HEADER}`
      try {
        const codeGroupData = await this.unviredSDK.dbExecuteStatement(query);
     if (codeGroupData?.data?.length > 0) {
      console.log('get codeGroupData after db call:', codeGroupData.data)
      return codeGroupData.data
     } else {
       throw new Error("No codeGroupData is present");
     }
   } catch (error) {
    this.unviredSDK.logError(
       "Priority type table ",
       "getCodeGroupHeaderData",
       `Error while fetching TABLE_C_CODE_GROUP_HEADER from ${AppConstants.TABLE_C_CODE_GROUP_HEADER}: ${JSON.stringify(error)}`
     );
     throw error;
   }
    }


          async getCodeHeaderData():Promise<any>{
      let query = `SELECT * FROM ${AppConstants.TABLE_C_CODE_HEADER}`
      try {
        const codeData = await this.unviredSDK.dbExecuteStatement(query);
     if (codeData?.data?.length > 0) {
      console.log('get code header after db call:', codeData.data)
      return codeData.data
     } else {
       throw new Error("No codeData is present");
     }
   } catch (error) {
    this.unviredSDK.logError(
       "Code type table ",
       "getCodeHeaderData",
       `Error while fetching TABLE_C_CODE_HEADER from ${AppConstants.TABLE_C_CODE_HEADER}: ${JSON.stringify(error)}`
     );
     throw error;
   }
    }

  async getWorkCenterHeaderData(): Promise<C_RIG_WRK_CNTR_HEADER> {
    let query = `SELECT * FROM ${AppConstants.TABLE_C_RIG_WRK_CNTR_HEADER}`
    try {
      const codeData = await this.unviredSDK.dbExecuteStatement(query);
      if (codeData?.data?.length > 0) {
        console.log('get Rig Work Center Headers after db call:', codeData.data)
        return codeData.data
      } else {
        throw new Error("No Rig Work Centers are present");
      }
    } catch (error) {
      this.unviredSDK.logError(
        'data.service.ts',
        "getWorkCenterHeaderData",
        `Error while fetching getWorkCenterHeaderData from ${AppConstants.TABLE_C_RIG_WRK_CNTR_HEADER}: ${JSON.stringify(error)}`
      );
      throw error;
    }
  }

  async getStmrHeaderData(): Promise<STMR_HEADER[]> {
    let query = `SELECT * FROM ${AppConstants.TABLE_STMR_HEADER} WHERE (P_MODE IS NULL OR P_MODE <> 'D') ORDER BY CRTD_ON DESC`;
    try {
      const stmrHeaderData = await this.unviredSDK.dbExecuteStatement(query);
      if(stmrHeaderData.type === ResultType.success){
        if (stmrHeaderData?.data?.length > 0) {
          console.log('get stmrHeaderData after db call:', stmrHeaderData.data)
          return stmrHeaderData.data
        } else {
          console.log("No stmrHeaderData is present")
          return [];
        }
      }else if (stmrHeaderData.type === ResultType.error)  {
  
        this.unviredSDK.logError(
          'DataService',
          'getRigHeader',
          'Error while downloading righeader data .'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getRigHeader',
          'Error while downloading righeader data : ' + stmrHeaderData.message
        );
      }
      return [];
      
    } catch (error) {
      this.unviredSDK.logError(
        "Code type table ",
        "getStmrHeaderData",
        `Error while fetching TABLE_STMR_HEADER from ${AppConstants.TABLE_STMR_HEADER}: ${JSON.stringify(error)}`
      );
      throw error;
    }
  }

  async getStmrTopicData(): Promise<STMR_TOPIC[]> {
    let query = `SELECT * FROM ${AppConstants.TABLE_STMR_TOPIC}`
    try {
      const stmrTopicData = await this.unviredSDK.dbExecuteStatement(query);
      if(stmrTopicData.type === ResultType.success){
        if (stmrTopicData?.data?.length > 0) {
          console.log('get stmrTopicData after db call:', stmrTopicData.data)
          return stmrTopicData.data
        } else {
          console.log("No stmrTopicData is present")
          return [];
        }
      }else if (stmrTopicData.type === ResultType.error)  {
  
        this.unviredSDK.logError(
          'DataService',
          'stmrTopicData',
          'Error while downloading righeader data .'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getStmrTopicData',
          'Error while downloading stmrTopicData data : ' + stmrTopicData.message
        );
      }
      return [];
      
    } catch (error) {
      this.unviredSDK.logError(
        "Code type table ",
        "stmrTopicData",
        `Error while fetching TABLE_STMR_TOPIC from ${AppConstants.TABLE_STMR_TOPIC}: ${JSON.stringify(error)}`
      );
      throw error;
    }
  }

  async initialDataDownloadCall(){
       await ump.sendInitialDataDownloadRequest(this.inputDataForCustomization(this.rigResult));
  }


 async getSharepointSitesFromDB(): Promise<any[]> {
  try {
    const query = `SELECT * FROM ${AppConstants.TABLE_SP_SITE_HEADER}`;
    const result = await this.unviredSDK.dbExecuteStatement(query); 
    if (result?.data?.length > 0) {
      console.log('[DataService] SharePoint sites loaded from DB:', result.data);
      return result.data;
    } else {
      console.warn('[DataService] No SharePoint site data found in DB');
      return [];
    }
  } catch (error) {
    console.error('[DataService] Error loading SharePoint sites from DB:', error);
    return [];
  }
}


async getForms(stmrId: string): Promise<STMR_FORM[]> {
    const query = `
      SELECT * FROM STMR_FORM
      WHERE STMR_ID = '${stmrId}'
      AND (P_MODE IS NULL OR P_MODE <> 'D')
      ORDER BY TOPIC_NO
    `;
    const result = await this.unviredSDK.dbExecuteStatement(query);
    return result.data as STMR_FORM[];
  }

  async getFormActions(stmrId: string): Promise<STMR_ACTION[]> {
    const query = `
      SELECT * FROM STMR_ACTION
      WHERE STMR_ID = '${stmrId}'
      AND (P_MODE IS NULL OR P_MODE <> 'D')
    `;
    const result = await this.unviredSDK.dbExecuteStatement(query);
    return result.data as STMR_ACTION[];
  }

  async getFormData(stmrId: string): Promise<STMR_FORM_DATA[]> {
    const query = `
      SELECT * FROM STMR_FORM_DATA
      WHERE STMR_ID = '${stmrId}'
      AND (P_MODE IS NULL OR P_MODE <> 'D')
      ORDER BY TOPIC_NO
    `;
    const result = await this.unviredSDK.dbExecuteStatement(query);
    return result.data as STMR_FORM_DATA[];
  }

  async getCrewMembers(stmrId: string): Promise<STMR_CREW[]> {
    const query = `
      SELECT * FROM STMR_CREW
      WHERE STMR_ID = '${stmrId}'
      AND (P_MODE IS NULL OR P_MODE <> 'D')
      ORDER BY CREW_NAME
    `;
    const result = await this.unviredSDK.dbExecuteStatement(query);
    return result.data as STMR_CREW[];
  }

  async getTopics(stmrId: string): Promise<STMR_TOPIC[]> {
    const query = `
      SELECT STMR_TOPIC.* FROM STMR_TOPIC
      JOIN TOPIC_HEADER
      WHERE STMR_TOPIC.STMR_ID = '${stmrId}'
      AND STMR_TOPIC.TOPIC_ID = TOPIC_HEADER.TOPIC_ID
      AND (STMR_TOPIC.P_MODE IS NULL OR STMR_TOPIC.P_MODE <> 'D')
    `;
    const result = await this.unviredSDK.dbExecuteStatement(query);
    return result.data as STMR_TOPIC[];
  }

  async getAllTopicNumbers(stmrId: string): Promise<string[]> {
  // Get ALL topic numbers for this STMR, including deleted ones
  const query = `SELECT TOPICNO FROM STMR_TOPIC WHERE STMRID = '${stmrId}' AND TOPICNO IS NOT NULL AND TOPICNO != ''`;
  
  try {
    const result = await this.unviredSDK.dbExecuteStatement(query);
    if (result?.data?.length > 0) {
      return result.data.map((row: any) => row.TOPICNO);
    }
    return [];
  } catch (error) {
    this.unviredSDK.logError('DataService', 'getAllTopicNumbers', `Error fetching all topic numbers: ${error}`);
    return [];
  }
}


  async reloadHeader(lid: string): Promise<STMR_HEADER> {
    const result = await this.unviredSDK.dbSelect(AppConstants.STMR_HEADER, `LID = '${lid}'`);
    if (result.data.length > 0) {
      return result.data[0] as STMR_HEADER;
    }
    throw new Error('No STMR Header present in DB');
  }    
    returnFormsPageQuery(formLid?: string) {
    var query = "SELECT * FROM (SELECT FORM_DATA.DATA, FORM_DATA.LID AS DATA_LID, FORM_DATA.FID AS DATA_FID, FORM_HEADER.* FROM FORM_HEADER LEFT JOIN FORM_DATA ON FORM_HEADER.FORM_ID = FORM_DATA.FORM_ID ORDER BY FORM_HEADER.CRTD_ON DESC) AS A LEFT JOIN (SELECT CH.CAT_ID, CH.NAME AS CATEGORY_DESC, TV.TMPLT_ID, TH.DESCR AS TEMPLATE_DESC, TV.CRTD_ON AS PUBLISHED_ON, TV.VER_ID AS TMPLT_VERSION FROM TMPLT_HEADER AS TH, TMPLT_VER AS TV, CATEGORY_HEADER AS CH  WHERE TH.TMPLT_ID = TV.TMPLT_ID AND TH.TMPLT_ID IS NOT 'STMR' AND CH.CAT_ID = TH.CAT_ID) AS B ON A.VER_ID = B.TMPLT_VERSION where A.FORM_STATUS IS NOT 'DEL' AND (A.P_MODE IS NOT 'D' OR A.P_MODE = '') ";

    // if(this.completedFormsIsChecked) {
    //   query += ` AND A.FORM_STATUS != 'SUBM' `;
    // }
  
    if (formLid) {
      query += ` AND LID = '${formLid}' `
    }

    query += ` ORDER BY CRTD_ON DESC`
    return query;

  }


  async getAllFormsFromTheDB(): Promise<any>{
     console.log('[DataService] getAllFormsFromTheDB called');
     let query = this.returnFormsPageQuery()
     console.log('[DataService] Query:', query);
    try {
      const formHeaderData = await this.unviredSDK.dbExecuteStatement(query);
      console.log('[DataService] Raw DB response:', formHeaderData);
      if (formHeaderData?.data?.length > 0) {
        console.log('[DataService] get formHeaderData after db call:', formHeaderData.data)
        console.log('[DataService] Returning forms count:', formHeaderData.data.length);
        return formHeaderData.data
      } else {
        console.log("[DataService] No formHeaderData is present, returning empty array");
        return [];
      }
    } catch (error) {
      console.error('[DataService] Error in getAllFormsFromTheDB:', error);
      this.unviredSDK.logError(
        "Code type table ",
        "getAllFormsFromTheDB",
        `Error while fetching TABLE_FORM_HEADER from ${AppConstants.TABLE_FORM_HEADER}: ${JSON.stringify(error)}`
      );
      throw error;
    }
  }

async getSTMRsCount(): Promise<number> {
  try {
    const result: any = await this.unviredSDK.dbExecuteStatement(
      `SELECT COUNT(*) as COUNT FROM ${AppConstants.TABLE_STMR_HEADER}`
    );

    if (result?.rows?.length) {
      return result.rows[0]?.COUNT ?? 0;
    } else {
      this.unviredSDK.logError('TemplatesPage', 'getSTMRsCount', 'No result returned');
      return 0;
    }
  } catch (error) {
    this.unviredSDK.logError('TemplatesPage', 'getSTMRsCount', JSON.stringify(error));
    throw error;
  }
}

async createFormHeader(template: TEMPLATE_HEADER) {
  let formHeader = new FORM_HEADER();

  // Get prefilled data from store
  const prefilledData = await firstValueFrom(this.store.select(selectPrefilledData).pipe(take(1)));

  formHeader.VER_ID = template.L_VER_ID;
  formHeader.LID = this.utilityService.guid32();
  formHeader.FORM_ID = 'New' + this.utilityService.guid32();
  formHeader.CRTD_BY = prefilledData?.USER_ID || '';
  formHeader.CRTD_ON = moment.utc().unix();
  formHeader.SUBM_BY = prefilledData?.USER_ID || '';
  formHeader.DATE_COMP = moment.utc().unix();
  formHeader.COMPANY = prefilledData?.COMP_CODE || '';
  formHeader.RIG_NO = prefilledData?.RIG_NO || '';
  formHeader.COMMENTS = "";
  formHeader.FORM_STATUS = AppConstants.VAL_FORM_STATUS.OPEN;
  formHeader.LAST_SYNC_USER = prefilledData?.USER_ID || '';
  formHeader.LAST_SYNC_TIME = moment.utc().unix();
  formHeader.P_MODE = "A";
  formHeader.TIME_ZONE = this.utilityService.getTimezone();
  formHeader.TEMPLATE_DESC = template.NAME;
  formHeader.CATEGORY_DESC = template.CATEGORY_DESC;
  formHeader.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
  formHeader.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;

  return formHeader;
}


async createFormFromTemplateAndSaveToDB(template: TEMPLATE_HEADER) {

  let formHeader = await this.createFormHeader(template);
  // console.log('formHeader in createFormAndSaveToDB is ' , formHeader);

  // Create a copy without the FID field for database insertion
  const { FID, ...formHeaderForDB } = formHeader;
 console.log('formHeader in createFormAndSaveToDB2 is ' , formHeader);

  let formCreated = await this.unviredSDK.dbInsert(AppConstants.TABLE_FORM_HEADER, formHeaderForDB, AppConstants.BOOL_TRUE);
  if(formCreated.type === ResultType.success){
    this.store.dispatch(RigActions.loadAllFormsFromDb());
    return formHeader;
  }else{
    throw new Error("Error while creating form" + formCreated.error);
  }
}
 setNetworkStatus(param:any) {
    this.isConnectedToNetwork = param;
  }
    
  getNetworkStatus() {
    return this.isConnectedToNetwork;
  }
   
async getMasterDataFromDB(): Promise<Record<string, any[]>> {
  const query = `SELECT * FROM ${AppConstants.TABLE_MASTER_DATA_HEADER}`;
  try {
    const result = await this.unviredSDK.dbExecuteStatement(query);
    if (!result?.data || result.data.length === 0) {
      return {};
    }
       // Normalize and group into required shape
    const grouped: Record<string, any[]> = {};

    for (let raw of result.data as any[]) {
      let row: any = raw;
      // Parse DATA_VALUE JSON if it is a stringified JSON
      if (typeof row?.DATA_VALUE === 'string') {
        try {
          row.DATA_VALUE = JSON.parse(row.DATA_VALUE);
        } catch {
          // keep as string if not JSON
        }
      }

      // Determine the master key column name defensively
      const key: string = (row.MASTER_KEY ?? row.KEY ?? row.NAME ??row.DATA_TYPE ?? '').toString();
      if (!key) {
        continue;
      }

      // The value to push can be string, number, or object
      let value: any = row.DATA_VALUE ?? row.VALUE ?? row.VAL ?? row.DESCR;

      // If value is an array of primitives, flatten by pushing each primitive
      if (Array.isArray(value)) {
        for (const v of value) {
          if (!grouped[key]) grouped[key] = [];
          grouped[key].push(v);
        }
      } else {
        if (!grouped[key]) grouped[key] = [];
        grouped[key].push(value);
      }
    }

    this.unviredSDK.logDebug("DataService", "getMasterDataFromDB", "MASTER_DATA grouped: " + JSON.stringify(grouped));
    return grouped;
  } catch (err) {
    this.unviredSDK.logError("DataService", "getMasterDataFromDB", "Error fetching MASTER_DATA_HEADER:" + err);
    return {};
  }
}

  async updateOrInsertFormHeaderInDB(formId: any, formStatus: any) {

    let updateResult = await this.unviredSDK.dbInsertOrUpdate(AppConstants.TABLE_FORM_HEADER, { FORM_ID: formId, FORM_STATUS: formStatus }, true);

    if (updateResult.type === ResultType.success) {
     console.log('Form header updated successfully');
        } else {
      throw new Error("Error while updating form header" + updateResult.error);
    }
  }


  async checkIfFormDataExists(form: any , updatedResult: string) {
    let query = `SELECT * FROM ${AppConstants.TABLE_FORM_DATA} WHERE FID = '${form.LID}'`;
    let checkedResult = await this.unviredSDK.dbExecuteStatement(query);
    if (checkedResult?.data?.length > 0) {
      console.log('Form data exists, updating...');
      this.store.dispatch(RigActions.updateFormData({ matchingForm: form, result: updatedResult }));
    //  this.updateFormDataInDB(form, updatedResult);
   
    } else {
      console.log('Form data does not exist, inserting...');
      this.store.dispatch(RigActions.insertFormData({ matchingForm: form, result: updatedResult }));
      // this.insertFormDataInDB(form, updatedResult);
     
    }
  }



  async insertFormDataInDB(matchingForm: any, result: string , ) {
    let LID = this.utilityService.guid32();
    let FID = matchingForm.LID;
    const timestamp = new Date().toISOString(); 
    let safeResult = result.replace(/'/g, "''");

    let insertFormDataQuery = `INSERT INTO FORM_DATA (FORM_ID, DATA, OBJECT_STATUS, P_MODE, LID , TIMESTAMP , SYNC_STATUS , FID)
    VALUES ('${matchingForm.FORM_ID}', '${safeResult}', '${matchingForm.OBJECT_STATUS}', 'A', '${LID}', '${timestamp}' , '${matchingForm.SYNC_STATUS}' , '${FID}')`;
    let insertResult = await this.unviredSDK.dbExecuteStatement(insertFormDataQuery);
    if (insertResult.type === ResultType.success) {
      let selectQuery = `SELECT * FROM ${AppConstants.TABLE_FORM_DATA} WHERE FORM_ID = '${matchingForm.FORM_ID}'`;
      let selectResult = await this.unviredSDK.dbExecuteStatement(selectQuery);
      console.log('selectResult is ' , selectResult);
          this.store.dispatch(RigActions.loadAllFormsFromDb());
    } else {
      throw new Error("Error while updating form data" + insertResult.error);
    }
  }



  async updateFormDataInDB(matchingForm: any, result: string) {
    let safeResult = result.replace(/'/g, "''");  
    let updateFormDataQuery = `UPDATE FORM_DATA SET DATA = '${safeResult}', OBJECT_STATUS = '${AppConstants.OBJECT_STATUS.MODIFY}', P_MODE = 'M' WHERE FORM_ID = '${matchingForm.FORM_ID}'`;
    let updateResult = await this.unviredSDK.dbExecuteStatement(updateFormDataQuery);
    if (updateResult.type === ResultType.success) {
        console.log('db updated successfully')
       this.store.dispatch(RigActions.loadAllFormsFromDb());
    } else {
      throw new Error("Error while updating form data" + updateResult.error);
    }
  }


  async submitFormToServer(form: FORM_HEADER) {
   console.log('submit server form is ' , form);
    let inputHeader = {
        "FORM_HEADER": form
      };

    // Queue the latest form to the server
    this.unviredSDK.logInfo("FormsPage", "submitFormToServer", "Sending form to server in async mode.");
    let submitted = await this.unviredSDK.syncBackground(RequestType.RQST, inputHeader, "", AppConstants.FORMS_PA_FORM_SUBMIT, "FORM", form.LID, AppConstants.BOOL_FALSE,)
    if (submitted.type === ResultType.success) {
      this.store.dispatch(RigActions.loadAllFormsFromDb());
    } else {
      throw new Error("Error while submitting form" + submitted.error);
    }
  }


  async createFormActionForSubmit(form: any) {
    let formAction = new FORM_ACTION();
    formAction.FORM_ID = form.FORM_ID;
    formAction.ACTION_CODE = AppConstants.ACTION_CODE.COMPLETE;
    formAction.P_MODE = 'A';
    formAction.FID = form.LID;
    formAction.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
    return formAction;
  }


  async insertFormActionInDB(formAction: any) {
    let insertResult = await this.unviredSDK.dbInsertOrUpdate(AppConstants.TABLE_FORM_ACTION, formAction, false);
    if (insertResult.type === ResultType.success) {
      console.log('Form action inserted successfully');
    } else {
      throw new Error("Error while inserting form action" + insertResult.error);
    }
  }


  async createSchedulerInput(rigResult: any, template: any) {
    return {
      "FORM_SCHD_ALERT": [
        {
          "FORM_SCHD_ALERT_HEADER": {
            "RIG_NO": rigResult.RIG_NO,
            "COMPANY": rigResult.COMPANY,
            "TMPLT_ID": template.TMPLT_ID
          }
        }
      ]
    }
  }

  /**
   * Clears existing forms state and updates with fresh data from database
   */

  async updateFormsStateFromDB(): Promise<void> {
    try {
      console.log('[DataService] Clearing existing forms state and updating from DB...');
      
      // Step 1: Clear existing forms state
     
     const updatedForms = await this.getAllFormsFromTheDB();
console.log('[DataService] About to dispatch, DB returned:', updatedForms.length);
console.log(
  '[DataService] store object',
  this.store,
  'constructor:', this.store.constructor?.name
);

this.ngZone.run(() => {
  console.log(
  '[DataService] Dispatching action instance:',
  RigActions.loadAllFormsDbSuccess.type
);
const freshForms = updatedForms.map((f: any) => ({ ...f }));

this.store.dispatch(RigActions.loadAllFormsDbSuccess({ forms: freshForms }));

});

      console.log('[DataService] Retrieved forms from DB:', updatedForms);
      console.log('[DataService] Forms count from DB:', updatedForms?.length);
      
      // Step 3: Store forms data directly in the service
     
      // Step 5: Verify the update
   const currentForms = await firstValueFrom(
  this.store.select(selectAllForms).pipe(
    filter((forms: any) => forms.length === updatedForms.length) // wait until count matches
  )
);
      console.log('[DataService] Verification - Forms count in store:', currentForms?.length);
      console.log('[DataService] Forms match:', currentForms?.length === updatedForms?.length);
      
      if (currentForms?.length !== updatedForms?.length) {
        console.log('[DataService] Still mismatch, trying one more time...');
        
        // // Set form to initial state and dispatch loadAllFormsFromDb action
        // this.store.dispatch(RigActions.loadAllFormsDbSuccess({ forms: [] }));
        // this.store.dispatch(RigActions.loadAllFormsFromDb());
        
        // this.store.dispatch(RigActions.loadAllFormsDbSuccess({ forms: updatedForms }));
        
        // const finalForms = await firstValueFrom(this.store.select(selectAllForms));
        // console.log('[DataService] Final verification - Forms count:', finalForms?.length);
      }else{
        this.router.navigate(['/forms']);
      }
      
    } catch (error) {
      console.error('[DataService] Error updating forms state:', error);
    }
  }

  async makePACallToCreateOnlineForm(schedulerInput: any) {
    console.log('schedulerInput in makePACallToCreateOnlineForm is ' , schedulerInput);
    let formCreated = await this.unviredSDK.syncForeground(RequestType.QUERY, null, schedulerInput, AppConstants.PA_FORM_CREATE_AND_COPY, true);
    if (formCreated.type === ResultType.success) {
      console.log('Form scheduler created successfully');
      
      // Update forms state from database

      await this.updateFormsStateFromDB()
      
    } else {
      throw new Error("Error while creating form scheduler" + formCreated.error);
    }
  }

 
  

async  deleteConfirmedFormsAndSTMRsFromDB(deleteFormsArr: any[], deleteStmrArr: any[]) {
  let that = this;

  // Delete items from UI
  // that.removeSelection()

  let allFormIds = deleteFormsArr.map((data) => {
    return "'" + data['FORM_ID'] + "'"
  }).join(',')

  let allStmrIds = deleteStmrArr.map((data) => {
    return "'" + data.STMR_ID + "'"
  }).join(',')

  // Table FORM_HEADER
  if (allFormIds && allFormIds.length) {
    let query = `UPDATE FORM_HEADER SET P_MODE = 'D', FORM_STATUS = 'DEL', OBJECT_STATUS = ${AppConstants.OBJECT_STATUS.DELETE} WHERE FORM_ID IN (${allFormIds})`;
    this.unviredSDK.logInfo("FormsPage", "confirmDeleteForms()", "Deleting Forms: " + query)
    let deleteFormResult = await this.unviredSDK.dbExecuteStatement(query)
    if (deleteFormResult.type === ResultType.success) {
      this.store.dispatch(RigActions.loadAllFormsFromDb());
      await this.createArrayToSubmitFordeleteForms(deleteFormsArr)
    }else if (deleteFormResult.type === ResultType.error) {
      this.unviredSDK.logError("FormsPage", "confirmDeleteForms()", "Deleting Form Header from database failed: " + deleteFormResult.message + " " + deleteFormResult.error)
      console.error("FormsPage", "confirmDeleteForms()", "Deleting Form Header from database failed: " + deleteFormResult.message + " " + deleteFormResult.error)
    }




  }
  if (deleteStmrArr && deleteStmrArr.length) {
    let query = `UPDATE STMR_HEADER SET P_MODE = 'D', STMR_STATUS = 'DEL', OBJECT_STATUS = ${AppConstants.OBJECT_STATUS.DELETE} WHERE STMR_ID IN (${allStmrIds})`;
    this.unviredSDK.logInfo("FormsPage", "confirmDeleteForms()", "Deleting STMRs: " + query)
    let deleteStmrResult = await this.unviredSDK.dbExecuteStatement(query)
    if (deleteStmrResult.type === ResultType.success) {
      this.store.dispatch(RigActions.loadStmrDataFromDb());
      this.store.dispatch(RigActions.loadStmrDataAndTopicsFromDb());
      await this.createArrayToSubmitFordeleteSTMRs(deleteStmrArr)
    }else if (deleteStmrResult.type === ResultType.error) {
      this.unviredSDK.logError("FormsPage", "confirmDeleteForms()", "Deleting STMR Header from database failed: " + deleteStmrResult.message + " " + deleteStmrResult.error)
      console.error("FormsPage", "confirmDeleteForms()", "Deleting STMR Header from database failed: " + deleteStmrResult.message + " " + deleteStmrResult.error)
    }
  }
}


  async createArrayToSubmitFordeleteForms(deleteFormsArr: FORM_HEADER[]) {
    try {
      this.isDeleting = true;  // show busy indicator
      if(this.isDeleting){
        console.log('isDeleting is true');
        this.busyIndicatorService.displayBusyIndicator('Deleting Forms...')
      }
      for (let k = 0, kLen = deleteFormsArr.length; k < kLen; k++) {
        let form = JSON.parse(JSON.stringify(deleteFormsArr[k]));
  
        form.P_MODE = 'D';
        form.OBJECT_STATUS = AppConstants.OBJECT_STATUS.DELETE;
        form.FORM_STATUS = 'DEL';
  
        await this.deleteFormInServer(form);
      }
    } finally {
      this.isDeleting = false;  // always hide, even if error happens

      await this.busyIndicatorService.hideBusyIndicator();
    }
  }

  async createArrayToSubmitFordeleteSTMRs(deleteStmrArr: STMR_HEADER[]) {
    for (let l = 0, lLen = deleteStmrArr.length; l < lLen; l++) {
      let filteredSTMR = deleteStmrArr[l];
      if (filteredSTMR.STMR_ID.startsWith('New') && filteredSTMR.SYNC_STATUS == AppConstants.SYNC_STATUS.NONE) {  // Do not send locally created STMRs (not synced with the server) for deletion.
        continue;
      }
      let stmr = JSON.parse(JSON.stringify(filteredSTMR));
      stmr.P_MODE = 'D'
      delete stmr.stmrStatus
      delete stmr.syncStatus
      stmr.OBJECT_STATUS = AppConstants.OBJECT_STATUS.DELETE
      stmr.STMR_STATUS = 'DEL'
      this.deleteSTMRInServer(stmr);
    }
  }

  async deleteFormInServer(form: FORM_HEADER) {

    let formHeaderForDeletion = new FORM_HEADER()
    formHeaderForDeletion.LID = form.LID
    formHeaderForDeletion.P_MODE = form.P_MODE
    formHeaderForDeletion.FORM_ID = form.FORM_ID

    // Submit STMR
    let inputHeader = {
      "FORM_HEADER": formHeaderForDeletion
    }
    this.unviredSDK.logInfo("Forms", "deleteFORMInServer", "Sending Form to server in async mode");
    let deleteFormResult = await this.unviredSDK.syncBackground(RequestType.RQST, inputHeader, "", AppConstants.FORMS_PA_FORM_SUBMIT, "FORM", form.LID, false)
    if (deleteFormResult.type === ResultType.success) {
      this.unviredSDK.logInfo("Forms", "deleteFORMInServer", "Succesfully sent the Form with MODE 'D's to server");
    } else{
      this.unviredSDK.logError("Forms", "deleteFORMInServer", "Failed to delete Form in server");
    }

  }


  async deleteSTMRInServer(stmr: STMR_HEADER) {

    // Submit STMR
    let inputHeader = {
      "STMR_HEADER": stmr
    }
    if (1) {
      let deleteSTMRResult = await this.unviredSDK.syncBackground(RequestType.RQST, inputHeader, "", AppConstants.PA_FORMS_STMR_SUBMIT, "STMR", stmr.LID, false)
      if (deleteSTMRResult.type === ResultType.success) {
        this.unviredSDK.logInfo("Forms", "deleteSTMRInServer", "Succesfully sent the STMR with MODE 'D's to server");
      } else{
        this.unviredSDK.logError("Forms", "deleteSTMRInServer", "Failed to delete STMR in server");
      }
     
    }
  }


  async getAllFormsFromServer(rig: any): Promise<any>{
  //  this.inputDataForCustomization(this.rigResult);
  console.log('rig is ' , rig);
  // let input  ={
  //   "INPUT_RIG_CONTEXT": [
  //     {
  //       "INPUT_RIG_CONTEXT_HEADER": {
  //         "RIG_NO": "can701o",
  //         "RIG_TYPE": "SHOP",
  //         "RIG_SUB_TYPE": "SHOP_OFFICE",
  //         "COMP_CODE": "1000",
  //         "DEVICE_NAME": "03002a0f-0300-3e85-2b62-0005ed180003"
  //       }
  //     }
  //   ]
  // }

  let inputData = {
    "INPUT_RIG_CONTEXT": [
      {
        "INPUT_RIG_CONTEXT_HEADER": {
          "RIG_NO": rig.RIG_NO,
          "RIG_TYPE": rig.RIG_TYPE,
          "RIG_SUB_TYPE": rig.RIG_SUB_TYPE,
          "COMP_CODE": rig.COMP_CODE,
          "DEVICE_NAME": this.deviceId
        }
      }
    ]
  }
  // console.log('input is ' , input);
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.PULL,
        '',
        inputData,
        AppConstants.FORMS_PA_HISTORY_GET,
        true
      );
      if (result.type == ResultType.success) {
        // this.store.dispatch(RigActions.loadAllFormsFromDb());
        // this.store.dispatch(RigActions.loadStmrDataFromDb());
        this.unviredSDK.logInfo(
          'DataService',
          'getAllFormsFromServer',
          'Form Data has downloaded successfully.'
        );
        this.store.dispatch(RigActions.loadAllFormsFromDb());
      } else if (result.code && result.code === 401) {
        this.unviredSDK.logError(
          'DataService',
          'getAllFormsFromServer',
          'Unauthorized. Please login again.'
        );
      }

  }catch (error) {
    this.unviredSDK.logError(
      'DataService',
      'getAllFormsFromServer',
      'Error while downloading form data: ' + JSON.stringify(error)
    );
  }

}



async saveActualRigDocPath(path: string): Promise<void> {
   if (this.isPathSaved) {
    console.log('Path already saved, skipping...');
    return;
  }
  const name = 'ACTUAL_RIG_DOC_PATH';
  const now = new Date().toISOString();
  
  try {
    // Check if record exists first
    const result = await this.unviredSDK.dbSelect(
      AppConstants.TABLE_SETTING_HEADER, 
      `NAME = '${name}'`
    );
    
    let lid: string;
    
    if (result.data.length > 0) {
      // Record exists - reuse existing LID
      lid = result.data[0].LID;
      console.log('Record exists - reusing LID:', lid);
    } else {
      // No record exists - generate new LID
      lid = this.utilityService.guid32();
    }
    
    await this.unviredSDK.dbInsertOrUpdate(
      AppConstants.TABLE_SETTING_HEADER,
      {
        LID: lid,           // Primary key
        NAME: name,         // Unique constraint (handled properly now)
        VALUE: path,        // Your data
        TIMESTAMP: now,     // Current timestamp
      },
      true
    );
    this.isPathSaved = true; 
  } catch (err) {
    this.unviredSDK.logError('DataService', 'saveActualRigDocPath', 'Error: ' + err);
    throw err;
  }
}

async getSpConfigFromDB(): Promise<any[]> {
  try {
    const query = `SELECT * FROM ${AppConstants.TABLE_SP_CONFIG_HEADER}`;
    const result = await this.unviredSDK.dbExecuteStatement(query);
    if (result?.data?.length > 0) {
      console.log('[DataService] SP config loaded from DB:', result.data);
      return result.data;
    } else {
      console.warn('[DataService] No SP config found in DB');
      return [];
    }
  } catch (error) {
    console.error('[DataService] Error loading SP config:', error);
    return [];
  }
}

  async processSpConfigAndSaveToSettings(): Promise<void> {
  console.log('[DataService] processSpConfigAndSaveToSettings - METHOD CALLED');
  try {
    const spConfigData = await this.getSpConfigFromDB();
    const spLocalRoot = spConfigData.find(config => config.NAME === 'SP_LOCAL_ROOT');
    
    if (spLocalRoot && spLocalRoot.VALUE) {
      console.log('[DataService] Processing SP_LOCAL_ROOT value:', spLocalRoot.VALUE);
      let configPath = spLocalRoot.VALUE;
      let finalPath = '';
      
      if (this.attachmentHelper.isElectron()) {
        finalPath = this.attachmentHelper.normalizeLocalPath(configPath);
      } else {
        const basePath = this.attachmentHelper.getBasePath();
        const normalizedConfigPath = this.attachmentHelper.normalizeLocalPath(configPath);
        finalPath = basePath + normalizedConfigPath;
      }
      
      finalPath = this.attachmentHelper.sanitizePathPreserveFileScheme(finalPath);
      this.unviredSDK.logDebug('formsPage', 'processSpConfigAndSaveToSettings', `Final path after sanitization:', ${finalPath}`);
      
      await this.saveActualRigDocPath(finalPath);
      this.unviredSDK.logDebug('formsPage', 'processSpConfigAndSaveToSettings', ` SP_LOCAL_ROOT processed and saved successfully:, ${finalPath}`);
      console.log('SP_LOCAL_ROOT processed and saved successfully:', finalPath);
    } else {
      console.log(' No SP_LOCAL_ROOT found or no VALUE');
    }
  } catch (error) {
    this.unviredSDK.logError('formsPage', 'processSpConfigAndSaveToSettings', ` Error processing SP_LOCAL_ROOT: ${error}`);
    console.log(' Error processing SP config:', error);
  }
}

async getTemplateDownloadedFromServer(form: FORM_HEADER): Promise<void> {
 // No Attachment exists.
          // Let's download the Template based on the version id
          try {
            this.busyIndicatorService.displayBusyIndicator("Downloading template. Please wait...");
          let customObj = {
            "INPUT_TMPLT_CONTEXT": [
              {
                "INPUT_TMPLT_CONTEXT_HEADER": {
                  "VER_ID": form.VER_ID
                }
              }
            ]
          }

           console.log('Downloading template from server' , customObj);
         let tempDownloadResult = await this.unviredSDK.syncForeground(ump.requestType.RQST, null, customObj, AppConstants.FORMS_PA_TEMPLATE_DOWNLOAD, true)
          console.log('tempDownloadResult is ' , tempDownloadResult);
          if(tempDownloadResult.type === ResultType.success){
            this.unviredSDK.logInfo('FormsPage','getTemplateDownloadedFromServer','Template downloaded successfully');
            this.busyIndicatorService.hideBusyIndicator();
            return tempDownloadResult.data.TMPLT
          }
          } catch (error) {
            this.unviredSDK.logError('FormsPage','getTemplateDownloadedFromServer','Error downloading template: ' + error);
            await this.busyIndicatorService.hideBusyIndicator();
            throw error;
          
          }
}

async getTemplateAttachmentFromDB(form: FORM_HEADER): Promise<TMPLT_ATTACHMENT> {
  try {
    const query = `SELECT * FROM ${AppConstants.TABLE_TMPLT_ATTACHMENT} WHERE TAG1 = '${form.VER_ID}'`;
    const result = await this.unviredSDK.dbExecuteStatement(query);
    if (result?.data?.length > 0) {
      console.log('[DataService] Template attachment loaded from DB:', result.data);
      return result.data[0];
    } else {
      console.warn('[DataService] No Template attachment found in DB');
      return new TMPLT_ATTACHMENT();
    }
  } catch (error) {
    console.error('[DataService] Error loading Template attachment from DB:', error);
    return new TMPLT_ATTACHMENT();
  }
}
async getRigManagerFromDb(rigData: RIG_HEADER): Promise<RIG_MANAGER[]> {
  try {
    const query = `SELECT * FROM ${AppConstants.TABLE_RIG_MANAGER} WHERE COMP_CODE = '${rigData.COMP_CODE}'`;
    const result = await this.unviredSDK.dbExecuteStatement(query);
    if (result?.data?.length > 0) {
      console.log('[DataService] Rig Manager loaded from DB:', result.data);
      this.unviredSDK.logInfo('DataService', 'getRigManagerFromDb', 'Rig Manager loaded from DB');
      return result.data;
    } else {
      console.warn('[DataService] No Rig Manager found in DB');
      this.unviredSDK.logInfo('DataService', 'getRigManagerFromDb', 'No Rig Manager found in DB');
      return [];
    }
  } catch (error) {
    console.error('[DataService] Error loading Rig Manager:', error);
    this.unviredSDK.logError('DataService', 'getRigManagerFromDb', 'Error loading Rig Manager: ' + error);
    return [];
  }   
  }
  
 
async getReleaseNotesFromDB(): Promise<RELEASE_NOTE_HEADER[]> {
   console.log('[DataService] getReleaseNotesFromDB - Starting DB query');
   this.unviredSDK.logInfo('DataService','getReleaseNotesFromDB','Starting Db query');
  try {
    const rigInfo: any = await this.unviredSDK.dbSelect(AppConstants.TABLE_RIG_HEADER, {});
    const rig = rigInfo?.data?.[0];
    if (!rig){ 
      this.unviredSDK.logDebug('DataService','getReleaseNotesFromDB',' No rig data found')
      return [];
    }

    const subquery = `SELECT TH.TMPLT_ID FROM TMPLT_HEADER AS TH, TMPLT_VER AS TV, TMPLT_ATTACHMENT AS TA, TMPLT_ASSGN AS TA WHERE TV.STATUS = 'REL' AND TH.IS_ACTIVE = 'true' AND TH.TMPLT_ID = TV.TMPLT_ID AND TA.TAG1 = TV.VER_ID AND TA.TMPLT_ID = TH.TMPLT_ID AND TA.TMPLT_ID IN (SELECT TMPLT_ID FROM TMPLT_ASSGN WHERE ASSGN_TYPE = '${rig.RIG_TYPE}' and VAL1 = '${rig.RIG_SUB_TYPE}') AND TA.TMPLT_ID NOT IN (SELECT TMPLT_ID FROM TMPLT_ASSGN WHERE ASSGN_TYPE = 'COMPANY' AND VAL1 != ${rig.COMP_CODE} EXCEPT SELECT TMPLT_ID FROM TMPLT_ASSGN WHERE ASSGN_TYPE = 'COMPANY' AND VAL1 = ${rig.COMP_CODE}) GROUP BY TH.TMPLT_ID`;

    const query = `SELECT RNH.*, TH.DESCR AS TMPLT_NAME, TH.IS_DATA_ENH AS IS_DATA_ENH FROM RELEASE_NOTE_HEADER as RNH LEFT JOIN TMPLT_HEADER as TH ON TH.TMPLT_ID = RNH.TMPLT_ID where RNH.DESCRIPTION is not null and RNH.DESCRIPTION !='' AND CASE WHEN NOTE_TYPE = 'TMPLT' THEN TH.TMPLT_ID IN (${subquery}) ELSE 1 END ORDER BY CHGD_ON DESC`;

    const result = await this.unviredSDK.dbExecuteStatement(query);
    console.log('[DataService] getReleaseNotesFromDB - Query result count:', result?.data?.length || 0);
    this.unviredSDK.logDebug('DataService','getReleaseNotesFromDB',`Query result count:, ${result?.data?.length || 0}`)
    return result?.data || [];
  } catch (error) {
    this.unviredSDK.logError('DataService', 'getReleaseNotesFromDB', `Error: ${error}`);
    throw error;
  }
}

  async createStmrFormHeader(Tmplt: TEMPLATE_HEADER, topicEntity: STMR_TOPIC_ENTITY) {

    const prefilledData = await firstValueFrom(this.store.select(selectPrefilledData).pipe(take(1)));
    let stmrFormHeader: STMR_FORM = <STMR_FORM>{};
    stmrFormHeader.LID = this.utilityService.guid32();
    stmrFormHeader.STMR_ID = topicEntity.topic.STMR_ID;
    stmrFormHeader.FID = topicEntity.topic.FID;
    stmrFormHeader.TOPIC_NO = topicEntity.topic.TOPIC_NO;
    stmrFormHeader.FORM_ID = 'New' + this.utilityService.guid32();
    stmrFormHeader.VER_ID = Tmplt.L_VER_ID;
    stmrFormHeader.CRTD_BY = prefilledData?.USER_ID || '';
    stmrFormHeader.CRTD_ON = moment.utc().unix();
    stmrFormHeader.SUBM_BY = prefilledData?.USER_ID || '';
    stmrFormHeader.DATE_COMP = moment.utc().unix();
    stmrFormHeader.COMPANY = prefilledData?.COMP_CODE || '';
    stmrFormHeader.RIG_NO = prefilledData?.RIG_NO || '';
    stmrFormHeader.COMMENTS = "";
    stmrFormHeader.FORM_STATUS = AppConstants.VAL_FORM_STATUS.OPEN;
    stmrFormHeader.NAME = Tmplt.NAME;
    stmrFormHeader.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
    stmrFormHeader.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
    stmrFormHeader.P_MODE = "A";
    stmrFormHeader.LAST_SYNC_USER = prefilledData?.USER_ID || '';
    stmrFormHeader.SUBM_BY = prefilledData?.USER_ID || '';
    return stmrFormHeader;
  }

  async createFormFromTopicAndSaveToDB(tmpltHeader: TEMPLATE_HEADER, topicEntity: STMR_TOPIC_ENTITY) : Promise<STMR_FORM> {
  let stmrformHeader = await this.createStmrFormHeader(tmpltHeader, topicEntity);
  // Create a copy without the FID field for database insertion
  console.log('stmrformHeader in createFormFromTopicAndSaveToDB is ' , stmrformHeader);
  let formCreated = await this.unviredSDK.dbInsert(AppConstants.TABLE_STMR_FORM, stmrformHeader, AppConstants.BOOL_TRUE);
  console.log('formCreated in createFormFromTopicAndSaveToDB is ' , formCreated);
  if(formCreated.type === ResultType.success){
    // this.store.dispatch(RigActions.l());
    return formCreated.data;
  }else{
    throw new Error("Error while creating form" + formCreated.error);
  }
}

  async getSTMRFormFromDb(stmrHeader: STMR_HEADER): Promise<any> {

     let stmrFormData = await this.unviredSDK.dbSelect(AppConstants.TABLE_STMR_FORM ,`STMR_ID = ${stmrHeader.STMR_ID}`);
  if(stmrFormData.data && stmrFormData.data.length > 0){
    let formPresent =  stmrFormData.data
    console.log('formPresent is' , formPresent)

  }else{
    console.log('form not present');
  
  }
}


 












 
 }

