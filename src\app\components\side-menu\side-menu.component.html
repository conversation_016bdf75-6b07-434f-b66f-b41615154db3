<ion-menu contentId="main-content" class="custom-menu" side="start" type="push">
  <ion-header>
    <ion-toolbar>
      <ion-title>Menu</ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-content class="custom-menu">
    <ng-container *ngIf="prefilledData$ | async as data">
      <ion-card style="margin-left: 0px; margin-right: 0px; margin-top: 0px; border-radius: 0px;">
        <ion-card-content>
          <img *ngIf="data.LOGO" [src]="data.LOGO" class="logo-container" alt="Logo" />
        </ion-card-content>
      </ion-card>
      <!-- <ion-card> -->
        <!-- <ion-card-content class="logo-container"> -->
        <!-- </ion-card-content> -->

        <!-- <ion-card-header> -->
          <div style="display: flex; flex-direction: column; align-items: center; justify-content: center;">
            <div (click)="openSiteNumberPopup()" *ngIf="rigData$ | async as rig">
              <ion-label style="color:whitesmoke; cursor: pointer; font-size: smaller;">
                Site: {{ rig.RIG_NO }}
              </ion-label>
            </div>

            <ion-card-title style="color: white; text-align: center; font-weight: bold; font-size: large;">
              Welcome, {{ data.USER_NAME }}
            </ion-card-title>

               <p style="color:whitesmoke; cursor: pointer; font-size: smaller; font-weight: bold;">&nbsp; {{dataService.selectedServer}}&nbsp;</p>
            <p style="color:whitesmoke; cursor: pointer; font-size: small; margin-top: 0px;">&nbsp;{{'App Version'}}: {{version}}&nbsp;</p>
          </div>
        <!-- </ion-card-header> -->
      <!-- </ion-card> -->
    </ng-container>

    <ion-list style="background-color: primary;">
      <ion-menu-toggle>
        <ion-item (click)="navigateTo('/forms')" class="custom-menu" lines="none">Forms ({{ (forms$ | async)?.length || 0 }})</ion-item>
      </ion-menu-toggle>

      <ion-menu-toggle>
        <ion-item (click)="navigateTo('/templates')" class="custom-menu" lines="none">
      Templates ({{ (templates$ | async)?.length || 0 }})
    </ion-item>
      </ion-menu-toggle>

      <ion-menu-toggle>
        <ion-item (click)="navigateTo('/release-notes')" class="custom-menu" lines="none">Release notes ({{ totalReleaseNotes }})</ion-item>
      </ion-menu-toggle>

      <ion-menu-toggle>
        <ion-item (click)="navigateTo('/inbox')" class="custom-menu" lines="none">Inbox</ion-item>
      </ion-menu-toggle>

      <ion-menu-toggle>
        <ion-item (click)="navigateTo('/settings')" class="custom-menu" lines="none">Settings</ion-item>
      </ion-menu-toggle>

      <ion-menu-toggle>
        <ion-item class="custom-menu" lines="none" (click)="openReports()">Reports</ion-item>
      </ion-menu-toggle>
    </ion-list>
  </ion-content>
</ion-menu>
