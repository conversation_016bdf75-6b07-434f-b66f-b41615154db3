// Modern themed checkbox styling (like your reference image)
ion-checkbox.checkbox-themed {
  --size: 22px;
  --checkbox-background: white;
  --checkbox-background-checked: var(--app-primary-green);
  --border-color: #d0d0d0;
  --border-color-checked: var(--app-primary-green);
  --border-width: 2px;
  --border-style: solid;
  --border-radius: 6px;
  --checkmark-color: white;
  --checkmark-width: 3px;
  margin-right: 16px;
  opacity: 1 !important;
  visibility: visible !important;
}

ion-checkbox.checkbox-themed::part(container) {
  border: 2px solid #d0d0d0;
  border-radius: 6px;
  width: 22px;
  height: 22px;
  background: white;
  display: block !important;
  transition: all 0.2s ease;
  box-shadow: none; /* remove the drop shadow */
}

ion-checkbox.checkbox-themed::part(mark) {
  stroke: white;
  stroke-width: 3px;
  stroke-linecap: round;
  stroke-linejoin: round;
}

ion-checkbox.checkbox-themed[aria-checked="true"]::part(container) {
  background: var(--app-primary-green) !important;
  border-color: var(--app-primary-green) !important;
}

ion-toolbar.search-container {
  --background: var(--app-primary); /* green container */
}

// Search toolbar styling - normal colors, properly aligned
.search-toolbar {
  --background: white;
  --color: #333;
  --placeholder-color: #999;
  --icon-color: #666;
  --clear-button-color: #666;
  --border-radius: 8px;
  margin: 8px 12px ;
}

// Ensure toolbar has proper background
ion-toolbar {
  --background: var(--app-primary);
  --color: white;
}

// Item styling with light green selection
ion-item {
  --background: white;
  --color: black;
  --border-color: transparent;
  --inner-padding: 16px;
  --min-height: 56px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 1px;
}

ion-item:hover {
  --background: #f8f9fa;
}

// Light green selection background
ion-item.item-selected {
  --background: rgba(76, 175, 80, 0.1) !important; // Light version of green
}

// Error state styling
ion-item.has-error {
  ion-label {
    color: rgb(194, 4, 4) !important;
  }
  
  ion-checkbox.checkbox-themed::part(container) {
    border-color: rgb(194, 4, 4) !important;
  }
}

// Label styling
ion-label {
  color: #333;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.4;
}

// List styling
ion-list {
  background: white;
  padding: 0;
}

// Content styling
ion-content {
  --background: #f8f9fa;
}

// Contrast theme support
ion-content.contrast {
  --background: black;
  
  ion-item {
    --background: black;
    --color: white;
  }
  
  ion-label {
    color: white;
  }
  
}
