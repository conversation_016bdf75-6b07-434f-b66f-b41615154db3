import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';
import { ExternalLinkService } from './external-link.service';
import { AlertService } from './alert.service';
import { File } from '@awesome-cordova-plugins/file/ngx';
import * as moment from 'moment-timezone';
import { UtilityService } from './utility.service';
import { UnviredCordovaSDK, ResultType, RequestType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AppConstants } from '../constants/appConstants';

declare var cordova: any;

@Injectable({
  providedIn: 'root',
})
export class PrintSTMRFormService {
  private stmrId: string = '';
  private stmrObj: any;
  private readonly OFFLINE_MSG = 'Please connect to the PD Network for printing.';

  constructor(
    private file: File,
    private platform: Platform,
    private externalLink: ExternalLinkService,
    private alertService: AlertService,
    private unviredSdk: UnviredCordovaSDK,
    private utilityService: UtilityService
  ) {}

  private formatDateTime(timestamp: any, timezone: string): string {
    if (!timestamp || !timezone) return timestamp;
    const ts = Number(timestamp);
    if (ts === 0) return timestamp;
    return moment.unix(ts).tz(timezone).format('MMM DD YYYY HH:mm z') + ' (24Hrs)';
  }

  private formatStmrObject(stmrObj: any): any {
    const tz = stmrObj.stmrHeader?.TIME_ZONE;
    if (!tz) return stmrObj;

    const header = stmrObj.stmrHeader;
    header.CRTD_ON_FORMATTED = this.formatDateTime(header.CRTD_ON, tz);
    header.SHIFT_TIME_FORMATTED = this.formatDateTime(header.SHIFT_TIME, tz);
    header.LAST_SYNC_TIME_FORMATTED = this.formatDateTime(header.LAST_SYNC_TIME, tz);
    header.DATE_COMP_FORMATTED = this.formatDateTime(header.DATE_COMP, tz);

    stmrObj.stmrTopic = (stmrObj.stmrTopic || []).map((topic: any) => ({
      ...topic,
      TOPIC_START_FORMATTED: this.formatDateTime(topic.TOPIC_START, tz),
    }));

    return stmrObj;
  }

  
  // NEW: iOS path – get URL from server and open with InAppBrowser via ExternalLinkService
  private async printSTMRViaURL(stmrId: string): Promise<void> {
    try {
    // Get the URL via the shared utility method
    const url = await this.utilityService.getFormDisplayUrl(stmrId);
    console.log('URL is ' + url); // [1]
    this.externalLink.openFormtoPrint(url); // InAppBrowser on iOS,
  } catch (error: any) {
    this.printError(error?.message || String(error)); 
    throw error; 
  }
  }

  // UPDATED: single entry point with platform branching
  public async printSTMRForm(stmrId: string, stmrObj: any): Promise<void> {
    this.stmrId = stmrId || stmrObj?.stmrHeader?.STMR_ID;
    const isElectron = cordova.platformId == 'electron';
    const isIOS = cordova.platformId == 'ios';

    if (isElectron) {
      // Preserve existing Electron flow (unzip + open local HTML)
      this.stmrObj = this.formatStmrObject(stmrObj);
      // 1) Query attachment with FILE_NAME
      const result = await this.unviredSdk.dbExecuteStatement(
        "SELECT FILE_NAME, ATTACHMENT_STATUS FROM TMPLT_ATTACHMENT WHERE TAG1 = 'STMR.STMR' AND TAG2 = 'STMR'"
      );
      if (result.type !== ResultType.success || !result.data.length) {
        this.printError('Attachment not found.');
        return;
      }
      const attachment = result.data[0];
      if (attachment.ATTACHMENT_STATUS !== 'DOWNLOADED' && attachment.ATTACHMENT_STATUS !== 2) {
        this.alertService.showAlert('Info', 'Downloading Attachment, please wait...');
        return;
      }
      const basePath = await this.unviredSdk.getAttachmentFolderPath();
      const zipFilePath = `${basePath}/${attachment.FILE_NAME}`.replace(/\\/g, '/');
      const folderName = `Print_STMR_${this.stmrId}`;
      await this.prepareAndOpenPrintForm(basePath, zipFilePath, folderName);
      return;
    }

    if (isIOS) {
      // New iOS path: request URL and open with InAppBrowser
      await this.printSTMRViaURL(this.stmrId);
      return;
    }

    // Unsupported platform
    this.printError('Printing is only supported on Electron (Windows) and iOS.');
  }

  private async prepareAndOpenPrintForm(basePath: string, zipFilePath: string, folderName: string): Promise<void> {
    try {
      try {
        // Clean up old folder if exists
        await this.file.checkDir(basePath, folderName);
        await this.file.removeRecursively(basePath, folderName);
      } catch {
        // Ignore if folder doesn't exist
      }

      // Unzip archive and write files
      await this.unzipUsingUtility(basePath, zipFilePath, folderName);

      // Update HTML and open in external browser (Electron or iOS)
      await this.updateHtmlAndOpen(basePath, folderName);
    } catch (err) {
      this.printError('Failed to prepare and open print form: ' + err);
    }
  }

  private async unzipUsingUtility(basePath: string, zipFilePath: string, folderName: string): Promise<void> {
    try {
      // Get zip file as ArrayBuffer from utility service
      const buffer = await this.utilityService.getAttachmentAsArrayBuffer(zipFilePath);

      // Unzip and write file content using utility service
      await this.utilityService.unzipTheFileAndWriteFileContent(buffer, folderName, basePath);
    } catch (error) {
      this.printError(`Unzip failed: ${error}`);
      throw error;
    }
  }

  private async updateHtmlAndOpen(basePath: string, folderName: string): Promise<void> {
    const htmlFile = 'index.html';
    const folderPath = `${basePath.endsWith('/') ? basePath : `${basePath}/`}${folderName}`.replace(/\\/g, '/');


    try {
      // Read existing HTML file
      const content = await this.file.readAsText(folderPath, htmlFile);

      // Fetch company logo from DB
      const query = `
        SELECT C.* FROM 
          (SELECT COMP_CODE FROM RIG_HEADER) AS R 
          LEFT JOIN 
          (SELECT * FROM COMPANY_HEADER) AS C 
          ON R.COMP_CODE = C.CODE
      `;

      const result = await this.unviredSdk.dbExecuteStatement(query);

      if (result.type !== ResultType.success || !result.data?.length) {
        this.printError('Company logo not found.');
        return;
      }

      const logo = result.data[0].LOGO || '';

      // Inject form data, logo, and print flag into HTML
      const formScript = `
        var formData = ${JSON.stringify(this.stmrObj)};
        var companyLogo = '${logo}';
        var shouldPrint = true;
      `;

      const updatedHtml = content.replace(
        '<!-- FORM DATA -->',
        `<!-- FORM DATA -->\n<script>${formScript}</script>\n<!-- ./FORM DATA -->`
      );

      // Write updated HTML back to file
      await this.file.writeFile(folderPath, htmlFile, updatedHtml, { replace: true });

      // Resolve platform-specific file URL and open in external browser
      let finalPath = (folderPath.endsWith('/') ? folderPath : (folderPath + '/')) + htmlFile;

      console.log('Base path:', basePath)
      console.log('Folder path:', folderPath)
      console.log('HTML path:', htmlFile)
      console.log('Opening file at path:', finalPath);
      this.externalLink.openFormtoPrint(finalPath);
    } catch (err: any) {
      this.printError('Failed to update HTML: ' + (err?.message || JSON.stringify(err)));
    }
  }

  private printError(msg: string) {
  const raw = (msg || '').toString();
  const isOffline =
    !navigator.onLine || /internet.*not.*connected|network|offline/i.test(raw); // reuse existing detection [2]
  this.alertService.showAlert('Print Failed', isOffline ? this.OFFLINE_MSG : raw); // unified message [1]
}
}

