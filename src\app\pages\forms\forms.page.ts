import { Component, NgZone, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NotifResult, RequestType, ResultType, UnviredCordovaSDK, UnviredCredential } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { filter, Observable, switchMap, take, tap, map, Subscription } from 'rxjs';
import { Store } from '@ngrx/store';
import { selectAllForms, selectAllNotifications, selectAllReleaseNotes, selectAllTemplates, selectFilteredForms, selectFilteredStmrWithTopics, selectPrefilledData, selectProgressPercentage, selectRigData, selectRigLoadedFromDb, selectSearchQuery, selectSTMRWithTopics } from 'src/app/store/store.selector';
import { TEMPLATE_HEADER } from 'src/models/TEMPLATE_HEADER';
import * as RigActions from 'src/app/store/store.actions';
import { AlertController, IonButton, IonButtons, IonCheckbox, IonCol, IonContent, IonGrid, IonHeader, IonItem,IonBadge, IonIcon, IonItemDivider, IonLabel, IonList, IonMenuButton, IonRouterOutlet, IonRow, IonSearchbar, IonThumbnail, IonTitle, IonToggle, IonToolbar, MenuController, ModalController, Platform, IonProgressBar } from '@ionic/angular/standalone';
import { DataService } from 'src/app/services/data.service';
import { FORM_HEADER } from 'src/models/FORM_HEADER';
import { CustomDatePipe } from 'src/app/pipes/custom-date.pipe';
import { TranslateModule } from '@ngx-translate/core';
import { BehaviorSubject } from 'rxjs';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { UtilityService } from 'src/app/services/utility.service';
import {  SafeResourceUrl } from '@angular/platform-browser';
import { AppConstants } from 'src/app/constants/appConstants';
import { BusyIndicatorService } from 'src/app/services/busy-indicator.service';
import { FORM_DATA } from 'src/models/FORM_DATA';
import moment from 'moment';
import { FormListDisplayService} from 'src/app/services/form-list-display.service'
import { STMR_HEADER } from 'src/models/STMR_HEADER';
import { Router } from '@angular/router';
import { STMRDetailsPage } from '../stmr-details/stmr-details.page';
import { AttachmentHelper } from 'src/app/helpers/attachment-helper';
import { ReleaseNotesService } from 'src/app/services/release-notes.service';
import { addIcons } from 'ionicons';
import { chatboxOutline } from 'ionicons/icons';
import { NotificationService } from 'src/app/notification.service';
import { JwtService } from 'src/app/services/jwt.service';

addIcons({ 'chatbox-outline': chatboxOutline,
 });
declare var ump: any;
declare var cordova: any;


@Component({ 
  selector: 'app-forms',
  templateUrl: './forms.page.html',
  styleUrls: ['./forms.page.scss'],
  standalone: true,
  imports: [IonRow, IonCheckbox, IonProgressBar, IonCol, IonGrid,  IonItemDivider, IonThumbnail, IonSearchbar, IonToggle, TranslateModule, CustomDatePipe, CommonModule, FormsModule ,IonItem ,IonList,IonToolbar , IonLabel,  IonHeader, IonButtons ,IonMenuButton ,IonContent ,IonTitle , IonButton,IonIcon, IonBadge]
})
export class FormsPage implements OnInit {
  notifications$: Observable<NotifResult[]> | undefined;
  templates$: Observable<TEMPLATE_HEADER[]|null> | undefined;
  prefilledData$!: Observable<any>;
  forms$!: Observable<FORM_HEADER[]>;
stmrData$!: Observable<STMR_HEADER[] | null>;
stmrWithTopics$!: Observable<any>;
  groupedForms: { category: string, items: FORM_HEADER[] }[] = [];
  plainList: FORM_HEADER[] = [];
  latestFilteredForms: FORM_HEADER[] = [];
  filteredStmrWithTopics : any= [];
  categoryToggle: boolean = false; // now a simple boolean
  isTemplateLoadTriggered = false;
  searchTermSubject = new BehaviorSubject<string>('');
  filteredForms$: Observable<FORM_HEADER[]> | undefined;
  hideCompletedToggle: boolean = false;
  trustedIndexUrl!: SafeResourceUrl;
  lidOfLastOpenedForm: string = '';
  selectionMode: boolean = false;
  selectedForms: FORM_HEADER[] = [];
  selectedSTMRs: STMR_HEADER[] = [];
  spConfig$!: Observable<any[]>;
  newReleaseNoteAvailable: number = 0;
  newInboxItemAvailable: number = 0;
  gridVisible: boolean = false;
  private releaseNotesSub?: Subscription;
  private inboxSub?: Subscription;
  private spConfigProcessed = false;
  stmrWithTopicsFiltered$!: Observable<any>;
  searchQuery$ = this.store.select(selectSearchQuery);
  formErrorMessage='';
  stmrErrorMessage='';
  isElectron: boolean = false;
  isIOS: boolean = false;

  constructor(
    private unviredSdk: UnviredCordovaSDK,
    private store: Store,
    private routerOutlet: IonRouterOutlet,
    private menuCtrl: MenuController,
    public dataService: DataService,
    private file: File,
    private utilityService: UtilityService,
    public platform: Platform,
    private busyIndicatorService: BusyIndicatorService,
    private router: Router,
    public formsDisplayService: FormListDisplayService,
    private alertController: AlertController,
    private modalController: ModalController,
    public notificationService: NotificationService,
    private attachmentHelper: AttachmentHelper,
    private releaseNotesService: ReleaseNotesService,
    private jwtService: JwtService
  ) {
    this.routerOutlet.swipeGesture = false;
    this.menuCtrl.swipeGesture(true)
    this.formsDisplayService.formSelected = false;
    this.isElectron = cordova.platformId == 'electron';
    this.isIOS = cordova.platformId == 'ios';


  }

  ngOnInit() {

    this.jwtService.setupForegroundCheck();

    this.store.select(selectAllReleaseNotes).subscribe(releaseNotes => {
      if (releaseNotes) {
        this.newReleaseNoteAvailable = releaseNotes.filter(note => 
          !note.IS_READ || note.IS_READ.toLowerCase().trim() !== 'true'
        ).length;
      }
    });
    this.inboxSub = this.releaseNotesService.inboxCount$.subscribe(count => {
      this.newInboxItemAvailable = count;
    });

    this.stmrWithTopics$ = this.store.select(selectSTMRWithTopics);


    this.stmrWithTopicsFiltered$ = this.store.select(selectFilteredStmrWithTopics);


    this.stmrWithTopicsFiltered$.subscribe(stmrs => {
      this.filteredStmrWithTopics = this.applyCompletedFilterForSTMRs(stmrs);
    });
    this.unviredSdk.userSettings().then((result: any) => {
      const data = result.data || {};
      this.dataService.selectedServer = data.SERVER_TYPE
    });


    this.forms$ = this.store.select(selectAllForms); // raw list if needed
    this.filteredForms$ = this.store.select(selectFilteredForms); // base (search applied)

    this.filteredForms$.subscribe(filtered => {
      this.latestFilteredForms = filtered;   // keep latest search-filtered list
      this.applyCompletedFilterForForms();              // apply hideCompleted + grouping
    });

    console.log('[Store-Fixes] ngOnInit - FormsPage initialized');
    this.initialPrefilledDataLoad()
  }

  

  

  updateGroupedForms() {
    if (this.categoryToggle) {
      const groups: { [key: string]: FORM_HEADER[] } = {};
      this.plainList.forEach(form => {
        const cat = form.CATEGORY_DESC ? form.CATEGORY_DESC : '';
        if (!groups[cat]) {
          groups[cat] = [];
        }
        groups[cat].push(form);
      });
      this.groupedForms = Object.entries(groups).map(([category, items]) => ({ category, items }));
    } else {
      this.groupedForms = [{ category: 'Forms', items: this.plainList }];
    }
  }

  async getForms(){
    this.store.select(selectRigData).subscribe(async (rigData) => {
      console.log('rigData is ' , rigData);
     await this.dataService.getAllFormsFromServer(rigData);
  
        this.store.dispatch(RigActions.loadAllFormsFromDb());
        this.store.dispatch(RigActions.loadStmrDataFromDb());
        this.store.dispatch(RigActions.loadStmrDataAndTopicsFromDb());
        this.forms$ = this.store.select(selectAllForms)
        this.forms$.subscribe((forms: FORM_HEADER[]) => {
         this.plainList = forms;
         console.log('this.forms is ' , forms)
         this.updateGroupedForms();
       })
       this.stmrWithTopics$.subscribe((stmrWithTopics: any) => {
         if (stmrWithTopics) {
           console.log('Loaded stmrWithTopics:', stmrWithTopics);
         } else {
           console.log('No STMR with topics data yet');
           
             }
           });
      
    }  )
 
 
   

  }
  

  onCategoryToggle(event: any) {
    this.categoryToggle = event.detail.checked;
    this.updateGroupedForms();
  }

  navigateToReleaseNotes() {
  this.router.navigate(['/release-notes']);
}
  navigateToDataEnhancedForm() {
    this.router.navigate(['/inbox']);
  }

  onSearchChange(query: string | null | undefined) {
    this.store.dispatch(RigActions.updateSearchQuery({ query: query ?? '' }));
  }

   onHideCompletedToggle(event: any) {
    this.hideCompletedToggle = event.detail.checked;
    this.applyCompletedFilterForForms();
    this.stmrWithTopics$.subscribe(stmrs => {
    this.filteredStmrWithTopics = this.applyCompletedFilterForSTMRs(stmrs);
  });
  }


  applyCompletedFilterForForms() {
    let result = this.latestFilteredForms;

    if (this.hideCompletedToggle) {
      result = result.filter(f => f.FORM_STATUS !== 'SUBM');
    }

    this.plainList = result;
    this.updateGroupedForms();
  }


  applyCompletedFilterForSTMRs(stmrs: any[]): any[] {
    if (this.hideCompletedToggle) {
      return stmrs.filter(f => f.STMR_STATUS !== 'SUBM');
      }
    return stmrs;
  }

  





   



  async onFormSelect(form: FORM_HEADER) {
    this.unviredSdk.logInfo('FormsPage', 'onFormSelect', `Form selected: ${form.FORM_ID}`);

    try {
      const pathResult: any = await this.unviredSdk.getAttachmentFolderPath();
      if (!pathResult) {
        this.unviredSdk.logError('FormsPage', 'onFormSelect', 'Could not retrieve attachment folder path.');
        return;
      }
      const attachmentPath = pathResult;
      const folderName = form.VER_ID;
      const zipFileName = `${form.VER_ID}.zip`;
      this.unviredSdk.logInfo('FormsPage', 'onFormSelect', `Attachment path: ${attachmentPath}, Folder name: ${folderName}, Zip file name: ${zipFileName}`);

      await this.platform.ready();

      const folderExists = await this.checkDirectoryExists(attachmentPath, folderName);
      if (folderExists) {
        this.unviredSdk.logInfo('FormsPage', 'onFormSelect', 'Folder already exists. Proceeding to check for HTML file.');
        await this.checkForHtmlFileInFolder(attachmentPath, folderName, form);
      } 
      else {
        this.unviredSdk.logInfo('FormsPage', 'onFormSelect', 'Folder does not exist. Checking for zip file or downloading template.');
        const zipFileExists = await this.checkFileExists(attachmentPath, zipFileName);
        if (zipFileExists) {
          this.unviredSdk.logInfo('FormsPage', 'onFormSelect', 'Zip file exists. Unzipping now.');
          await this.createDirectoryIfNeeded(attachmentPath, folderName);
          const attachmentAsArrayBuffer = await this.utilityService.getAttachmentAsArrayBuffer(attachmentPath + zipFileName)
          await this.utilityService.unzipTheFileAndWriteFileContent(attachmentAsArrayBuffer, folderName, attachmentPath)

          const folderExistsAfterUnzip = await this.checkDirectoryExists(attachmentPath, folderName);
          if (folderExistsAfterUnzip) {
            this.unviredSdk.logInfo('FormsPage', 'onFormSelect', 'Folder exists after unzipping. Proceeding to check for HTML file.');
            await this.checkForHtmlFileInFolder(attachmentPath, folderName, form);
          }
        } 
        else {

          this.unviredSdk.logInfo('FormsPage', 'onFormSelect', 'Zip file does not exist. Downloading template now.');
          let tempDownloaded = await this.dataService.getTemplateDownloadedFromServer(form);
          this.unviredSdk.logInfo('FormsPage', 'onFormSelect', `Template download status from server: ${tempDownloaded}`);
          let tempAttachmentData = await this.dataService.getTemplateAttachmentFromDB(form);
          if (tempAttachmentData) {
            this.unviredSdk.logInfo('FormsPage', 'onFormSelect', 'Downloading the actual attachment file...');
            ump.downloadAttachmentSync(tempAttachmentData, async (data: any) => {
              this.unviredSdk.logInfo('FormsPage', 'onFormSelect', 'Attachment downloaded successfully. Unzipping now.');
              const attachmentAsArrayBuffer = await this.utilityService.getAttachmentAsArrayBuffer(attachmentPath + zipFileName)
              await this.utilityService.unzipTheFileAndWriteFileContent(attachmentAsArrayBuffer, folderName, attachmentPath)

              // Verify folder exists after unzipping
              const folderExistsAfterUnzip = await this.checkDirectoryExists(attachmentPath, folderName);
              this.unviredSdk.logInfo('FormsPage', 'onFormSelect', `Folder exists after unzipping: ${folderExistsAfterUnzip}`);

              if (folderExistsAfterUnzip) {
                this.unviredSdk.logInfo('FormsPage', 'onFormSelect', 'Folder exists after unzipping. Proceeding to check for HTML file.');
                await this.busyIndicatorService.hideBusyIndicator();
                await this.checkForHtmlFileInFolder(attachmentPath, folderName, form);
              }
            }, async (err: any) => {
              await this.busyIndicatorService.hideBusyIndicator();
              console.log('Error downloading template:', err);
              await this.alertController.create({
                header: 'Error',
                message: 'Failed to download template: ' + err.message,
                buttons: ['OK']
              }).then(alert => alert.present());
            });
            console.log('Download complete after download', zipFileName);
          }
          console.log('attachmentPath after download is ', attachmentPath);
          console.log('zipFileName after download is ', zipFileName);
        }
      }
    } catch (error) {
      console.error('Error in onFormSelect:', error);
      this.unviredSdk.logError(
        'FormsPage',
        'onFormSelect',
        `Error selecting form: ${error}`
      );
    }
  }






  private async updateFormDataInHTML(folderPath: string, form: FORM_HEADER) {
    try {
      // Read the HTML file
      const htmlContent = await this.file.readAsText(folderPath, 'index.html');
      
      // Get prefilled data including logo
      const prefilledData = await this.store.select(selectPrefilledData).pipe(take(1)).toPromise();
      const logo = prefilledData?.LOGO || '';
      
      // Create the script content
      const script = `var prefilledData = ${JSON.stringify(prefilledData || {})};
                      var companyLogo = '${logo}';`;
      
      // Replace the placeholder with the script
      const updatedHtml = htmlContent.replace(
        '<!-- FORM DATA -->',
        `<!-- FORM DATA -->\n<script>\n${script}\n</script>\n<!-- ./FORM DATA -->`
      );
      
      // Write the updated HTML back
      await this.file.writeExistingFile(folderPath, 'index.html', updatedHtml);
      
    } catch (error) {
      console.error('Error updating HTML with form data:', error);
    }
  }

  /**
   * Opens the HTML file using InAppBrowser based on platform
   */
 


   //Update form view
  async updateFormsArray(form: FORM_HEADER, formData: string, loggedInUserId: string, submitForm: boolean, isSubmit?: boolean, isQueued?: boolean) {
    // Form Header with FORM_STATUS
    console.log('updateFormsArray' , form , formData , loggedInUserId , submitForm , isSubmit , isQueued);
    let that = this,
      status = "",
      dateComp = moment.utc().unix(),
      tmpHeader = JSON.parse(JSON.stringify(form));

    if (isSubmit) status = AppConstants.VAL_FORM_STATUS.SUBM;
    else status = AppConstants.VAL_FORM_STATUS.INPR;

    tmpHeader.FORM_STATUS = status;
    tmpHeader.DATE_COMP = dateComp;
    tmpHeader.OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;

    // Update Operator Info
    var operatorInfo = ''
    let data = JSON.parse(formData)
    for (const key in data) {
      var subJSON = data[key]
      for (const key2 in subJSON) {
        if (key2 == 'Operator') {
          operatorInfo = subJSON[key2]
          break;
        }
      }
    }
    tmpHeader.OPERATOR = operatorInfo.replace("'", "''")

    // Do not update Sync Status if already Queued
    if (!isQueued) {
      tmpHeader.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
    }

    tmpHeader.LAST_SYNC_USER = loggedInUserId;
    tmpHeader.SUBM_BY = loggedInUserId;

    this.busyIndicatorService.displayBusyIndicator("Updating form status ...");

    let timezone = this.utilityService.getTimezone()

    console.log('tmp header is ' , tmpHeader.OPERATOR , tmpHeader.FORM_ID , dateComp , status);
    let query = `UPDATE FORM_HEADER SET FORM_STATUS ='${status}', DATE_COMP ='${dateComp}', OBJECT_STATUS='${AppConstants.OBJECT_STATUS.MODIFY}', LAST_SYNC_USER = '${loggedInUserId}', SUBM_BY = '${loggedInUserId}', TIME_ZONE = '${timezone}', OPERATOR = '${tmpHeader.OPERATOR}' WHERE FORM_ID = '${tmpHeader.FORM_ID}'`;
   console.log('query isss' , query);
    await this.unviredSdk.dbExecuteStatement(query).then(async (result: any) => {
      console.log('result after update query is ' , result);

      if (result.type === ResultType.success) {
        // Form Data
        let formsDataObj: FORM_DATA = <FORM_DATA>{};
        formsDataObj.FORM_ID = tmpHeader.FORM_ID;
        formsDataObj.DATA = formData;
        console.log('formsDataObj' , formsDataObj);
        let parsedFormData = JSON.parse(formData);
if (parsedFormData.LID) {
  formsDataObj.LID = parsedFormData.LID;
  formsDataObj.FID = parsedFormData.DATA_FID;
  formsDataObj.OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;
} else if (tmpHeader.LID) {
  formsDataObj.FID = tmpHeader.LID;
  formsDataObj.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
}
        if (tmpHeader.LID) {
          formsDataObj.FID = tmpHeader.LID;
          formsDataObj.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
        }

        // Do not update Sync Status if already Queued
        if (isQueued) {
          formsDataObj.SYNC_STATUS = AppConstants.SYNC_STATUS.QUEUED;
        } else {
          formsDataObj.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
        }
        console.log('formsDataObj is ' , formsDataObj.DATA);

              let updateFormDataQuery = `UPDATE FORM_DATA SET DATA = '${formsDataObj.DATA}', OBJECT_STATUS = '${AppConstants.OBJECT_STATUS.MODIFY}', P_MODE = 'M' WHERE FORM_ID = '${formsDataObj.FORM_ID}'`;
        // console.log('updateFormDataQuery is ' , updateFormDataQuery);
        let updateResult = await this.unviredSdk.dbExecuteStatement(updateFormDataQuery);
        // console.log('updateResult is ' , updateResult);
        if (updateResult.type === ResultType.success) {
          // console.log('Form data updated successfully');
           let selectQuery = `SELECT * FROM FORM_DATA WHERE FORM_ID = '${formsDataObj.FORM_ID}'`;
          let selectResult = await this.unviredSdk.dbExecuteStatement(selectQuery);
          // console.log('selectResult is ' , selectResult);
           if (isSubmit) {
              // Create FORM_ACTION for the form
              let formActionsObj: any = {};
              formActionsObj.FORM_ID = tmpHeader.FORM_ID;
              formActionsObj.ACTION_CODE = AppConstants.ACTION_CODE?.COMPLETE;
              formActionsObj.FID = tmpHeader.LID;
              formActionsObj.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;


              this.unviredSdk.dbInsertOrUpdate(AppConstants.TABLE_FORM_ACTION, formActionsObj, AppConstants.BOOL_FALSE).then((result: any) => {
                if (result.type === ResultType.success) {

                  if (submitForm) {
                    tmpHeader = this.deleteFormPageFields(tmpHeader);
                    this.callCreateForm(tmpHeader);
                  } else {
                    this.reloadForm(formsDataObj.FID)
                    that.busyIndicatorService.hideBusyIndicator();
                  }

                } else {
                  that.busyIndicatorService.hideBusyIndicator();
                  that.unviredSdk.logError("FormsPage", "updateFormsArray", "Inserting Form Action into database failed: " + result.message + " " + result.error);
                }
              });
            } else {
              if (submitForm) {
                tmpHeader = this.deleteFormPageFields(tmpHeader);
                this.callCreateForm(tmpHeader);
              } else {
                this.reloadForm(formsDataObj.FID)
                that.busyIndicatorService.hideBusyIndicator();
              }
            }
        }
          else {
            that.busyIndicatorService.hideBusyIndicator();
            that.unviredSdk.logError("FormsPage", "updateFormsArray", "Inserting/Updating Form Data into database failed: " + result.message + " " + result.error);
          }
      }
      else {
        that.busyIndicatorService.hideBusyIndicator();
        that.unviredSdk.logError("FormsPage", "updateFormsArray", "Inserting/Updating Form Header into database failed: " + result.message + " " + result.error);
      }
    })
  }


    // Delete all additional fields from the given object
  deleteFormPageFields(tmpHeader: any) {
    try {
      var delHeader = tmpHeader;
      if (delHeader.DATA) delete delHeader.DATA;
      if (delHeader.formStatus) delete delHeader.formStatus;
      if (delHeader.syncStatus) delete delHeader.syncStatus;
      if (delHeader.isSelected) delete delHeader.isSelected;
      if (delHeader.CAT_ID) delete delHeader.CAT_ID;
      if (delHeader.CATEGORY_DESC) delete delHeader.CATEGORY_DESC;
      if (delHeader.TMPLT_ID) delete delHeader.TMPLT_ID;
      if (delHeader.TEMPLATE_DESC) delete delHeader.TEMPLATE_DESC;
      if (delHeader.PUBLISHED_ON) delete delHeader.PUBLISHED_ON;
      if (delHeader.DATA) delete delHeader.DATA;
      if (delHeader.DATA_LID) delete delHeader.DATA_LID;
      if (delHeader.DATA_FID) delete delHeader.DATA_FID;
    } catch (e) {
      this.unviredSdk.logError("FormsPage", "deleteFormPageFields", "Deleting additional fields from given custom FORM object failed.");
    }
    return delHeader;
  }

    async callCreateForm(formHeader: FORM_HEADER) {
      console.log('the form header in callcreateform is ' , formHeader);
    let that = this,
      inputHeader = {
        "FORM_HEADER": formHeader
      };

    // Queue the latest form to the server
    this.unviredSdk.logInfo("FormsPage", "callCreateForm", "Sending form to server in async mode.");
  //  await this.unviredSdk.syncBackground(RequestType.RQST, inputHeader, "", AppConstants.FORMS_PA_FORM_SUBMIT, "FORM", formHeader.LID, AppConstants.BOOL_FALSE).subscribe( (result) => {

      that.busyIndicatorService.hideBusyIndicator();
      that.reloadForm(formHeader.LID)

  }


    async reloadForm(formLid: any) {
      console.log('formLid is ' , formLid);
    // In Category mode, the screen does not refresh.
    // Therefore we are reloading the complete screeb.
    if (this.categoryToggle) {
      this.store.dispatch(RigActions.loadAllFormsFromDb());

      return
    }
this.store.dispatch(RigActions.loadAllFormsFromDb());
      this.forms$ = this.store.select(selectAllForms);
    let that = this
    let  query = this.dataService.returnFormsPageQuery(formLid);
   this.unviredSdk.logInfo("FormsPage", "reloadForm", "Query to reload specific Form: " )
    var results: any = []

    let reloadForm = await this.unviredSdk.dbExecuteStatement(query)

    if(reloadForm.type === ResultType.success){
      if(reloadForm.data.length > 0){
        results = reloadForm.data;
        console.log('result in reload form is ' , results);

                results = reloadForm.data;
        results.forEach((element: any) => {
          if (element.FORM_STATUS) {
            try {
              element["formStatus"] = this.utilityService.getFormStatusObj(element.FORM_STATUS);
              var tmp = this.utilityService.getSyncStatusObj(element.SYNC_STATUS);
              console.log('tmp is in forms page ' , tmp);
              if (tmp["descr"] && tmp["color"]) element["syncStatus"] = tmp;
            } catch (e) {
              this.unviredSdk.logError("FormsPage", "reloadForm", "Determining status error : " + JSON.stringify(e));
            }
          }
          if (element.DATA) {
            try {
              element.DATA = JSON.parse(element.DATA);
              console.log('element.DATA is ' , element.DATA);
            } catch (e) {
              this.unviredSdk.logError("FormsPage", "reloadForm", "Parsing FORM_DATA error : " + JSON.stringify(e));
            }
          }
        });
      }

     let formPageQueryResult = await this.unviredSdk.dbExecuteStatement(query)
      if(formPageQueryResult.type === ResultType.success){
        if(formPageQueryResult.data.length > 0){
          results = formPageQueryResult.data;
          results.forEach((element: any) => {
            if (element.FORM_STATUS) {
              try {
                element["formStatus"] = this.utilityService.getFormStatusObj(element.FORM_STATUS);
                var tmp = this.utilityService.getSyncStatusObj(element.SYNC_STATUS);
                if (tmp["descr"] && tmp["color"]) element["syncStatus"] = tmp;
              } catch (e) {
                this.unviredSdk.logError("FormsPage", "reloadForm", "Determining status error : " + JSON.stringify(e));
              }
            }
            if (element.DATA) {
              try {
                element.DATA = JSON.parse(element.DATA);
              } catch (e) {
                this.unviredSdk.logError("FormsPage", "reloadForm", "Parsing FORM_DATA error : " + JSON.stringify(e));
              }
            }
          });
      } else {
        results = [];
        this.unviredSdk.logError("Forms", "reloadForm", "Did not get a Form Record for Form ID: " + formLid);
        return;
      }
    }

      
  }

    }


    async checkForHtmlFileInFolder(attachmentPath: string, folderName: string, form: FORM_HEADER){
      try {
      const folderPath = `${attachmentPath}${folderName}/`;
      console.log('Checking for index.html in path:', folderPath);

        const htmlFileExists = await this.checkFileExists(folderPath, 'index.html');
        if (htmlFileExists) {
          console.log('index.html file exists in folder.');

          // Update the HTML file with form data and logo before opening
          await this.updateFormDataInHTML(folderPath, form);

          const indexHtmlPath = `${folderPath}index.html`;
          try {
            console.log('Creating iframe form viewer...');
            this.formsDisplayService.formSelected = true;
        
            this.formsDisplayService.createEmbeddedIframe(indexHtmlPath , form)
            
          } catch (error) {
            console.error('Error opening iframe:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            // await this.showAlert('Error', `Failed to open form viewer: ${errorMessage}`);
          }
        
        } else {
          throw new Error('index.html not found');
        }

      } catch (fileError) {
        console.log('index.html not found, checking other files in folder...');
        try {
            const entries = await this.file.listDir(attachmentPath, folderName);
            console.log('Files in folder:', entries.map(entry => entry.name));
        } catch (listError) {
          console.log('Error listing directory contents:', listError);
        }
      }
    }

    /**
     * Platform-aware directory existence check
     */
    private async checkDirectoryExists(attachmentPath: string, folderName: string): Promise<boolean> {
      try {
        // First try to detect if we're in Electron environment
        if ((window as any).require) {
          try {
            // Use Node.js fs for Electron
            const fs = (window as any).require('fs');
            const path = (window as any).require('path');
            const fullPath = path.join(attachmentPath, folderName);
            const exists = fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory();
            console.log(`Electron directory check: ${fullPath} exists: ${exists}`);
            return exists;
          } catch (electronError) {
            console.log('Electron fs check failed, falling back to Cordova:', electronError);
          }
        }

        // Fallback to Cordova File plugin for mobile platforms or if Electron fails
        try {
          await this.file.checkDir(attachmentPath, folderName);
          return true;
        } catch (cordovaError: any) {
          // If Cordova also fails, try a different approach
          console.log('Cordova directory check failed:', cordovaError);

          // Try to list the parent directory to see if our folder exists
          try {
            const entries = await this.file.listDir(attachmentPath, '');
            const folderExists = entries.some(entry => entry.name === folderName && entry.isDirectory);
            console.log(`Directory listing check: ${folderName} exists: ${folderExists}`);
            return folderExists;
          } catch (listError) {
            console.log('Directory listing also failed:', listError);
            return false;
          }
        }
      } catch (error) {
        console.log('All directory check methods failed:', error);
        return false;
      }
    }

    /**
     * Platform-aware file existence check
     */
    private async checkFileExists(folderPath: string, fileName: string): Promise<boolean> {
      try {
        // First try to detect if we're in Electron environment
        if ((window as any).require) {
          try {
            // Use Node.js fs for Electron
            const fs = (window as any).require('fs');
            const path = (window as any).require('path');
            const fullPath = path.join(folderPath, fileName);
            const exists = fs.existsSync(fullPath) && fs.statSync(fullPath).isFile();
            console.log(`Electron file check: ${fullPath} exists: ${exists}`);
            return exists;
          } catch (electronError) {
            console.log('Electron fs check failed, falling back to Cordova:', electronError);
          }
        }

        // Fallback to Cordova File plugin for mobile platforms or if Electron fails
        try {
        return await this.file.checkFile(folderPath, fileName);
        } catch (cordovaError: any) {
          // If Cordova also fails, try to list directory contents
          console.log('Cordova file check failed:', cordovaError);

          try {
            // Remove trailing slash from folderPath for listDir
            const parentPath = folderPath.replace(/\/$/, '');
            const parentDir = parentPath.substring(0, parentPath.lastIndexOf('/'));
            const dirName = parentPath.substring(parentPath.lastIndexOf('/') + 1);

            const entries = await this.file.listDir(parentDir, dirName);
            const fileExists = entries.some(entry => entry.name === fileName && entry.isFile);
            console.log(`Directory listing file check: ${fileName} exists: ${fileExists}`);
            return fileExists;
          } catch (listError) {
            console.log('Directory listing for file check also failed:', listError);
            return false;
          }
        }
      } catch (error) {
        console.log('All file check methods failed:', error);
        return false;
      }
    }

    /**
     * Platform-aware directory creation
     */
    private async createDirectoryIfNeeded(attachmentPath: string, folderName: string): Promise<void> {
      try {
        // First try to detect if we're in Electron environment
        if ((window as any).require) {
          try {
            // Use Node.js fs for Electron
            const fs = (window as any).require('fs');
            const path = (window as any).require('path');
            const fullPath = path.join(attachmentPath, folderName);

            if (!fs.existsSync(fullPath)) {
              fs.mkdirSync(fullPath, { recursive: true });
              console.log(`Electron: Directory created: ${fullPath}`);
            } else {
              console.log(`Electron: Directory already exists: ${fullPath}`);
            }
            return;
          } catch (electronError) {
            console.log('Electron directory creation failed, falling back to Cordova:', electronError);
          }
        }

        // Fallback to Cordova File plugin for mobile platforms or if Electron fails
        try {
          await this.file.createDir(attachmentPath, folderName, false);
          console.log(`Cordova: Directory created: ${attachmentPath}${folderName}`);
        } catch (cordovaError: any) {
          console.log('Cordova directory creation failed:', cordovaError);
          // Don't throw here, as the directory might already exist
          // The error code 12 typically means "directory already exists"
          if (cordovaError.code !== 12) {
            throw cordovaError;
          } else {
            console.log('Directory likely already exists (error code 12)');
          }
        }
      } catch (error) {
        console.error('Error creating directory:', error);
        throw error;
      }
    }


    // Updated onSTMRSelect for router navigation
async onSTMRSelect(stmr: STMR_HEADER): Promise<void> {
  console.log('STMR selected:', stmr);
  
  // Navigate to STMR details page
  this.router.navigate(['/stmr-details'], {
    state: {
      stmrHeader: stmr,
      isEdit: true,
    }
  });
}

// Add ionViewWillEnter to handle refresh when returning from STMR details
ionViewWillEnter(): void {
  this.updateGroupedForms();
  // Check if returning from STMR details page
  const navigation = this.router.getCurrentNavigation();
  if (navigation?.extras?.state?.['returnFromSTMR']) {
    // Refresh data similar to modal onDidDismiss
    this.store.dispatch(RigActions.loadStmrDataAndTopicsFromDb());
    this.store.dispatch(RigActions.loadAllFormsFromDb());
  }
}
    async processSpConfigAndSaveToSettings(): Promise<void> {
  console.log('[DataService] processSpConfigAndSaveToSettings - METHOD CALLED');
  try {
    const spConfigData = await this.dataService.getSpConfigFromDB();
    const spLocalRoot = spConfigData.find(config => config.NAME === 'SP_LOCAL_ROOT');
    
    if (spLocalRoot && spLocalRoot.VALUE) {
      console.log('[DataService] Processing SP_LOCAL_ROOT value:', spLocalRoot.VALUE);
      let configPath = spLocalRoot.VALUE;
      let finalPath = '';
      
      if (this.attachmentHelper.isElectron()) {
        finalPath = this.attachmentHelper.normalizeLocalPath(configPath);
      } else {
        const basePath = this.attachmentHelper.getBasePath();
        const normalizedConfigPath = this.attachmentHelper.normalizeLocalPath(configPath);
        finalPath = basePath + normalizedConfigPath;
      }
      
      finalPath = this.attachmentHelper.sanitizePathPreserveFileScheme(finalPath);
      this.unviredSdk.logDebug('formsPage', 'processSpConfigAndSaveToSettings', `Final path after sanitization:', ${finalPath}`);
      
      await this.dataService.saveActualRigDocPath(finalPath);
      this.unviredSdk.logDebug('formsPage', 'processSpConfigAndSaveToSettings', ` SP_LOCAL_ROOT processed and saved successfully:, ${finalPath}`);
      console.log('SP_LOCAL_ROOT processed and saved successfully:', finalPath);
    } else {
      console.log(' No SP_LOCAL_ROOT found or no VALUE');
    }
  } catch (error) {
    this.unviredSdk.logError('formsPage', 'processSpConfigAndSaveToSettings', ` Error processing SP_LOCAL_ROOT: ${error}`);
    console.log(' Error processing SP config:', error);
  }
}
   


  initialPrefilledDataLoad() {

    console.log('[Store-Fixes] Registered a selector for reading Rig Data from DB');

    this.store.select(selectRigData)
      .pipe(
        filter(rigData => !!rigData)
      ).subscribe(async (rigData) => {

        console.log('[Store-Fixes] Rig loaded from DB, now checking rigData: ', JSON.stringify(rigData, null, 2));
        console.log('Rig data from DB:', JSON.stringify(rigData, null, 2));

        if (rigData?.RIG_NO == '') {
          console.log('[Store-Fixes] Rig data not found, showing popup');
          await this.dataService.openSiteNumberPopup(); // Show popup only after DB load is complete and rigData is missing
          return;
        }

        if (rigData && rigData.RIG_NO) {
          console.log('[Store-Fixes] Rig data found, Dispatching Events');
          // ... your existing logic ...
          this.store.dispatch(RigActions.loadAllTemplatesFromDb());
          this.store.dispatch(RigActions.loadPrefilledData());
          this.store.dispatch(RigActions.loadAllFormsFromDb());
          this.store.dispatch(RigActions.loadStmrDataAndTopicsFromDb());
          this.store.dispatch(RigActions.loadReleaseNotesFromDb());

          if (!this.spConfigProcessed) {
            this.dataService.processSpConfigAndSaveToSettings();
            this.spConfigProcessed = true;
          }

          // TODO: Add dispatch for loading RELEASE NOTES
        }
      });

    console.log('[Store-Fixes] Dispatching LoadRigFromDb action');
    this.store.dispatch(RigActions.loadRigFromDb());
  }

    // Prefer explicit sync state over name pattern
public displayStmrId(stmr: any): string {
  const idRaw = stmr?.STMRID ?? stmr?.STMR_ID ?? stmr?.STMR_NO ?? '';
  const id = typeof idRaw === 'string' ? idRaw.trim() : '';
  return id.toLowerCase().startsWith('new') ? 'New' : id;
}


    selectFormToDelete() {
    this.selectionMode = !this.selectionMode;
    if (!this.selectionMode) {
      this.selectedForms = [];
      this.selectedSTMRs = [];
    }
  }

  onFormCheckboxChange(form: FORM_HEADER, checked: boolean) {
    console.log('Form checkbox changed:', form, checked);
    if (checked) {
      if (!this.selectedForms.includes(form)) {
        this.selectedForms.push(form);
      }
    } else {
      this.selectedForms = this.selectedForms.filter(f => f !== form);
    }
  }

  async deleteSelectedItems() {
    const formCount = this.selectedForms.length;
    const stmrCount = this.selectedSTMRs.length;
    let message = '';

    if (formCount > 0 && stmrCount > 0) {
      message = `You have selected ${formCount} Form(s) and ${stmrCount} STMR(s). Are you sure you want to delete them?`;
    } else if (formCount > 0) {
      message = `You have selected ${formCount} Form(s). Are you sure you want to delete them?`;
    } else if (stmrCount > 0) {
      message = `You have selected ${stmrCount} STMR(s). Are you sure you want to delete them?`;
    } else {
      return;
    }

    await this.alertController.create({
      header: 'Confirmation',
      message: message,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Delete',
          handler: () => {
            this.deleteFormsAndSTMRs(this.selectedForms, this.selectedSTMRs);
          }
        }
      ]
    }).then(alert => alert.present());
  }

  async deleteFormsAndSTMRs(selectedForms: FORM_HEADER[], selectedSTMRs: STMR_HEADER[]) {
    console.log('Deleting selected forms:', selectedForms);
    console.log('Deleting selected STMRs:', selectedSTMRs);
    await this.dataService.deleteConfirmedFormsAndSTMRsFromDB(selectedForms, selectedSTMRs);
    this.selectedForms = [];
    this.selectedSTMRs = [];
    this.selectionMode = false;
    this.store.dispatch(RigActions.loadAllFormsFromDb());
    // this.store.dispatch(RigActions.loadStmrDataFromDb());
    this.store.dispatch(RigActions.loadStmrDataAndTopicsFromDb());
    this.updateGroupedForms();
  }

  onSTMRCheckboxChange(stmr: STMR_HEADER, checked: boolean) {
    console.log('STMR checkbox changed:', stmr, checked);
    if (checked) {
      if (!this.selectedSTMRs.includes(stmr)) {
        this.selectedSTMRs.push(stmr);
      }
    } else {
      this.selectedSTMRs = this.selectedSTMRs.filter(s => s !== stmr);
    }
  }

  toggleGrid() {
  this.gridVisible = !this.gridVisible;

  if (!this.gridVisible) {
    this.hideCompletedToggle = false;
    this.categoryToggle = false;

    // Re-apply filters so lists update
    this.applyCompletedFilterForForms();                // Forms
    this.stmrWithTopicsFiltered$.subscribe(stmrs => {
      this.filteredStmrWithTopics = stmrs; // reset STMRs to only search
    });
  }
}

  getInfoMessages(header : any , lid : any) {
  this.unviredSdk.getInfoMessages(header, lid).then((result) => {
    console.log('Info messages:', result);
    if (header === 'FORM_HEADER') {
      this.formErrorMessage = result
        .filter((r: any) => r.belid === lid)   // ✅ only keep matching lid
        .map((r: any) => r.message);
    } else {
      this.stmrErrorMessage = result
        .filter((r: any) => r.belid === lid)   // ✅ only keep matching lid
        .map((r: any) => r.message);
    }
  }).catch((error) => {
    console.log('Error getting info messages:', error);
  });
}
}
