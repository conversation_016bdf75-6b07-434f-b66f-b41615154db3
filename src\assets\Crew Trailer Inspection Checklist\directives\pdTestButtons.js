angular.module('PDTest')
.directive('pdTestButtons', function() {
    var html = '<div class="hidden-print" ng-if="form.debug">';
    html += '<br>';
    html += '<span class="btn btn-lg btn-default" onclick="loadDefaultValues()">loadDefaultValues</span>';
    html += '<span class="btn btn-lg btn-default" onclick="loadPreviousValues()">loadPreviousValues</span>';
    html += '<span class="btn btn-lg btn-default" onclick="generateJSON()">generateJSON</span>';
    html += '<span class="btn btn-lg btn-default" onclick="setStatus()">setStatus</span>';
    html += '</div>';

    return {
        restrict: 'EA',
        template: html
    };

});