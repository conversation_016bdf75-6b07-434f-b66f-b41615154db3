/* 
    Precision Drilling stylesheet 
    -----------------
    These are exceptions that override the base stylesheet

    COLOUR CODES:
    primary:    #004C80 - blue
    success:    #429538 - green
    warning:    #ea8a00 - yellow
    danger:     #DC3E38 - red
*/

/* Common look and feel */
body {
    position: relative;
    margin-top: 50px; /* add margin for fixed header */
    background-color: #F5F5F5;
}

hr {
    border-top: 1px solid #9e9e9e;
}

/* Table styles */
.thead-inverse {
    color: #fff;
    background-color: #555;
}

/* .table-striped > tbody > tr:nth-child(2n+1) > td, .table-striped > tbody > tr:nth-child(2n+1) > th {
   background-color: #e6e6e6;
} */

/* breadcrumb like style for section navigation */
.sectionnav {
    margin-right: 8px;
}

/* Navbar: logo and title placement */
.navbar {
    border: 0;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,.26);
    margin-bottom: 0px;
    border-radius: 0px;
}

.navbar-default {
    background-color: #429538;
    border-color: #429538;
}

.navbar-header {
    padding-left: 23px;
}

.navbar-back {
    float: left;
    line-height: 17px;
    margin-left: 0px;
    margin-top: 8px;
    padding-left: 16px;
    color: white;
    font-size: 36px;
}

.navbar-brand {
    margin-left: 0px;
    padding-left: 7px;
    padding-right: 7px;
    padding-top: 7px;
    padding-bottom: 7px;
}

.navbar-title {
    position: absolute;
    left: 0;
    margin-left: 60px;
    padding-top: 14px; 
    color: #FFFFFF;
    font-size: 16px;
}

.navbar-btn {
    margin-right: 8px;
}
  
.navbar-brand>a:hover {
    background-color: #FFFFFF;    
}
  
.navbar-brand>img {
    -o-object-fit: contain; /* Opera Support */
    object-fit: contain; /* Resize down to fit container */
    max-height: 100%; /* resize DOWN to height of container which is set to 50px */
    height: 100%; /* resize UP to fit*/
    max-width: 100%; 
    width: auto; /* resize based on height */
    margin: 0 auto;
}
  
.navbar-brand.normal {
    background-color: #fff;
    color: #333;
}

.navbar-inverse {
    background-color: #004C80
}
  
.navbar-inverse .navbar-brand, 
.navbar-inverse .navbar-nav>li>a, 
.navbar-inverse .navbar-text {
    color: #FEFEFE;
}

.navbar-default .navbar-nav>li>a {
    color: #FFFFFF;
}

.navbar-default .navbar-nav>.active>a, 
.navbar-default .navbar-nav>.active>a:focus, 
.navbar-default .navbar-nav>.active>a:hover {
    color: #FFFFFF;
    background-color: #3b8332;
}

.navbar-right {
    float: right!important;
    margin-right: 8px;
}

/* Form container */
.form-container {
    background-color: #FFFFFF;
    border: rgb(235, 235, 235) 1px solid;
    padding: 8px;
    margin-top: 16px;
}

.form-container-title {
    width: 100%;
    padding-left: 50px;
    padding-top: 12px; 
    font-size: 20px;
    font-weight: 300;
    margin-bottom: 24px;
}

/* Section container */
.section-container {
    background-color: #FFFFFF;
    border: rgb(10, 10, 10) 3px solid;
    padding: 5px;
    margin-top: 16px;
}

.section-container-title {
    width: 100%;
    padding-left: 50px;
    padding-top: 12px;
    font-size: 20px;
    font-weight: 300;
    margin-bottom: 16px;
}

/* Hide HTML5 Up and Down arrows. */
input[type="number"]::-webkit-outer-spin-button, input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
 
input[type="number"] {
    -moz-appearance: textfield;
}

.form-control {
    padding-right: 4px;
}

/* table styles */
.table-responsive {
    overflow-x: visible;
}

.table-bordered {
    border: 2px solid #555;
}

.table-bordered>thead>tr>th, 
.table-bordered>tbody>tr>th, 
.table-bordered>tfoot>tr>th, 
.table-bordered>thead>tr>td, 
.table-bordered>tbody>tr>td, 
.table-bordered>tfoot>tr>td {
    border: 1px solid #555;
}

.table {
    width: 100%;
    max-width: 100%;
    margin-bottom: 7px;
}

/* corporate colours for buttons */
.btn-clear {
    color: #fff;
    background-color: transparent;
    border: none;
    cursor: pointer;
}

.btn-outline {
    color: #fff;
    background-color: transparent;
    border-color: #FFFFFF;
}

.btn-primary {
    color: #fff;
    background-color: #004C80;
    border-color: #004C80;
}

.btn-success {
    color: #fff;
    background-color: #429538;
    border-color: #429538;
}

.btn-warning {
    color: #fff;
    background-color: #ea8a00;
    border-color: #ea8a00;
}

.btn-danger {
    color: #fff;
    background-color: #DC3E38;
    border-color: #DC3E38;
}

.btn-dark:focus,
.btn-dark.focus {
  color: #fff;
  background-color: #858888;
  border-color: #858888;
}

.btn-dark {
    color: #fff;
    background-color: #858888;
    border-color: #858888;
}

.text-light {
    color: #fff;
}

 .text-info {
        color: #858888 !important;
    }

/* corporate colours for labels */
.label-primary {
    background-color: #004C80;
}

.label-success {
    background-color: #429538;
}

.label-warning {
    background-color: #ea8a00 !important;
}

.label-danger {
    background-color: #DC3E38;
}

/* context icon colours */
.icon-light {
    color: #fff;
    font-size: x-large;
    cursor: pointer;
}

.icon-muted {
    color: #90939a;
    font-size: x-large;
    cursor: pointer;
}

.icon-selected {
    color: #858888;
    font-size: x-large;
    cursor: pointer;
}

.icon-warning {
    color: #ea8a00;
    font-size: x-large;
    cursor: pointer;
}

.icon-danger {
    color: #DC3E38;
    font-size: x-large;
    cursor: pointer;
}

.icon-success {
    color: #429538;
    font-size: x-large;
    cursor: pointer;
}

/* Signature Pad styling*/
.signcontainer {
    height: 220px !important;
    width: 568px !important;
}

.signcontainer .signature {
    box-shadow: 0 2px 5px 0 rgba(0,0,0,.26);
    background-color: #fff;
    margin: 0 auto;
    cursor: pointer;
}

.signcontainer .signature canvas {
    margin: 0 auto;
    cursor: pointer;
}

.signatureresult {
    box-shadow: 0 2px 5px 0 rgba(0,0,0,.26);
    height: 55px;
    width: 142px;
}

/* Alerts and alert animation */
 .alert {
    outline: none;
}
.alert.top,
.alert.top-left,
.alert.top-right,
.alert.bottom,
.alert.bottom-left,
.alert.bottom-right {
    position: fixed;
    z-index: 1050;
    margin: 20px;
}
.alert.top,
.alert.top-left,
.alert.top-right {
    top: 50px;
}
.alert.top {
    right: 0px;
    left: 0px;
}
.alert.top-right {
    right: 0px;
}
.alert.top-right .close {
    padding-left: 10px;
}
.alert.top-left {
    left: 0px;
}
.alert.top-left .close {
    padding-right: 10px;
}
.alert.bottom,
.alert.bottom-right,
.alert.bottom-left {
    bottom: 0px;
}
.alert.bottom {
    right: 0px;
    left: 0px;
}
.alert.bottom-right {
    right: 0px;
}
.alert.bottom-right .close {
    padding-left: 10px;
}
.alert.bottom-left {
    left: 0px;
}
.alert.bottom-left .close {
    padding-right: 10px;
}
.alert-material {
    outline: none;
    display: inline-block;
    background: #868686;
    color: #f1f1f1;
    min-height: 48px;
    padding: 13px 24px 12px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .26);
    z-index: 9999;
    border-radius: 2px;
    cursor: default;
    font-size: large;
}

/* Static text area */
.static-spacing {
    line-height: 1.7em;
}

.icon {
    display: inline-block;
    width: 1em;
    height: 1em;
    stroke-width: 0;
    stroke: currentColor;
    fill: currentColor;
  }

.vertical-align {
    display: flex;
    align-items: center;
}

.dropdown-wide {
    min-width: 300px;
}

.table-borderless {
    border: 0;
}

.table-borderless>thead>tr>th, 
.table-borderless>tbody>tr>th, 
.table-borderless>tfoot>tr>th, 
.table-borderless>thead>tr>td, 
.table-borderless>tbody>tr>td, 
.table-borderless>tfoot>tr>td {
    border: 0;
}

input[type=text]::-ms-clear {
	display:none;
}

/* Print layout */
@media print {
    body {
        -webkit-filter: none !important;
        filter: none !important;
        font-size: 9px;
        line-height: 1;
        margin-top: 0px; /* remove fixed header margin */
        -webkit-print-color-adjust:exact; /* retain colours in Chrome print/preview */
    }

    hr {
        margin-top: 4px;
        margin-bottom: 8px;
        border-top: 1px solid #686868;
    }

    /* labels */


    .label-primary {
        border: none;
        color: #FFFFFF !important;
        background-color: #004C80 !important;
    }
    
    .label-success {
        border: none;
        color: #FFFFFF !important;
        background-color: #429538 !important;
    }
    
    .label-warning {
        border: none;
        color: #FFFFFF !important;
        background-color: #ea8a00 !important;
    }
    
    .label-danger {
        border: none;
        color: #FFFFFF !important;
        background-color: #DC3E38 !important;
    }

    /*text*/

    .text-success {
        color: #28a745 !important;
    }

    .text-info {
        color: #858888 !important;
    }

    .text-warning {
        color: #ea8a00 !important;      }

    .text-danger {
        color: #dc3545 !important;
    }

    /* coloured icons */
    .icon-muted:before {
        color: #90939a !important;
    }
    
    .icon-selected:before {
        color: #004C80 !important;
    }
    
    .icon-warning:before {
        color: #ea8a00 !important;      }
    
    .icon-danger:before {
        color: #DC3E38 !important;
    }
    
    .icon-success:before {
        color: #429538 !important;
    }

    .icon-selected {
        font-size: x-small;
    }

    .icon-info{
        fill: #31708f;
        stroke: #31708f;
        font-size: x-small
    }

    .icon-warning {
        fill: #ea8a00;
        stroke: #ea8a00;
        font-size: x-small;
    }

    .icon-danger {
        fill: #DC3E38;
        stroke: #DC3E38;
        font-size: x-small;
    }

    .icon-success {
        fill: #429538;
        stroke: #429538;
        font-size: x-small;
}

    /* tables */
    .thead-inverse th, 
    .thead-inverse td {
        background-color: #555 !important;
        color: #fff !important;
    }

    .table-condensed > thead > tr > th,
    .table-condensed > tbody > tr > th,
    .table-condensed > tfoot > tr > th,
    .table-condensed > thead > tr > td,
    .table-condensed > tbody > tr > td,
    .table-condensed > tfoot > tr > td {
        padding: 2px;
            }

    .table-stripe {
        background-color: #dedede !important;
	}
	
	.table-bordered-print {
		border-left: 1px solid #dddddd;
		border-right: 1px solid #dddddd;
	}

    .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, 
    .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, 
    .col-md-13, .col-md-14, .col-md-15, .col-md-16, .col-md-17, .col-md-18, 
    .col-md-19, .col-md-20,
    .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, 
    .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, 
    .col-sm-13, .col-sm-14, .col-sm-15, .col-sm-16, .col-sm-17, .col-sm-18, 
    .col-sm-19, .col-sm-20 {
      float: left;
    }
    
    .col-xs-1 { width: 2.5%; }


    .col-md-1, .col-sm-1 { width: 5%; }
    .col-md-2, .col-sm-2 { width: 10%; }
    .col-md-3, .col-sm-3 { width: 15%; }
    .col-md-4, .col-sm-4 { width: 20%; }
    .col-md-5, .col-sm-5 { width: 25%; }
    .col-md-6, .col-sm-6 { width: 30%; }
    .col-md-7, .col-sm-7 { width: 35%; }
    .col-md-8, .col-sm-8 { width: 40%; }
    .col-md-9, .col-sm-9 { width: 45%; }
    .col-md-10, .col-sm-10 { width: 50%; }
    .col-md-11, .col-sm-11 { width: 55%; }
    .col-md-12, .col-sm-12 { width: 60%; }
    .col-md-13, .col-sm-13 { width: 65%; }
    .col-md-14, .col-sm-14 { width: 70%; }
    .col-md-15, .col-sm-15 { width: 75%; }
    .col-md-16, .col-sm-16 { width: 80%; }
    .col-md-17, .col-sm-17 { width: 85%; }
    .col-md-18, .col-sm-18 { width: 90%; }
    .col-md-19, .col-sm-19 { width: 95%; }
    .col-md-20, .col-sm-20 { width: 100%; }
    
    .visible-xs {
      display: none !important;
    }
    .hidden-xs {
      display: block !important;
    }
    table.hidden-xs {
      display: table;
    }
    tr.hidden-xs {
      display: table-row !important;
    }
    th.hidden-xs,
    td.hidden-xs {
      display: table-cell !important;
    }
    .hidden-xs.hidden-print {
      display: none !important;
    }
    .hidden-sm {
      display: none !important;
    }
    .visible-sm {
      display: block !important;
    }
    table.visible-sm {
      display: table;
    }
    tr.visible-sm {
      display: table-row !important;
    }
    th.visible-sm,
    td.visible-sm {
      display: table-cell !important;
    }

    .form-group {
        margin-bottom: 0px;
    }

    .well {
        padding: 8px;
        margin-bottom: 0px;
    }

    .static-spacing {
        line-height: 1.1em;
    }
    input[type="radio"] {
        margin-top: -1px;
        vertical-align: middle;
    }
    input[type="checkbox"] {
        margin-top: -1px;
        vertical-align: middle;
    }

    .print-text-area {
        display: inline-block;
    }

    .print-text-area:empty {
        border-style: solid;
        border-width: 1px;
        border-color: #e3e3e3;
        min-height: 6em;
        width: 100%;
    }
    .print-text-area-med:empty {
        border-style: solid;
        border-width: 1px;
        border-color: #e3e3e3;
        min-height: 8em;
        width: 100%;
    }
    .print-text-area-lg:empty {
        border-style: solid;
        border-width: 1px;
        border-color: #e3e3e3;
        min-height: 12em;
        width: 100%;
    }

    .print-line {
        display: inline-block;
    }

    .print-line:empty {
        border-style: solid;
        border-width: 1px;
        border-color: #e3e3e3;
        min-height: 2.5em;
        width: 100%;
    }
}
