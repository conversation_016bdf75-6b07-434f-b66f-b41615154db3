import { Injectable } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>roll<PERSON>, LoadingController, ToastController } from '@ionic/angular/standalone';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root',
})
export class BusyIndicatorService {


    isLoading = false;
  message = '';
    loading: any;
 showAlert: any = null;
  constructor( private toastController: ToastController,  public alertController: AlertController,private loadingController: LoadingController,  private translate: TranslateService,) { }

   async displayBusyIndicator(message: string) {
    this.loading = await this.loadingController.create({ message });
    await this.loading.present();
  }

    async hideBusyIndicator() {
    if (this.loading) {
      await this.loading.dismiss();
      this.loading = undefined;
    }
  }
  

    async presentAlert(errorResponse: string) {
    this.showAlert = await this.alertController.create({
      header: this.translate.instant('Error'),
      message: errorResponse,
      animated: true,
      backdropDismiss: false,
      buttons: [
        {
          text: this.translate.instant('OK'),
        },
      ],
    });
    await this.showAlert.present();
  }


  async presentToast(message: string) {
    const toast = await this.toastController.create({
      message: message,
      duration: 5000,
      position: 'bottom',
    });

    await toast.present();
  }

}

