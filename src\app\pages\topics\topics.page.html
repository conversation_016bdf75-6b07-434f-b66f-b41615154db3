<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="goBack()">
        <ion-icon name="arrow-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>{{ topicType }} Topic</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="saveTopic()" class="save-btn" *ngIf="!isReadonly">
        SAVE
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">

<!-- ===== Topic Details (stacked layout) ===== -->
<div class="section-header">Topic Details</div>
<div class="section-body">

  <div class="row">
    <!-- Select Topic -->
    <div class="col-6">
      <div class="field-wrapper">
        <label class="field-label"
               [class.required-empty]="!newTopicDesc"
               [class.filled]="!!newTopicDesc">
          Select Topic <span class="required">*</span> :
        </label>

        <!-- styled button to act as select; keyboard accessible -->
        <button type="button"
                class="select-link"
                role="button"
                tabindex="0"
                [class.required-empty]="!newTopicDesc"
                [class.filled]="!!newTopicDesc"
                [disabled]="isReadonly"
                (click)="!isReadonly && openSelectList()">
          {{ newTopicDesc || ('Select' | translate) }}
          <i class="fa-solid fa-chevron-down" *ngIf="!newTopicDesc && !isReadonly"></i>
        </button>
      </div>
    </div>

    <!-- Select CTA (if applicable) -->
    <div class="col-6" *ngIf="checkIfCTA()">
      <div class="field-wrapper">
        <label class="field-label"
               [class.required-empty]="!newCtaDesc"
               [class.filled]="!!newCtaDesc">
          Select CTA <span class="required">*</span> :
        </label>
        <button type="button"
                class="select-link"
                role="button"
                tabindex="0"
                [class.required-empty]="!newCtaDesc"
                [class.filled]="!!newCtaDesc"
                [disabled]="isReadonly"
                (click)="!isReadonly && openSelectList(true)">
          {{ newCtaDesc || ('Select' | translate) }}
          <i class="fa-solid fa-chevron-down" *ngIf="!newCtaDesc && !isReadonly"></i>
        </button>
      </div>
    </div>

    <!-- Select HSE (if applicable) -->
    <div class="col-6" *ngIf="checkIfHSE()">
      <div class="field-wrapper">
        <label class="field-label"
               [class.required-empty]="!newhseDesc"
               [class.filled]="!!newhseDesc">
          Select File <span class="required">*</span> :
        </label>
        <button type="button"
                class="select-link"
                role="button"
                tabindex="0"
                [class.required-empty]="!newhseDesc"
                [class.filled]="!!newhseDesc"
                [disabled]="isReadonly"
                (click)="!isReadonly && openSelectListHSE()">
          {{ newhseDesc || ('Select' | translate) }}
          <i class="fa-solid fa-chevron-down" *ngIf="!newhseDesc && !isReadonly"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Date/time block (stacked label above control) -->
  <div class="date-time-section">
    <div class="field-wrapper">
      <label class="field-label"
             [class.required-empty]="!topicStart"
             [class.filled]="!!topicStart">
        Date &amp; Time
      </label>

      <ion-item>
        <ion-datetime
          class="datetime-input"
          [ngClass]="{ 'required-empty': !topicStart, 'filled': !!topicStart }"
          [value]="topicStart"
          [min]="minDate ? toLocalISOString(minDate) : null"
          [max]="maxDate ? toLocalISOString(maxDate) : null"
          presentation="date-time"
          locale="en-GB-u-hc-h24"
          [disabled]="isReadonly || !isAllowedToUpdateTopicTime()"
          (ionChange)="!isReadonly && onDatetimeChange($event)">
        </ion-datetime>
      </ion-item>

      <ion-text color="danger" *ngIf="errorMessageForInvalidDateTime">
        {{ errorMessageForInvalidDateTime }}
      </ion-text>
    </div>

    <div class="field-wrapper max-topic-row">
      <label class="field-label">Max Allowed Topic Time</label>

      <div class="max-topic-controls">
        <ion-note class="max-topic-note">{{ maxAllowedTopicTime }}</ion-note>
        <ion-button size="small"
                    *ngIf="isAllowedToUpdateTopicTime() && !isReadonly"
                    (click)="setMaxTopicTime()">
          {{ 'Set to Max Topic Time' | translate }}
        </ion-button>
      </div>
    </div>
  </div>

  <!-- Description (stacked) -->
  <div class="field-wrapper">
    <label class="desc-label" 
    [class.filled]="(TOPIC_NOTE || '').trim().length > 0"
    [class.required-empty]="!(TOPIC_NOTE || '').trim().length">
      Description / Additional Comments
    </label>
    <textarea
      class="desc-textarea"
      [(ngModel)]="TOPIC_NOTE"
      placeholder="Description / Additional Comments"
      [class.filled]="(TOPIC_NOTE || '').trim().length > 0"
      [class.required-empty]="!(TOPIC_NOTE || '').trim().length"
      [disabled]="isReadonly">
    </textarea>
  </div>
</div>

<!-- ===== Documents section (always visible if CTA or HSE selected) ===== -->
<div *ngIf="checkIfCTA() || checkIfHSE()">
  <div class="section-header">
       Documents&nbsp;<span *ngIf="getDocumentsCount() > 0"> ({{ getDocumentsCount() }})</span>
  </div>

  <div class="section-body documents-section">
    <div class="attachments-inline">

      <!-- CTA Documents -->
      <ng-container *ngIf="checkIfCTA()">
        <ng-container *ngIf="(ctaDocs?.length || 0) > 0; else noCtaDocs">
          <div class="doc-row" *ngFor="let doc of ctaDocs" (click)="openDoc(doc)">
            <i class="fas fa-file-alt fpx-44 color-grey"></i>
            <span class="doc-name">{{ doc.NAME }}</span>
          </div>
        </ng-container>
        <ng-template #noCtaDocs>
          <div class="no-items">Select a CTA to retrieve documents.</div>
        </ng-template>
      </ng-container>

      <!-- HSE Documents -->
      <ng-container *ngIf="checkIfHSE()">
        <ng-container *ngIf="(hseDocs?.length || 0) > 0; else noHseDocs">
          <div class="doc-row" *ngFor="let doc of hseDocs" (click)="openDocHSE(doc)">
            <i class="fas fa-file-alt fpx-44 color-grey"></i>
            <span class="doc-name">{{ doc.NAME }}</span>
          </div>
        </ng-container>
        <ng-template #noHseDocs>
          <div class="no-items">Select a HSE to retrieve documents.</div>
        </ng-template>
      </ng-container>

    </div>
  </div>
</div>


<!-- ===== Forms header: add button placed in header (rightmost) ===== -->
<div class="section-header forms-header">
  <span>
    Forms<span *ngIf="getActiveFormsCount() > 0"> ({{ getActiveFormsCount() }})</span>
  </span>

  <!-- Add button in header (rightmost) -->
  <ion-button fill="clear" class="add-form-btn" (click)="addFormsToTopic()" aria-label="Add form">
    <i class="fa-solid fa-circle-plus"></i>
    <ion-ripple-effect></ion-ripple-effect>
  </ion-button>
</div>

<!-- ===== Forms body (same padding as Topic Details) ===== -->
<div class="section-body forms-body">
  <div *ngIf="getActiveFormsCount() === 0" class="forms-row">
    <span class="no-forms">No forms added.</span>
  </div>
  </div>

  <div *ngIf="topicEntity && topicEntity.forms && topicEntity.forms.length > 0">
  <ng-container *ngFor="let form of topicEntity.forms; let i = index">
    <!-- <div class="forms-row" *ngIf="fHeader.P_MODE !== 'D'">
      <i class="fas fa-file-alt fpx-44 color-grey"></i>
      <span>{{ fHeader.NAME }}</span>
    </div> -->
    
    <ion-item *ngIf="form.P_MODE !== 'D'">
     <ion-thumbnail slot="start" class="thumbnail-tight">
              <div class="icon-wrapper">
                <i class="fas fa-file-alt base-icon"></i>
              </div>
            </ion-thumbnail>
            <ion-label>
              <h2>{{ form.NAME }}</h2>
              <p>{{ form.FORM_ID }}</p>
              <p><strong>Created On:</strong> {{ form.CRTD_ON | customDate }}</p>
              <p><strong>Last Updated:</strong> {{ form.DATE_COMP | customDate }}</p>
              <p>by {{ form.LAST_SYNC_USER }}</p>
            <!-- <div *ngIf="form.SYNC_STATUS == 3 && formErrorMessage?.length">
             <p *ngFor="let msg of formErrorMessage" style="color: red;">
              {{ msg }}
            </p> 
            </div> -->
            </ion-label>
    </ion-item>

  </ng-container>


  </div>



</ion-content>