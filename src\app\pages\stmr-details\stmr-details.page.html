<ion-header>
  <ion-toolbar class="main-toolbar" color="primary" mode="md">
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="onBack()">
        <ion-icon name="arrow-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>{{ 'STMR Details' | translate }}</ion-title>

    <ion-buttons slot="end">
      <ion-button fill="primary" 
            (click)="presentSettingsPopover($event)"
            style="--border-color: white; --border-width: 1px; --border-style: solid; --border-radius: 4px; --box-shadow: 0 2px 2px 0 rgba(0,0,0,0.14);">
  <i class="fa-solid fa-gear" style="color: white;"></i>
</ion-button>


      <ion-button fill="solid"
                  color="light"
                  class="header-btn"
                  (click)="saveAndExitSTMR()"
                  *ngIf="!isSTMRReadonly(stmrHeader)">
        <i class="fa-solid fa-chevron-left"></i>
        <span class="button-text">{{ 'Save And Exit' | translate }}</span>
      </ion-button>

      <ion-button fill="solid"
                  color="light"
                  class="header-btn"
                  (click)="localSaveSTMR()"
                  *ngIf="!isSTMRReadonly(stmrHeader)">
        <i class="fa-solid fa-check"></i>
        <span class="button-text">{{ 'Save' | translate }}</span>
      </ion-button>

      <ion-button fill="solid"
                  color="warning"
                  class="header-btn"
                  (click)="submitSTMR()"
                  [disabled]="!isSTMRComplete"
                  *ngIf="!isSTMRReadonly(stmrHeader)">
        <i class="fa-solid fa-paper-plane"></i>
        <span class="button-text">{{ 'Send' | translate }}</span>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>



<ion-content class="ion-padding">
  <!-- Details Section -->
  <div class="section-header">
    <h2>{{ 'Details' | translate }}</h2>
  </div>

  <div class="details-content">
<div class="details-grid">
  <!-- Row 1 -->
  <div class="detail-item">
    <span class="label">{{ 'Company #' | translate }}:</span>
    <span class="value">{{ stmrHeader?.COMPANY || '-' }}</span>
  </div>
<div class="detail-item">
    <span class="label">{{ 'Site #' | translate }}:</span>
    <span class="value">{{ stmrHeader?.RIG_NO || '' }}</span>
  </div>
  <div >
  </div>

  <!-- Row 2 -->
  <div class="detail-item">
    <span class="label">{{ 'STMR #' | translate }}:</span>
    <span class="value">{{ getSTMRIDForDisplay(stmrHeader?.STMR_ID || '') || 'New' }}</span>
  </div>
  <div class="detail-item">
    <span class="label">{{ 'Created' | translate }}:</span>
   <span class="value">{{ getFormattedShiftTime() }}</span>
  </div>
  <div class="detail-item">
  <span class="label" [ngClass]="{'error-text': !stmrHeader.SHIFT}">
    {{ 'Shift Type' | translate }}:
  </span>
  <div class="radio-group">
    <label class="radio-option">
      <input type="radio"
             [disabled]="isSTMRReadonly(stmrHeader)"
             color="primary"
             name="shiftType"
             value="NIGHT"
             [(ngModel)]="stmrHeader.SHIFT"
             (change)="onModelChange($event.target.value)">
      <span class="radio-label" [ngClass]="{'error-text': !stmrHeader.SHIFT}">{{ 'Night Shift' | translate }}</span>
    </label>
    <label class="radio-option">
      <input type="radio"
             [disabled]="isSTMRReadonly(stmrHeader)"
             color="primary"
             name="shiftType"
             value="DAY"
             [(ngModel)]="stmrHeader.SHIFT"
             (change)="onModelChange($event.target.value)">
      <span class="radio-label" [ngClass]="{'error-text': !stmrHeader.SHIFT}" >{{ 'Day Shift' | translate }}</span>
    </label>
  </div>
</div>


  <!-- Row 3 -->
  
   <div class="detail-item">
    <span class="label" [ngClass]="{'error-text': !stmrHeader.WELL_LOC}">{{ 'Location' | translate }}:</span>
    <input type="text" class="detail-input" [disabled]="isSTMRReadonly(stmrHeader)" [ngClass]="{'error-input': !stmrHeader.WELL_LOC}" [(ngModel)]="stmrHeader.WELL_LOC" placeholder="Location">
  </div>
  <div class="detail-item" (click)="openSelectCrew('CHAIRED_BY', stmrHeader.CHAIRED_BY, stmrHeader.CHAIRED_BY)">
  <span class="label" [ngClass]="{'error-text': !stmrHeader.CHAIRED_BY}">{{ 'Chaired By' | translate }}:</span>
  <div class="input-with-dropdown">
    <input type="text" class="detail-input" [disabled]="isSTMRReadonly(stmrHeader)" [ngClass]="{'error-input': !stmrHeader.CHAIRED_BY}" [(ngModel)]="stmrHeader.CHAIRED_BY" placeholder="Name">
    <ion-button [class.error]="!stmrHeader.CHAIRED_BY" fill="clear" *ngIf="!isSTMRReadonly(stmrHeader)" class="dropdown-btn" >
      <ion-icon name="chevron-down-outline"></ion-icon>
    </ion-button>
  </div>
</div>
  <div class="detail-item">
    <span class="label">{{ 'Operator' | translate }}:</span>
    <span class="value">{{ stmrHeader?.OPERATOR || '' }}</span>
  </div>
</div>
  </div>

<!-- Topics Section -->
<div class="section-header">
 <h2>{{ 'Topics' | translate }}<span *ngIf="(stmrTopicEntity.length - getNoOfDeletedTopics(stmrTopicEntity)) > 0"> ({{ (stmrTopicEntity.length - getNoOfDeletedTopics(stmrTopicEntity)) }})</span></h2>
  <ion-button fill="clear"
              class="add-btn"
              style="position: relative;"
              *ngIf="!isSTMRReadonly(stmrHeader)"
              (click)="addTopic()">
    <i class="fa-solid fa-circle-plus"></i>
    <ion-ripple-effect></ion-ripple-effect>
  </ion-button>
</div>

<div class="section-content">
  <div class="empty-message"    
       *ngIf="stmrTopicEntity.length === 0 || getNoOfDeletedTopics(stmrTopicEntity) === stmrTopicEntity.length"
        (click)="!isSTMRReadonly(stmrHeader) && addTopic()">
    <span class="error-text">{{ 'No Topics added.' | translate }}</span>
  </div>

  <div class="topics-list" *ngIf="stmrTopicEntity.length > 0">
    <div class="topic-item"
         *ngFor="let entity of stmrTopicEntity; let i = index"
         [class.readonly-state]="isSTMRReadonly(stmrHeader)"
         [style.display]="entity.topic.P_MODE === 'D' ? 'none' : 'flex'">
      
      <!-- Topic content area that's clickable for edit -->
      <div class="topic-content"  [class.readonly-content]="isSTMRReadonly(stmrHeader)" (click)="editTopic(entity, i)">
        <div class="topic-info">
          <!-- Topic name on first line -->
          <h3 class="topic-name">{{ entity.topic.TOPIC_NAME || 'Unnamed Topic' }}</h3>
          
          <!-- Description and time on second line -->
          <div class="topic-details">            
            <span class="topic-description" *ngIf="entity.topic.TOPIC_NOTE">
              {{ entity.topic.TOPIC_NOTE }}
            </span>
            <span class="topic-time" *ngIf="entity.topic.TOPIC_START">
              {{ dispTime(entity.topic.TOPIC_START) }}
            </span>
            <span class="topic-description no-description" *ngIf="!entity.topic.TOPIC_NOTE">
              {{ 'No description provided' | translate }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- Delete button with red background and rounded corners -->
      <ion-button fill="clear"
                  color="danger"
                  (click)="removeTopic($event, i); $event.stopPropagation()"
                  [disabled]="isSTMRReadonly(stmrHeader)"
                  class="delete-btn">
        <i class="fa-solid fa-trash-can" style="color: white;"></i>
        <ion-ripple-effect></ion-ripple-effect>
      </ion-button>
    </div>
  </div>
</div>



  <!-- Crew Section -->
  <div class="section-header">
    <h2>{{ 'Crew' | translate }}<span *ngIf="getNoOfNonThirdPartyCrewMembers(stmrCrew) > 0"> ({{ getNoOfNonThirdPartyCrewMembers(stmrCrew) }})</span></h2>
    <ion-button fill="clear"
                class="add-btn"
                *ngIf="!isSTMRReadonly(stmrHeader)"
                style="position: relative;"
                (click)="addCrew()">
      <i class="fa-solid fa-circle-user"></i>
      <ion-ripple-effect></ion-ripple-effect>
    </ion-button>
  </div>

  <div class="section-content">
    <div class="empty-message"
         *ngIf="getNoOfNonThirdPartyCrewMembers(stmrCrew) === 0"
         (click)="addCrew()">
      <span class="error-text">{{ 'No Crew members added.' | translate }}</span>
    </div>

    <div class="crew-list" *ngIf="getNoOfNonThirdPartyCrewMembers(stmrCrew) > 0">
  <div class="crew-item"
       *ngFor="let crew of stmrCrew"
       [class.readonly-state]="isSTMRReadonly(stmrHeader)"
       [style.display]="crew.CREW_TYPE === 'THIRD_PARTY' || crew.P_MODE === 'D' ? 'none' : 'block'">
    
    <!-- ADD this wrapper div -->
    <div class="crew-content">
      <div class="crew-info">
        <h3>{{ crew.CREW_NAME }}</h3>
        <p>{{ crew.CREW_POS }}</p>
      </div>
      
      <!-- MOVE signature-related content into new crew-signature div -->
      <div class="crew-signature">
        <ion-button *ngIf="!crew.CREW_SIGN"
                    class="signature-btn"
                    (click)="captureSign('CREW_SIGN', crew)">
          {{ 'Tap to Add Signature' | translate }}
        </ion-button>
        
        <div *ngIf="crew.CREW_SIGN" 
             class="signature-display" 
             [class.readonly-signature]="isSTMRReadonly(stmrHeader)"
             (click)="!isSTMRReadonly(stmrHeader) && captureSign('CREW_SIGN', crew)">
          <img [src]="crew.CREW_SIGN" alt="Crew Signature" class="signature-image" />
        </div>
    
    <!-- MOVE remove button outside crew-content, rename crew-actions to crew-remove -->
    <div class="crew-actions">
      <ion-button fill="clear"
                  color="medium"
                  class="remove-btn"
                  (click)="removeCrewMember(crew)"
                  *ngIf="!isSTMRReadonly(stmrHeader)">
        <ion-icon name="close-outline"></ion-icon>
        <ion-ripple-effect></ion-ripple-effect>
      </ion-button>
      </div>
      </div>
  </div>
  </div>
</div>
</div>

  <!-- 3rd Party Section -->
  <div class="section-header">
    <h2>{{ '3rd Party' | translate }}<span *ngIf="getNoOfThirdPartyCrewMembers(stmrCrew) > 0"> ({{ getNoOfThirdPartyCrewMembers(stmrCrew) }})</span></h2>
    <ion-button fill="clear"
                class="add-btn"
                *ngIf="!isSTMRReadonly(stmrHeader)"
                style="position: relative;"
                (click)="addThirdParty()">
      <i class="fa-solid fa-circle-plus"></i>
      <ion-ripple-effect></ion-ripple-effect>
    </ion-button>
  </div>

  <div class="section-content">
    <div class="empty-message">
      <div class="crew-list" *ngIf="getNoOfThirdPartyCrewMembers(stmrCrew) === 0">
      <span class="normal-text">{{ 'No third party crew members added.' | translate }}</span>
    </div>
</div>
    <div class="crew-list" *ngIf="getNoOfThirdPartyCrewMembers(stmrCrew) > 0">
      <div class="crew-item third-party-crew-item"
           *ngFor="let crew of stmrCrew; let i = index"
           [style.display]="crew.CREW_TYPE !== 'THIRD_PARTY' || crew.P_MODE === 'D' ? 'none' : 'block'">
        <div class="crew-input-section">
          <div class="crew-input-group">
            <input type="text"
                   class="detail-input crew-name-input"
                   [(ngModel)]="crew.CREW_NAME"
                   (ngModelChange)="onThirdPartyCrewNameChange(crew, i)"
                   placeholder="Name"
                   [disabled]="isSTMRReadonly(stmrHeader)">
          </div>
          <div class="crew-signature">
            <ion-button *ngIf="!crew.CREW_SIGN"
                        class="signature-btn"
                        (click)="captureSign('CREW_SIGN', crew)">
              {{ 'Tap to Add Signature' | translate }}
            </ion-button>
            <div *ngIf="crew.CREW_SIGN" 
                class="signature-display" 
                [class.readonly-signature]="isSTMRReadonly(stmrHeader)"
                (click)="!isSTMRReadonly(stmrHeader) && captureSign('CREW_SIGN', crew)">
              <img [src]="crew.CREW_SIGN" alt="Crew Signature" class="signature-image" />
            </div>
          
          <div class="crew-actions">  
            <ion-button fill="clear"
                        color="medium"
                        class="remove-btn"
                        (click)="removeCrewMember(crew)"
                        style="position: relative;"
                        *ngIf="!isSTMRReadonly(stmrHeader)">
              <ion-icon name="close-outline"></ion-icon>
              <ion-ripple-effect></ion-ripple-effect>
            </ion-button>
          </div>
        </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Crew Supervisor Section -->
  <div class="section-header">
    <h2>{{ 'Crew Supervisor' | translate }}</h2>
  </div>

  <div class="section-content supervisor-section">
    <div class="supervisor-input">
      <div class="input-with-dropdown">
        <input type="text"
               class="detail-input"
               [ngClass]="{'error-input': !stmrHeader.ONSITE_SUP}"
               [(ngModel)]="stmrHeader.ONSITE_SUP"
               (focus)="openSelectCrew('ONSITE_SUP', stmrHeader.ONSITE_SUP)"
               [disabled]="isSTMRReadonly(stmrHeader)"
               placeholder="Name"
               readonly>
        <ion-button fill="clear"
                    class="dropdown-btn"
                    *ngIf="!isSTMRReadonly(stmrHeader)"
                    [class.error]="!stmrHeader.ONSITE_SUP"
                    (click)="openSelectCrew('ONSITE_SUP', stmrHeader.ONSITE_SUP)">
          <ion-icon name="chevron-down-outline"></ion-icon>
        </ion-button>
      </div>
    </div>

    <div class="supervisor-signature">
      <ion-button *ngIf="!stmrHeader.ONSITE_SUP_SIGN"
                  class="signature-btn"
                  (click)="captureOnsiteSign('ONSITE_SUP_SIGN')">
        {{ 'Tap to Add Signature' | translate }}
      </ion-button>
      <div *ngIf="stmrHeader.ONSITE_SUP_SIGN" 
          class="signature-display" 
          [class.readonly-signature]="isSTMRReadonly(stmrHeader)"
          (click)="!isSTMRReadonly(stmrHeader) && captureOnsiteSign('ONSITE_SUP_SIGN')">
        <img [src]="stmrHeader.ONSITE_SUP_SIGN" alt="Supervisor Signature" class="signature-image" />
      </div>
    </div>
  </div>
</ion-content>
