  .thumbnail-tight {
    margin-right: 0px; // Reduce spacing between thumbnail and label
  }


  .icon-wrapper {
    position: relative;
    width: 2.5rem;
    height: 2.5rem;
  
    .base-icon {
      font-size: 2.1rem;
      color: #444;
    }
  
    .star-icon,
    .wifi-icon {
      position: absolute;
      font-size: 0.9rem;
      color: orange;
    }
  
    .star-icon {
      top: -0.3rem;
      right: -0.3rem;
      color: orange;
    }
  
    .wifi-icon {
      top: -0.3rem;
      left: -0.8rem;
      color: orange;
    }

  }

    .group-header {
    --background: #4a4a4a;
    color: white;
    font-weight: bold;
  }
  
  .group-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .icon-with-badge {
    position: relative;
  }
  
  .icon-with-badge ion-icon {
    font-size: 26px; /* adjust size */
  }
  
  .icon-with-badge .badge {
    position: absolute;
    top: -6px;
    right: 2px;
    font-size: 14px;
    min-width: 16px;
    height: 16px;
    border-radius: 50%;
    padding: 0 4px;
    background: var(--ion-color-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    left: 7px;
  }
  
ion-toolbar ion-button {
  // min-width: 100px;     /* choose the width you want */
  // height: 36px;         /* consistent height */
  justify-content: center;
  text-align: center;
  --padding-start: 8px;
  --padding-end: 8px;
}

ion-toolbar ion-button .badge {
  right: -4px;
}

ion-toolbar.main-toolbar {
  --background: var(--app-primary-green);
  --color: white;
}

ion-toolbar.main-toolbar .header-btn {
  --border-radius: 4px;
  --padding-start: 10px;
  --padding-end: 10px;
  height: 32px;
  font-size: 0.8rem;
  text-transform: none;
  --box-shadow: 0 2px 2px 0 rgba(0,0,0,0.14);
  margin: 0 4px;
}

ion-toolbar.main-toolbar .header-btn i {
  margin-right: 8px;
}

ion-toolbar.main-toolbar .header-btn .button-text {
  display: inline;
}

@media (max-width: 767px) {
  ion-toolbar.main-toolbar .header-btn .button-text {
    display: none !important;
  }
  
  ion-toolbar.main-toolbar .header-btn i {
    margin-right: 0;
  }
  
  ion-toolbar.main-toolbar .header-btn {
    --padding-start: 8px;
    --padding-end: 8px;
    min-width: 32px;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  ion-toolbar.main-toolbar .header-btn {
    font-size: 0.75rem;
  }
}

@media (min-width: 1024px) {
  .header-btn {
    font-size: 1rem;
  }
}




  .progress-container {
    transition: opacity 0.3s ease-in-out;
  }
