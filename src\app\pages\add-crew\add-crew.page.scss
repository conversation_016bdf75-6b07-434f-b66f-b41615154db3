.add-crew-header {
  --background: var(--ion-color-primary) !important;
}

.add-crew-toolbar {
  --background: var(--ion-color-primary) !important;
  --color: #fff !important;
}

.add-crew-title {
  color: #fff !important;
  font: 500 1.2rem/1.2;
}

.delete-button,
.add-button,
.save-button {
  --color: #fff !important;
  font: 500 0.9rem/1;
  margin: 0 4px;
}

.add-button ion-icon {
  color: #fff;
}

.add-crew-content {
  --background: #f5f5f5;
  padding: 0;
}

.contrast {
  --background: #000;
}

.contrast .crew-member-card {
  --background: #333;
  --color: #fff;
  border: 1px solid #555;
}

.contrast .crew-input {
  --background: #444;
  --color: #fff;
}

/* Main container using CSS Grid for responsive layout */
.crew-cards-container {
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(auto-fill, 360px); /* Fixed card width */
  gap: 16px;
  width: 100%;
  box-sizing: border-box;
}

/* Fallback for smaller screens - force single column on very small screens */
@media (max-width: 639px) {
  .crew-cards-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* Responsive breakpoints for different screen sizes */

/* Small tablets and large phones (landscape) */
@media (min-width: 576px) {
  .crew-cards-container {
    gap: 16px;
  }
}

/* Tablets */
@media (min-width: 768px) {
  .crew-cards-container {
    gap: 24px;
  }
}

/* Large tablets and small desktops */
@media (min-width: 992px) {
  .crew-cards-container {
    gap: 24px;
  }
}

/* Large desktops and wide screens */
@media (min-width: 1200px) {
  .crew-cards-container {
    gap: 16px;
  }
}

/* Extra large screens */
@media (min-width: 1400px) {
  .crew-cards-container {
    gap: 16px;
  }
}

.crew-member-card {
  --background: #fff;
  width: 360px;
  margin: 0;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 0;
  max-width: 100%;
}

.crew-member-card:hover {
  background-color: rgb(177, 212, 181);
}

.crew-distinct {
  border-left: 4px solid var(--ion-color-success);
}

.crew-card-content {
  padding: 16px;
}

/* Header with Add Crew label and close button */
.crew-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.add-crew-label {
  font-weight: 600;
  font-size: 1rem;
  color: rgb(194, 4, 4); /* red by default */
}

.add-crew-label.is-saved {
  color: black; /* black when saved */
}

.remove-x-btn {
  --color: grey;
  font-size: 1.1rem;
  padding: 0 8px;
  min-width: 32px;
  height: 32px;
}

/* Input container */
.crew-inputs {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Input styling */
.crew-input {
  --background: #f8f9fa;
  --border-color: #dee2e6;
  --border-radius: 6px;
  --padding-start: 12px;
  --padding-end: 12px;
  font-size: 0.9rem;
  width: 100%;
}

.crew-input.has-unsaved {
  color: rgb(191, 4, 4);
}

.crew-input:focus,
.input-field:focus {
  background-color: rgb(177, 212, 181);
}

.no-crew-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  text-align: center;
  margin-top: 50px !important;
}

.no-crew-message p {
  font: 500 1.1rem/1.4;
  margin: 0;
}

/* Placeholder styling */
.crew-input::placeholder {
  color: red; /* default red placeholder */
  opacity: 1; /* ensure visibility across browsers */
}

/* When crew is saved, make placeholder and text black */
.crew-input.is-saved::placeholder {
  color: black;
}

.crew-input.is-saved {
  color: black;
}

/* Actual input text always black */
.crew-input {
  color: black !important;
}

.has-unsaved {
  color: rgb(191, 4, 4);
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .crew-cards-container {
    gap: 12px;
  }
  
  .crew-card-content {
    padding: 12px;
  }
  
  .add-crew-title {
    font-size: 1rem;
  }
  
  .delete-button,
  .add-button,
  .save-button {
    font-size: 0.8rem;
  }
}

/* Additional responsive adjustments for very large screens */
@media (min-width: 1600px) {
  .crew-cards-container {
    padding: 32px;
    gap: 32px;
  }
} 