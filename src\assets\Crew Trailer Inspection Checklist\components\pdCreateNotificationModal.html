<div class="modal fade" id="createNotificationModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
	<div class="modal-dialog modal-dialog-centered modal-xl" role="document">
		<div class="modal-content">
		<div class="modal-header">
			<div class="row">
				<div class="col-sm-18">
					<h3 ng-show="$ctrl.model.addNotificationItem.Status == 'SCHEDULED'" class="modal-title" id="exampleModalCenterTitle">Create Notification</h3>
					<h3 ng-show="$ctrl.model.addNotificationItem.Status == 'CLOSED'" class="modal-title" id="exampleModalCenterTitle">Record Correction</h3>
				</div>
				<div class="col-sm-2">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-20">
					
					<h5 class="modal-title"> <span ng-show="$ctrl.model.addNotificationItem.Status == 'SCHEDULED'">Deficiency: </span>
						<span ng-show="$ctrl.model.addNotificationItem.Status == 'CLOSED'">Correction: </span>
						<span ng-bind-html="$ctrl.model.subTitle | trustAsHtml"></span>
					</h5>
				</div>
			</div>
		</div>
		<div class="modal-body">
			
			<form name="notificationForm" novalidate="">
			
				<div class="row" ng-show="$ctrl.model.addNotificationItem.Status == 'SCHEDULED'">
					<div class="col-sm-10">
						<div class="form-group" ng-class="{'has-error': notificationForm.notificationType.$invalid }">
							<label class="control-label">Notification Type</label>
							<select class="form-control" name="notificationType" id="notificationType" ng-model=$ctrl.model.addNotificationItem.NotificationType ng-options="item.NOTIF_TYPE as item.NOTIF_TYPE + ' - ' + item.SHORT_TEXT for item in $ctrl.model.lists.NOTIFICATION_TYPE | orderBy: item.NOTIF_TYPE " ng-required="$ctrl.model.addNotificationItem.Status == 'SCHEDULED'" ng-change="notificationTypeUpdated()">
							</select>
							<div class="help-block" ng-messages="notificationForm.notificationType.$error" role="alert">
								<div ng-message="required">Notification Type is required.</div>
							</div>
						</div>
					</div>
					<div class="col-sm-10">
						<div class="form-group" ng-class="{'has-error': notificationForm.codeGroup.$invalid }">
							<label class="control-label">Code Group</label>
							<select class="form-control" name="codeGroup" id="codeGroup"  ng-model="$ctrl.model.addNotificationItem.CodeGroup" data-ng-options="item.CODE_GROUP as item.CODE_GROUP + ' - ' + item.CODE_GROUP_TEXT for item in $ctrl.model.lists.NOTIFICATION_CODE_GROUP | filter: filterCodeGroupsByNotificationType | orderBy: item.CODE_GROUP" ng-required="$ctrl.model.addNotificationItem.Status == 'SCHEDULED'">
							</select>
							<div class="help-block" ng-messages="notificationForm.codeGroup.$error" role="alert">
								<div ng-message="required">Code Group is required.</div>
							</div>
						</div>
					</div>
				</div>
				<div class="row" ng-show="$ctrl.model.addNotificationItem.Status == 'SCHEDULED'">
					<div class="col-sm-10">
						<div class="form-group" ng-class="{'has-error': notificationForm.code.$invalid }">
							<label class="control-label">Code</label>
							<select class="form-control" name="code" id="exampleFormControlSelect1"  ng-model="$ctrl.model.addNotificationItem.Code" data-ng-options="item.CODE as item.CODE + ' - ' + item.CODE_TEXT for item in $ctrl.model.lists.NOTIFICATION_CODE | filter: filterCodesBySelectedCodeGroup | orderBy: item.CODE" ng-required="$ctrl.model.addNotificationItem.Status == 'SCHEDULED'">
							</select>
							<div class="help-block" ng-messages="notificationForm.code.$error" role="alert">
								<div ng-message="required">Code is required.</div>
							</div>
						</div>
					</div>
					<div class="col-sm-10" ng-class="{'has-error': notificationForm.priority.$invalid }">
						<div class="form-group">
							<label class="control-label">Priority</label>
							<select class="form-control" name="priority" id="exampleFormControlSelect1"  ng-model="$ctrl.model.addNotificationItem.Priority" ng-options="item.PRIORITY as item.PRIORITY + ' - ' + item.PRIORITY_DESC for item in $ctrl.model.lists.NOTIFICATION_PRIORITY | filter: filterPriorityBySelectedSelectedNotifType | orderBy: item.PRIORITY" ng-change="updateRequiredEndDate(event)" ng-required="$ctrl.model.addNotificationItem.Status == 'SCHEDULED'">
							</select>
							<div class="help-block" ng-messages="notificationForm.priority.$error" role="alert">
								<div ng-message="required">Priority is required.</div>
							</div>
						</div>
					</div>
				</div>

				<label ng-show="$ctrl.model.addNotificationItem.Status == 'SCHEDULED'" role="button" ng-click='handleAdvancedOptionClick()'><strong>Advanced Options </strong><span ng-class="{'glyphicon glyphicon-menu-right': $ctrl.model.hideAdvancedOptions, 'glyphicon glyphicon-menu-down': !$ctrl.model.hideAdvancedOptions}" aria-hidden="true"></span></label>

				<div class="row" ng-show="!$ctrl.model.hideAdvancedOptions && $ctrl.model.addNotificationItem.Status == 'SCHEDULED'">
					<div class="col-sm-10">
						<div class="form-group" ng-class="{'has-error': notificationForm.mainWorkCenter.$invalid }">
							<label class="control-label">Main Work Center</label>
							<select class="form-control" name="mainWorkCenter" id="exampleFormControlSelect1"  ng-model="$ctrl.model.addNotificationItem.MainWorkCenter" data-ng-options="item.WORK_CENTER as item.WORK_CENTER + ' - ' + item.WORK_CENTER_DESC for item in $ctrl.model.lists.NOTIFICATION_WORK_CENTER | orderBy: item.WORK_CENTER" ng-required="$ctrl.model.addNotificationItem.Status == 'SCHEDULED'">
							</select>
							<div class="help-block" ng-messages="notificationForm.mainWorkCenter.$error" role="alert">
								<div ng-message="required">Main Work Center is required.</div>
							</div>
						</div>
					</div>
					<div class="col-sm-10">
						<div class="form-group">
							<label class="control-label">2nd Work Center</label>
							<select class="form-control" name="secondaryWorkCenter" ng-disabled="$ctrl.model.addNotificationItem.NotificationType == 'S0'" id="exampleFormControlSelect1"  ng-model="$ctrl.model.addNotificationItem.SecondaryWorkCenter" data-ng-options="item.WORK_CENTER as item.WORK_CENTER + ' - ' + item.WORK_CENTER_DESC for item in $ctrl.model.lists.NOTIFICATION_WORK_CENTER | orderBy: item.WORK_CENTER">
							</select>
						</div>
					</div>
				</div>

				<div class="row" ng-show="!$ctrl.model.hideAdvancedOptions && $ctrl.model.addNotificationItem.Status == 'SCHEDULED'">
					<div class="col-sm-10">
						<div class="form-group"
							ng-class="{ 'has-error': notificationForm.requiredStart.$invalid }">
							<label class="control-label">Required Start</label>
							<input type="text" size="10" class="form-control" data-min-date='today'
								name="requiredStart" ng-readonly="submitted"
									ng-model="$ctrl.model.addNotificationItem.RequiredStartDate"
								data-date-format="dd-MMM-yyyy" bs-datepicker required ng-change="updateRequiredEndDate(event)" startDate='$ctrl.model.addNotificationItem.minRequiredStartDate' />
							<div class="help-block"
								ng-messages="notificationForm.requiredStart.$error"
								role="alert">
								<div ng-message="required">Start date is required.</div>
							</div>
						</div>
					</div>
					<div class="col-sm-10">
						<div class="form-group"
							ng-class="{ 'has-error': notificationForm.requiredEnd.$invalid }">
							<label class="control-label">Required End</label>
							<input type="text" size="10" class="form-control" data-min-date='today'
								name="requiredEnd" ng-readonly="submitted"
								ng-model="$ctrl.model.addNotificationItem.RequiredEndDate"
								data-date-format="dd-MMM-yyyy" bs-datepicker ng-required="$ctrl.model.addNotificationItem.Status == 'SCHEDULED'" />
							<div class="help-block"
								ng-messages="notificationForm.requiredEnd.$error"
								role="alert">
								<div ng-message="required">End date is required.</div>	
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-sm-20" ng-class="{'has-error': notificationForm.description.$invalid }">
						<div class="form-group">
							<label class="control-label">Reason</label>
							<textarea ng-show="$ctrl.model.addNotificationItem.Status == 'SCHEDULED'" class="form-control" name="description" ng-model="$ctrl.model.defaultReasonText" maxlength="500" ng-maxlength="500"	ng-disabled="submitted" ng-readonly="submitted"	placeholder="Add deficiency information" rows="5" required>
							
							</textarea>
							<textarea ng-show="$ctrl.model.addNotificationItem.Status == 'CLOSED'" class="form-control" name="description" ng-model="$ctrl.model.defaultReasonText" maxlength="500" ng-maxlength="500"	ng-disabled="submitted" ng-readonly="submitted"	placeholder="Add correction information" rows="5" required>
							
							</textarea>
							<div class="help-block" ng-messages="notificationForm.description.$error" role="alert">
								<div ng-message="required">Reason is required.</div>
							</div>
						</div>
					</div>
				</div>
			</form>
		</div>
		
		<div class="modal-footer">
			<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
			<button type="button" ng-disabled='!notificationForm.$valid' class="btn btn-primary" data-dismiss="modal" ng-click = '$ctrl.model.addNotificationItem.isUpdated = true; saveNotification()'>Save</button>
			</div>
		</div>
	</div>
</div>

