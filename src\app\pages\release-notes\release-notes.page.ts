import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonButtons, IonContent, IonHeader, IonMenuButton, IonRouterOutlet, IonTitle, IonToolbar, MenuController, IonButton, IonList, IonItem, IonListHeader, IonThumbnail, IonLabel, IonItemDivider } from '@ionic/angular/standalone';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AppConstants } from 'src/app/constants/appConstants';
import { RELEASE_NOTE_HEADER } from 'src/models/RELEASE_NOTE_HEADER';
import { UtilityService } from 'src/app/services/utility.service';
import { BusyIndicatorService } from 'src/app/services/busy-indicator.service';
import { AlertService } from 'src/app/services/alert.service';
import { UnviredCordovaSDK, RequestType, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { Store } from '@ngrx/store';
import { selectAllNotifications, selectRigData } from 'src/app/store/store.selector';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { TEMPLATE_HEADER } from 'src/models/TEMPLATE_HEADER';
import * as RigActions from 'src/app/store/store.actions';
import { DataService } from 'src/app/services/data.service';
import { ReleaseNotesService } from 'src/app/services/release-notes.service';
import { selectAllReleaseNotes } from 'src/app/store/store.selector';

@Component({
  selector: 'app-release-notes',
  templateUrl: './release-notes.page.html',
  styleUrls: ['./release-notes.page.scss'],
  standalone: true,
  imports: [ CommonModule, FormsModule , IonButtons, IonToolbar , IonMenuButton, IonHeader , IonContent, IonTitle, TranslateModule, IonButton, IonList, IonItem, IonListHeader, IonThumbnail, IonLabel,IonItemDivider]
})
export class ReleaseNotesPage implements OnInit, OnDestroy {
  appReleaseNotes: any[] = [];
  templateReleaseNotes: any[] = [];
  releaseNotesList: any[] = [];
  private notifSub?: Subscription;
  newInboxItemAvailable: number = 0;
  private releaseNotesLoaded: boolean = false;
  private loading: HTMLIonLoadingElement | null = null;
  private isLoading: boolean = false;
  private inboxSub?: Subscription;
  private rigDataValue: any;

  constructor(
    private routerOutlet: IonRouterOutlet,
    private menuCtrl: MenuController,
    private translate: TranslateService,
    private utilityService: UtilityService,
    private busy: BusyIndicatorService,
    private alert: AlertService,
    private unviredSdk: UnviredCordovaSDK,
    private store: Store,
    private router: Router,
    private dataService:DataService,
    private releaseNotesService:ReleaseNotesService,
  ) {
    this.routerOutlet.swipeGesture = false;
    this.menuCtrl.swipeGesture(true)
  }

  ngOnInit() {
    // this.loadReleaseNotes();
     this.store.select(selectAllReleaseNotes).subscribe(releaseNotes => {
    if (releaseNotes && releaseNotes.length > 0) {
      this.releaseNotesList = releaseNotes;
      this.appReleaseNotes = releaseNotes.filter(r => r.NOTE_TYPE === 'APP');
      this.templateReleaseNotes = releaseNotes.filter(r => r.NOTE_TYPE === 'TMPLT');
    }
  });
    // Listen to notification bus to refresh on inbox completion
     this.inboxSub = this.releaseNotesService.inboxCount$.subscribe(count => {
      this.newInboxItemAvailable = count;
    });
    this.notifSub = this.store.select(selectAllNotifications).subscribe(events => {
    const last = events?.[events.length - 1];
      if (!last) return;
      if (last.type === AppConstants.NOTIF_STATUS.INBOX_PROCESSING_COMPLETE) {
        setTimeout(() => {
          this.store.dispatch(RigActions.loadReleaseNotesFromDb());
           this.releaseNotesService.loadInboxCount();
         }, 500);
      }
    });
    this.releaseNotesService.loadInboxCount();
    this.store.select(selectRigData).subscribe(rigData => {
  this.rigDataValue = rigData;
});
  }

  ngOnDestroy(): void {
    this.notifSub?.unsubscribe();
  }

  async ionViewWillEnter() {
  }

 async ionViewWillLeave() {
  try {
    if (!this.releaseNotesList || this.releaseNotesList.length === 0) return;

    // debug: show distribution before update
    try {
      const before: any = await this.unviredSdk.dbExecuteStatement(
        `SELECT IS_READ, COUNT(*) as cnt FROM RELEASE_NOTE_HEADER GROUP BY IS_READ`
      );
      console.log('[ReleaseNotes] BEFORE distribution:', before);
    } catch (bErr) {
      console.warn('[ReleaseNotes] BEFORE distribution check failed:', bErr);
    }

    // normalized update - handles NULL/empty/"false"/0/case/spacing
    const updateQuery = `
      UPDATE RELEASE_NOTE_HEADER
      SET IS_READ = 'true'
      WHERE lower(trim(ifnull(IS_READ, ''))) != 'true'
    `;
    console.log('[ReleaseNotes] running update:', updateQuery.trim());

    const res: any = await this.unviredSdk.dbExecuteStatement(updateQuery);
    console.log('[ReleaseNotes] update result:', res);

   this.store.dispatch(RigActions.loadReleaseNotesFromDb());

      await this.releaseNotesService.loadInboxCount();

  } catch (e) {
    this.unviredSdk.logError('ReleaseNotes', 'ionViewWillLeave', `error: ${e}`);  
    console.error('[ReleaseNotes] ionViewWillLeave error:', e);
    }
  }

  // UI helpers
async presentLoading(message: string) {
  if (this.isLoading) return; 
  this.isLoading = true;
  await this.busy.displayBusyIndicator(message);
}

 async dismissLoading() {
  if (this.isLoading) {
    await this.busy.hideBusyIndicator();
    this.isLoading = false;
  }
}

  returnDisplayDate = (time: any, from?: string, to?: string) => this.utilityService.returnDisplayDate(time, from, to);

  async createForm(event: Event, releasenote: RELEASE_NOTE_HEADER) {
    event.preventDefault();
    try {
      if (!releasenote?.TMPLT_ID) return;
      const q = `SELECT 
    TH.*, 
    CH.DESCR AS CATEGORY_DESC, 
    TV.VER_ID AS L_VER_ID, 
    TV.VER_NO AS L_VER_NO, 
    TH.PBLSH_ON AS L_CRTD_ON, 
    TA.ATTACHMENT_STATUS AS ATT_STATUS
FROM TMPLT_HEADER TH
JOIN CATEGORY_HEADER CH 
    ON TH.CAT_ID = CH.CAT_ID
JOIN TMPLT_VER TV 
    ON TH.TMPLT_ID = TV.TMPLT_ID
JOIN TMPLT_ATTACHMENT TA 
    ON TA.TAG1 = TV.VER_ID
WHERE TV.STATUS = 'REL'
  AND TH.IS_ACTIVE = 'true'
  AND TH.TMPLT_ID = '${releasenote.TMPLT_ID}'
  AND TV.VER_NO = (
      SELECT MAX(VER_NO) 
      FROM TMPLT_VER 
      WHERE TMPLT_ID = TH.TMPLT_ID 
        AND STATUS = 'REL'
  );

'${releasenote.TMPLT_ID}'`;
      const res: any = await this.unviredSdk.dbExecuteStatement(q);
      if (res.type === ResultType.success && res.data?.length) {
        const template = res.data[0] as TEMPLATE_HEADER as any;
           if(template.IS_DATA_ENH == 'true'){
        if(navigator.onLine){
          let schedulerInput = await this.dataService.createSchedulerInput(this.rigDataValue, template);
          await this.dataService.makePACallToCreateOnlineForm(schedulerInput);
        } else {
          this.alert.showAlert(this.translate.instant('No Internet'), this.translate.instant('Please check your internet connection and try again.'));
          return;
        }
       } else { this.store.dispatch(RigActions.createForm({ template }));
        this.router.navigate(['/forms']);
      }
      }
    } catch (e) {
      // ignore
    }
  }

  navigateToDataEnhancedForm() {
    this.router.navigate(['/inbox']);
  }
}
