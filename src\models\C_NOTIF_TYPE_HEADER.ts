export class C_NOTIF_TYPE_HEADER {
    CATALOG_PROFILE: string;
    CAT_TYPE_ACTIVITY: string;
    CAT_TYPE_CAUSE: string;
    CAT_TYPE_CODING: string;
    CAT_TYPE_DAMAGE: string;
    CAT_TYPE_OBJECT_PARTS: string;
    CAT_TYPE_TASK: string;
    NOTIF_CATEGORY: string;
    NOTIF_TYPE: string;
    PRIORITY_TYPE: string;
    SHORT_TEXT: string;
    STATUS_PROFILE_NOTIF: string;
    STATUS_PROFILE_TASK: string;

    constructor(){
        this.CATALOG_PROFILE ='';
        this.CAT_TYPE_ACTIVITY = '';
        this.CAT_TYPE_CAUSE = '';
        this.CAT_TYPE_TASK = '';
        this.NOTIF_CATEGORY = '';
        this.CAT_TYPE_OBJECT_PARTS ='';
        this.NOTIF_TYPE ='';
        this.SHORT_TEXT = '';
        this.PRIORITY_TYPE ='';
        this.STATUS_PROFILE_NOTIF ='';
        this.STATUS_PROFILE_TASK = '';
        this.CAT_TYPE_CODING = '';
        this.CAT_TYPE_DAMAGE = '';
    }
}