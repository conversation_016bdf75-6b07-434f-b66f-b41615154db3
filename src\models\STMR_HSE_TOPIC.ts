import { STMR_TOPIC } from "./STMR_TOPIC";

export class STMR_HSE_TOPIC extends STMR_TOPIC {
    FORMS: string[] = []
    constructor(stmrTopic: STMR_TOPIC) {
        super()
        this.STMR_ID = stmrTopic.STMR_ID;
        this.TOPIC_NO = stmrTopic.TOPIC_NO;
        this.TOPIC_ID = stmrTopic.TOPIC_ID;
        this.TOPIC_NAME = stmrTopic.TOPIC_NAME;
        this.TOPIC_NOTE =  stmrTopic.TOPIC_NOTE;
        this.TOPIC_START = stmrTopic.TOPIC_START;
        this.STD_ID = stmrTopic.CTA_ID;
        this.P_MODE = stmrTopic.P_MODE;
    }
}