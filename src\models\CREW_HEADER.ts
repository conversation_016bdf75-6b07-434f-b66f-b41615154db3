import { STMR_ID_DS } from "./STMR";

export class CREW_HEADER extends STMR_ID_DS {
    USER_ID: string;
    USER_NAME: string;
    PERSON_NO: string;
    DESIGNATION: string;
    COMP_CODE: string;
    RIG_NO: string;
    EMAIL: string;
    MAINPH: string;
    CELPH: string;
    isSelected: boolean; //Custom
    CREW_SIGN: string; //Custom
    SOURCE : string; 
    userNameUnsaved : boolean;//Custom
    designationUnsaved : boolean;//Custom
    crewIdDistinct :boolean;//Custom
    saved : boolean;
    GLOBALID : string;
    index: string;
    IS_MANAGER: string;

    constructor(){
        super();
        this.USER_ID = '';
        this.USER_NAME = '';
        this.PERSON_NO = '';
        this.DESIGNATION = '';
        this.COMP_CODE = '';
        this.RIG_NO = '';
        this.EMAIL = '';
        this.MAINPH = '';
        this.CELPH = '';    
        this.isSelected = false;
        this.CREW_SIGN = '';
        this.SOURCE = '';
        this.userNameUnsaved = false;
        this.designationUnsaved = false;
        this.crewIdDistinct = false;
        this.saved = false;
        this.GLOBALID = '';
        this.index = '';
        this.IS_MANAGER = '';
    
    
    }
}