<!DOCTYPE html>
<html id="formApp" ng-app="PDTest" ng-cloak="" ng-controller="PSCtrl">

<head>
	<meta charset="utf-8" />
	<meta content="width=device-width, initial-scale=1.0, user-scalable=0" name="viewport" />
	<title>
		{{form.title}}
	</title>
	<!-- smoosh -->
	<link href="./styles/bootstrap.min.css" rel="stylesheet" />
	<link href="./styles/pdstylenormal.css" rel="stylesheet" />
	<link href="./styles/form.css" rel="stylesheet">
	<link href="./styles/responsive.css" rel="stylesheet">
	<!-- endsmoosh -->
	</link>
</head>

<body>
	<!-- ICONS -->
	<svg aria-hidden="true" style="position: absolute; width: 0; height: 0; overflow: hidden;" version="1.1"
		xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
		<!-- ARROW BACK -->
		<defs>
			<symbol id="icon-ion-ios-arrow-back" viewbox="0 0 12 32">
				<title>
					ion-ios-arrow-back
				</title>
				<path d="M12 8l-7.938 8 7.938 8-2 2-10-10 10-10z">
				</path>
			</symbol>
			<!-- ION GEAR -->
			<symbol id="icon-ion-gear-a" viewbox="0 0 24 32">
				<title>
					ion-gear-a
				</title>
				<path
					d="M21.688 16c0 1.5 0.938 2.688 2.313 3.5-0.25 0.813-0.625 1.625-1 2.375-1.563-0.375-2.813 0.188-3.875 1.25s-1.375 2.313-1 3.875c-0.75 0.375-1.563 0.75-2.375 1-0.813-1.375-2.25-2.313-3.75-2.313s-2.938 0.938-3.75 2.313c-0.813-0.25-1.688-0.625-2.438-1 0.375-1.563 0.125-2.813-0.938-3.875s-2.313-1.313-3.875-0.938c-0.375-0.75-0.75-1.625-1-2.438 1.375-0.813 2.313-2.25 2.313-3.75s-0.938-2.688-2.313-3.5c0.25-0.813 0.563-1.625 1-2.375 1.563 0.375 2.813-0.188 3.875-1.25s1.313-2.313 0.938-3.875c0.75-0.375 1.625-0.75 2.438-1 0.813 1.375 2.25 2.313 3.75 2.313s2.938-0.938 3.75-2.313c0.813 0.25 1.688 0.625 2.438 1-0.375 1.563-0.125 2.813 0.938 3.875s2.313 1.625 3.875 1.25c0.375 0.75 0.75 1.563 1 2.375-1.375 0.813-2.313 2-2.313 3.5zM12 22.125c3.375 0 6.125-2.75 6.125-6.125s-2.75-6.125-6.125-6.125-6.125 2.75-6.125 6.125 2.75 6.125 6.125 6.125z">
				</path>
			</symbol>
			<!-- ARROW LEFT -->
			<symbol id="icon-ion-ios-arrow-left" viewbox="0 0 12 32">
				<title>
					ion-ios-arrow-left
				</title>
				<path d="M12 7.188l-9.375 8.813 9.375 8.813-1.313 1.188-10.688-10 10.688-10z">
				</path>
			</symbol>
			<!-- ION CHECKMARK -->
			<symbol id="icon-ion-checkmark" viewbox="0 0 26 32">
				<title>
					ion-checkmark
				</title>
				<path
					d="M25.875 6.875c0.063 0.125 0.125 0.25 0.125 0.375s-0.063 0.25-0.125 0.375l-15.563 20c-0.125 0.125-0.313 0.375-0.563 0.375s-0.5-0.188-0.625-0.313-8.813-8.5-8.813-8.5l-0.188-0.188c-0.063-0.125-0.125-0.25-0.125-0.375s0.063-0.188 0.125-0.313l0.125-0.125c0.875-0.938 2.625-2.75 2.75-2.875s0.25-0.375 0.5-0.375 0.5 0.25 0.625 0.375 5 4.875 5 4.875l12.5-16.063c0.125-0.125 0.25-0.125 0.375-0.125s0.313 0.063 0.438 0.125z">
				</path>
			</symbol>
			<!-- Air-Plane -->
			<symbol id="icon-ion-paper-airplane" viewbox="0 0 32 32">
				<title>
					ion-paper-airplane
				</title>
				<path
					d="M0 17l28-15-7 28-7-7-5 7-1-10zM19.938 26.063l5-20.188-20.125 10.813 5.188 1.938 12-9.625-8 11z">
				</path>
			</symbol>
			<!-- CALENDER -->
			<symbol id="icon-ion-calendar" viewbox="0 0 32 32">
				<title>
					ion-calendar
				</title>
				<path
					d="M7 8c-1.125 0-2-0.875-2-2v-2c0-1.125 0.875-2 2-2s2 0.875 2 2v2c0 1.125-0.875 2-2 2zM21 8c-1.125 0-2-0.875-2-2v-2c0-1.125 0.875-2 2-2s2 0.875 2 2v2c0 1.125-0.875 2-2 2zM27.5 4c0.25 0 0.5 0.25 0.5 0.5v25c0 0.25-0.25 0.5-0.5 0.5h-27c-0.25 0-0.5-0.25-0.5-0.5v-25c0-0.25 0.25-0.5 0.5-0.5h3.5v2.563c0 1.375 1.5 2.438 3 2.438s3-1.063 3-2.438v-2.563h8v2.563c0 1.375 1.563 2.438 3.063 2.438s2.938-1.063 2.938-2.438v-2.563h3.5zM25 27v-16h-22v16h22z">
				</path>
			</symbol>
			<!-- CLOCK -->
			<symbol id="icon-ion-clock" viewbox="0 0 32 32">
				<title>
					ion-clock
				</title>
				<path
					d="M14 2c7.75 0 14 6.25 14 14s-6.25 14-14 14-14-6.25-14-14 6.25-14 14-14zM22.5 24.5c0.625-0.625 1.188-1.313 1.625-2.063l-1.688-1 0.5-0.875 1.688 1c0.813-1.563 1.313-3.25 1.375-5.063h-2v-1h2c-0.063-1.813-0.563-3.5-1.375-5.063l-1.688 1-0.5-0.875 1.688-1c-0.438-0.75-1-1.438-1.625-2.063s-1.313-1.188-2.063-1.625l-1 1.688-0.875-0.5 1-1.688c-1.563-0.813-3.25-1.313-5.063-1.375v2h-1v-2c-1.813 0.063-3.5 0.563-5.063 1.375l1 1.688-0.875 0.5-1-1.688c-0.75 0.438-1.438 1-2.063 1.625s-1.188 1.313-1.625 2.063l1.688 1-0.5 0.875-1.688-1c-0.813 1.563-1.313 3.25-1.375 5.063h2v1h-2c0.063 1.813 0.563 3.5 1.375 5.063l1.688-1 0.5 0.875-1.688 1c0.438 0.75 1 1.438 1.625 2.063s1.313 1.188 2.063 1.625l1-1.688 0.875 0.5-1 1.688c1.563 0.813 3.25 1.313 5.063 1.375v-2h1v2c1.813-0.063 3.5-0.563 5.063-1.375l-1-1.688 0.875-0.5 1 1.688c0.75-0.438 1.438-1 2.063-1.625zM23 15.063v2h-7.313c-0.375 0.563-1 0.938-1.688 0.938-1.125 0-2-0.875-2-2 0-0.75 0.375-1.375 1-1.75v-4.25h2v4.25c0.313 0.188 0.563 0.5 0.75 0.813h7.25z">
				</path>
			</symbol>
			<!-- ADD CIRCLE -->
			<symbol id="icon-ion-android-add-circle" viewbox="0 0 26 32">
				<title>
					ion-android-add-circle
				</title>
				<path
					d="M13 3c7.188 0 13 5.813 13 13s-5.813 13-13 13-13-5.813-13-13 5.813-13 13-13zM19.688 17.313v-2.625h-5.375v-5.375h-2.625v5.375h-5.375v2.625h5.375v5.375h2.625v-5.375h5.375z">
				</path>
			</symbol>
			<!-- OUTLINE CHECKBOX -->
			<symbol id="icon-ion-android-checkbox-outline-blank" viewbox="0 0 24 32">
				<title>
					ion-android-checkbox-outline-blank
				</title>
				<path
					d="M21.313 6.688h-18.625v18.625h18.625v-18.625zM21.313 4v0c1.438 0 2.688 1.25 2.688 2.688v18.625c0 1.438-1.25 2.688-2.688 2.688h-18.625c-1.438 0-2.688-1.25-2.688-2.688v-18.625c0-1.438 1.25-2.688 2.688-2.688h18.625z">
				</path>
			</symbol>
			<!-- CHECKBOX -->
			<symbol id="icon-ion-android-checkbox" viewbox="0 0 24 32">
				<title>
					ion-android-checkbox
				</title>
				<path
					d="M21.313 4c1.438 0 2.688 1.25 2.688 2.688v18.625c0 1.438-1.25 2.688-2.688 2.688h-18.625c-1.438 0-2.688-1.25-2.688-2.688v-18.625c0-1.438 1.25-2.688 2.688-2.688h18.625zM9.313 22.688l12-12-1.875-1.875-10.125 10.125-4.75-4.813-1.875 1.875z">
				</path>
			</symbol>
			<!-- RADIO BUTTON OFF -->
			<symbol id="icon-ion-android-radio-button-off" viewbox="0 0 26 32">
				<title>
					ion-android-radio-button-off
				</title>
				<path
					d="M13 3c7.125 0 13 5.875 13 13s-5.875 13-13 13-13-5.875-13-13 5.875-13 13-13zM13 26.375c5.75 0 10.375-4.625 10.375-10.375s-4.625-10.375-10.375-10.375-10.375 4.625-10.375 10.375 4.625 10.375 10.375 10.375z">
				</path>
			</symbol>
			<!-- RADIO BUTTON ON -->
			<symbol id="icon-ion-android-radio-button-on" viewbox="0 0 26 32">
				<title>
					ion-android-radio-button-on
				</title>
				<path
					d="M13 9.5c3.563 0 6.5 2.938 6.5 6.5s-2.938 6.5-6.5 6.5-6.5-2.938-6.5-6.5 2.938-6.5 6.5-6.5zM13 3c7.125 0 13 5.875 13 13s-5.875 13-13 13-13-5.875-13-13 5.875-13 13-13zM13 26.375c5.75 0 10.375-4.625 10.375-10.375s-4.625-10.375-10.375-10.375-10.375 4.625-10.375 10.375 4.625 10.375 10.375 10.375z">
				</path>
			</symbol>
			<!-- ION CLOSE -->
			<symbol id="icon-ion-close" viewbox="0 0 24 32">
				<title>
					ion-close
				</title>
				<path
					d="M23.75 24.188c0.188 0.188 0.25 0.375 0.25 0.625s-0.063 0.438-0.25 0.625l-2.375 2.313c-0.188 0.188-0.313 0.25-0.563 0.25s-0.438-0.063-0.625-0.25l-8.188-8.188-8.188 8.188c-0.188 0.188-0.313 0.25-0.563 0.25s-0.438-0.063-0.625-0.25l-2.375-2.313c-0.188-0.188-0.25-0.375-0.25-0.625s0.063-0.438 0.25-0.625l8.25-8.188-8.25-8.125c-0.313-0.313-0.313-0.938 0-1.25l2.313-2.375c0.188-0.125 0.375-0.25 0.625-0.25s0.438 0.063 0.625 0.25l8.188 8.125 8.188-8.125c0.188-0.125 0.375-0.25 0.625-0.25s0.438 0.063 0.625 0.25l2.313 2.375c0.313 0.313 0.313 0.875 0 1.188l-8.25 8.125z">
				</path>
			</symbol>
			<!-- ION WENCH -->
			<symbol id="icon-ion-wrench" viewbox="0 0 32 32">
				<title>
					ion-wrench
				</title>
				<path
					d="M26.875 7.188c0.875 0.875 1.188 1.938 1.125 2.75s-0.375 2.438-2 4.063-4.813 2.625-7.063 1.5c-0.313-0.188-0.813-0.313-1.313 0.188-0.625 0.563-12.625 13.438-12.625 13.438-1.063 1.188-3 1.125-4.125 0s-1.188-3.063 0-4.125c0 0 12.938-12.125 13.438-12.625s0.313-1 0.188-1.375c-1.25-2.938 0.125-5.563 1.5-7 1.375-1.5 3.188-1.938 4.063-2 0.813-0.063 1.938 0.313 2.75 1.125l-3.563 3.5 0.625 3.5 3.438 0.625zM3.688 27.688c0.375-0.375 0.375-1.063 0-1.438s-1-0.375-1.375 0-0.375 1.063 0 1.438 1 0.375 1.375 0z">
				</path>
			</symbol>
			<symbol id="icon-ion-chevron-left" viewbox="0 0 16 32">
				<title>
					ion-chevron-left
				</title>
				<path
					d="M5.375 16l10.438 10.875c0.25 0.25 0.25 0.75 0 1l-1.875 1.938c-0.25 0.25-0.75 0.25-1 0l-12.75-13.313c-0.125-0.125-0.188-0.313-0.188-0.5s0.063-0.375 0.188-0.5l12.75-13.313c0.25-0.25 0.75-0.188 1 0.063l1.875 1.875c0.25 0.25 0.25 0.75 0 1l-10.438 10.875z">
				</path>
			</symbol>
			<symbol id="icon-ion-ios-paperplane" viewbox="0 0 20 32">
				<title>
					ion-ios-paperplane
				</title>
				<path d="M0 15.625l20-9.625-13.375 12.5zM20 6l-9.125 20-3.25-6.5z">
				</path>
			</symbol>
			<symbol id="icon-ion-plus-circled" viewbox="0 0 32 32">
				<title>
					ion-plus-circled
				</title>
				<path
					d="M14 2c7.75 0 14 6.25 14 14s-6.25 14-14 14-14-6.25-14-14 6.25-14 14-14zM22 17v-2h-7v-7h-2v7h-7v2h7v7h2v-7h7z">
				</path>
			</symbol>
			<symbol id="icon-ion-android-remove-circle" viewbox="0 0 26 32">
				<title>
					ion-android-remove-circle
				</title>
				<path
					d="M13 3c7.188 0 13 5.813 13 13s-5.813 13-13 13-13-5.813-13-13 5.813-13 13-13zM19.688 17.313v-2.625h-13.375v2.625h13.375z">
				</path>
			</symbol>
		</defs>
	</svg>
	<!-- Test buttons -->
	<pd-test-buttons></pd-test-buttons>
	<!-- navbar -->
	<pd-nav-bar></pd-nav-bar>

	<!-- Content -->
	<div class="container-fluid">
		<!-- START: Form Container -->
		<div class="form-container">

			<!-- Common form title -->
			<pd-form-title></pd-form-title>


			<!-- START: Form Container.Hidden from print -->
			<div class="hidden-print">
				<form name="contentForm" novalidate="">
					<!-- Common form header -->
					<pd-form-header model="data.CrewTrailerNew"></pd-form-header>
					<div class="row" id="topSection">
						<div class="col-sm-5">


						</div>
					</div>
					<!-- monthly/new location -->
					<div id="section1"></div>
					<div ng-show="data.CrewTrailerNew.InspType !== undefined">
						<div ng-init="section_table = getSectionTable(data.CrewTrailerNew.InspType)"
							ng-repeat="group in getGroupsForSection(data.CrewTrailerNew.InspType)">

							<hr />
							<!-- Legend for Mobile -->
							<div class="mobile-only">
								<div class="well btn-group-justified">
									<h5 class="text-success text-center">
										<svg class="icon icon-ion-checkmark">
											<use xlink:href="#icon-ion-checkmark"></use>
										</svg> Satisfactory
									</h5>
									<h5 class="text-danger text-center">
										<svg class="icon icon-ion-close">
											<use xlink:href="#icon-ion-close"></use>
										</svg> Deficiency
									</h5>
									<h5 class="text-warning text-center">
										<svg class="icon icon-ion-wrench">
											<use xlink:href="#icon-ion-wrench"></use>
										</svg> Corrected
									</h5>
								</div>
							</div>

							<div class="table-responsive">
								<table class="table table-bordered table-striped"
									ng-class="{'table-borderless': !data[section_table][0][group.prop]}">
									<thead class="show-header"
										ng-class="{'thead-inverse': data[section_table][0][group.prop]}">
										<td class="col-sm-12">
											{{group.groupName}}
										</td>
										<td class="col-sm-8">
											<button class="btn btn-group-justified"
												ng-class="{'btn-default': data[section_table][0][group.prop], 'btn-primary': !data[section_table][0][group.prop]}"
												ng-click="updateGroup(group, data.CrewTrailerNew.InspType)"
												ng-disabled="submitted" ng-hide="submitted" ng-readonly="submitted">
												<span ng-hide="!data[section_table][0][group.prop]">
													Remove this section
												</span>
												<span ng-show="!data[section_table][0][group.prop]">
													Add this section
												</span>
											</button>
										</td>
									</thead>
									<tbody ng-show="data[section_table][0][group.prop]">
										<!-- this repeater works on 1,2,3 -->
										<tr ng-class="{ 'danger': (!data[section_table][0][item.ctrl])}"
											ng-repeat="item in getLineItemsForGroup(group.groupName, data.CrewTrailerNew.InspType, [1,2]) track by $index">
											<td>
												<span ng-bind-html="item.label | trustAsHtml"></span>
											</td>
											<td>
												<!-- Button input -->
												<div ng-if="item.dataType === 1">
													<div class="btn-group btn-group-justified">
														<div class="btn-group">
															<button class="btn btn-default"
																ng-class="{'btn-success': data[section_table][0][item.ctrl] == 'Yes'}"
																ng-click="deleteNotification(form.title,item.label,'',data[section_table][0][item.ctrl]);data[section_table][0][item.ctrl] = 'Yes'; checkAllItemsSelected(data.CrewTrailerNew.InspType)"
																ng-disabled="submitted" ng-readonly="submitted"
																type="button">
																<svg class="icon icon-ion-checkmark">
																	<use xlink:href="#icon-ion-checkmark">
																	</use>
																</svg>
															</button>
														</div>
														<div class="btn-group">
															<button data-placement="top" data-toggle="tooltip"
																id="{{group.prop}}bx{{$index}}"
																ng-mouseenter="show_tooltip(group.prop+'bx'+$index)"
																ng-mouseleave="dismiss_tooltip(group.prop+'bx'+$index)"
																title="This is used when an identified deficiency cannot be resolved during your shift, and it will create an actionable item for the Rig Manager."
																class="btn btn-default" data-toggle="dropdown"
																ng-class="{'btn-danger': data[section_table][0][item.ctrl] == 'Deficiency'}"
																ng-click="showCreateNotificationPopup(section_table, item, 'Deficiency', 'SCHEDULED'); "
																ng-disabled="submitted" ng-readonly="submitted"
																type="button">
																<svg class="icon icon-ion-close">
																	<use xlink:href="#icon-ion-close">
																	</use>
																</svg>
																<span
																	class="hide-on-mobile">&nbsp;&nbsp;&nbsp;Deficiency</span>
															</button>
														</div>
														<div class="btn-group">
															<button data-placement="top" data-toggle="tooltip"
																id="{{group.prop}}by{{$index}}"
																ng-mouseenter="show_tooltip(group.prop+'by'+$index)"
																ng-mouseleave="dismiss_tooltip(group.prop+'by'+$index)"
																title="This is used when an identified deficiency was corrected immediately, and it will log a closed item on the Corrective Action Register for reporting and follow-up."
																class="btn btn-default" data-toggle="dropdown"
																ng-class="{'btn-warning': data[section_table][0][item.ctrl] == 'Corrected'}"
																ng-click="showCreateNotificationPopup(section_table, item, 'Corrected', 'CLOSED'); "
																ng-disabled="submitted" ng-readonly="submitted"
																type="button">
																<svg class="icon icon-ion-wrench">
																	<use xlink:href="#icon-ion-wrench">
																	</use>
																</svg>
																<span
																	class="hide-on-mobile">&nbsp;&nbsp;&nbsp;Corrected</span>
															</button>
														</div>
														<div class="btn-group" ng-show="item.allowNA">
															<button class="btn btn-default"
																ng-class="{'btn-dark': data[section_table][0][item.ctrl] == 'NA'}"
																ng-click="deleteNotification(form.title,item.label,'',data[section_table][0][item.ctrl]); data[section_table][0][item.ctrl] = 'NA'; checkAllItemsSelected(data.CrewTrailerNew.InspType)"
																ng-disabled="submitted" ng-readonly="submitted"
																type="button">
																<span>
																	N/A
																</span>
															</button>
														</div>
													</div>
												</div>


											</td>
										</tr>
										<tr
											ng-repeat="item in getLineItemsForGroup(group.groupName, data.CrewTrailerNew.InspType, [2]) track by $index">
											<td class="col-sm-19" colspan="3">
												<span>Comments</span>
												<textarea class="form-control" rows="4"
													ng-model="data[section_table][0][item.ctrl]" ng-readonly="submitted"
													ng-disabled="submitted" ng-maxlength="1000" maxlength="1000">
											</textarea>
											</td>
										</tr>


									</tbody>
								</table>
							</div>
						</div>
					</div>
					<!-- end monthly/new location -->


					<!-- Comments -->

					<!-- Comments -->
					<!-- comments -->
					<div class="table-responsive" id="section2">
						<table class="table table-borderless">
							<thead class="thead-inverse show-header">
								<tr>
									<div class="row">
										<th class="col-sm-19" colspan="3">
											<label class="control-label">Additional Comments</label>
										</th>
									</div>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>
										<textarea id="comment" class="form-control" name="comment" maxlength="1000"
											ng-maxlength="1000" ng-disabled="submitted" ng-readonly="submitted"
											ng-model="data.CrewTrailerNew.AdditionalComments" rows="4">
                                    </textarea>
										<span class="text-muted">
											{{1000-data.CrewTrailerNew.AdditionalComments.length}} characters
											remaining
										</span>
									</td>
								</tr>
							</tbody>
						</table>
					</div>

					<br>

					<!-- End Comments -->

					<!-- Notifications -->
					<div id="section4"></div>
					<pd-notification model="data.FormNotification"></pd-notification>
					<!-- END: FORM -->
				</form>
			</div>

			<!-- START: Print area.  -->
			<div class="visible-print">
				<hr>
				<pd-form-header-print model="data.CrewTrailerNew"></pd-form-header-print>
				<!-- Inspection Items -->
				<hr>
				<!-- Legend -->
				<br>
				<div class="well">
					<div class="row">
						<div class="col-sm-5">
							<h5 class="text-success text-center"><svg class="icon icon-ion-checkmark">
									<use xlink:href="#icon-ion-checkmark"></use>
								</svg></span>&nbsp;&nbsp;Satisfactory</h5>
						</div>
						<div class="col-sm-5">
							<h5 class="text-danger text-center"><svg class="icon icon-ion-close">
									<use xlink:href="#icon-ion-close"></use>
								</svg></span>&nbsp;&nbsp;Deficiency</h5>
						</div>
						<div class="col-sm-5">
							<h5 class="text-warning text-center"><svg class="icon icon-ion-wrench">
									<use xlink:href="#icon-ion-wrench"></use>
								</svg></span>&nbsp;&nbsp;Corrected</h5>
						</div>
						<div class="col-sm-5">
							<h5 class="text-muted text-center">N/A</span>&nbsp;&nbsp;Not Applicable</h5>
						</div>
					</div>
				</div>
				<br>


				<!-- monthly/new location -->
				<div ng-show="data.CrewTrailerNew.InspType !== undefined">
					<div ng-init="section_table = getSectionTable(data.CrewTrailerNew.InspType)"
						ng-repeat="group in getGroupsForSection(data.CrewTrailerNew.InspType)">

						<div style="margin-bottom:10px">

							<div class="table-responsive">
								<table class="table table-striped">
									<thead ng-class="{'thead-inverse': data[section_table][0][group.prop]}">
										<td colspan="6">{{group.groupName}}<span
												ng-show="!data[section_table][0][group.prop]"> (Section
												removed)</span>
										</td>
									</thead>
									<tbody ng-show="data[section_table][0][group.prop]">
										<!-- this repeater works on 1,2,3 -->

										<tr ng-repeat="row_i in getRowRepeaterForPrint(group.groupName, data.CrewTrailerNew.InspType, [1,2])"
											ng-init="row_items = getLineItemsForRowForPrint(group.groupName, data.CrewTrailerNew.InspType, [1,2], row_i, 3)">
											<td ng-repeat="item in row_items" style="width:16.66%"
												ng-class="{ 'table-bordered-print': ($index+2) % 3 === 0 }">
												<div class="row">

													<span class="col-sm-17"
														ng-bind-html="item.label | trustAsHtml">hihi</span>

													<div class="col-sm-3">



														<!-- Button input -->
														<div>
															<div ng-if="data[section_table][0][item.ctrl] === 'Yes'">
																<svg class="icon icon-ion-checkmark">
																	<use xlink:href="#icon-ion-checkmark"></use>
																</svg>
															</div>
															<div
																ng-if="data[section_table][0][item.ctrl] === 'Deficiency'">
																<svg class="icon icon-ion-close">
																	<use xlink:href="#icon-ion-close"></use>
																</svg>
															</div>
															<div
																ng-if="data[section_table][0][item.ctrl] === 'Corrected'">
																<svg class="icon icon-ion-wrench">
																	<use xlink:href="#icon-ion-wrench"></use>
																</svg>
															</div>
															<div ng-if="data[section_table][0][item.ctrl] === 'NA'">N/A
															</div>
															<div ng-if="data[section_table][0][item.ctrl] === ''">
																<svg
																	class="icon icon-ion-android-checkbox-outline-blank">
																	<use
																		xlink:href="#icon-ion-android-checkbox-outline-blank">
																	</use>
																</svg>
															</div>
														</div>
													</div>
												</div>
											</td>
										</tr>

										<tr
											ng-repeat="item in getLineItemsForGroup(group.groupName, data.CrewTrailerNew.InspType, [2]) track by $index">
											<td class="col-sm-19" colspan="3">
												<div class="form-group col-sm-20">
													<label class="control-label">Comments</label>
													<br>
													<!-- <pre><span style="font-size: 10px; font-style:normal">{{data[section_table][0][item.ctrl]}}</span></pre> -->
													<pre><span style="font-size: 10px; font-style:normal;white-space:normal;">{{data[section_table][0][item.ctrl]}}</span></pre>
													<!-- Use for print view only -->
													<!-- <label class="control-label print-text-area-lg"></label> -->

												</div>
												</textarea>
											</td>
										</tr>

									</tbody>
								</table>
							</div>
						</div>
					</div>
					<!-- end monthly/new location -->

					<!-- Comments Table- Not for Print- used to populate Notification table -->
					<!-- End Comments -->

					<!-- Comments-USED for Blank PDF ONLY -->
					<!-- <div class="table-responsive">
						<table class="table table-borderless">
							<thead class="thead-inverse">
								<tr>
									<div class="row">
										<td class="col-sm-12" colspan="3">Comments</td>
										<td class="col-sm-8 text-center"></td>
									</div>
								</tr>
							</thead>
						</table>
					</div>
					<div>
						<label class="control-label" id="notifySection">Enter Comments/Concerns</label>
						<label class="print-text-area"></label>
					</div> -->

					<div class="table-responsive">
						<table class="table table-bordered">
							<thead class="thead-inverse">
								<td class="col-sm-20"> Additional Comments</td>
							</thead>
							<tbody>
								<tr>
									<td>
										<label
											class="print-text-area-lg">{{data.CrewTrailerNew.AdditionalComments}}</label>
									</td>
								</tr>
							</tbody>
						</table>
					</div>

					<!-- End Comments -->

					<hr />

					<!-- Notifications -->
					<pd-notification-print model="data.FormNotification"></pd-notification-print>
					<!-- USE for Blank PDF ONLY -->
					<!-- <pd-Notification-Print model="data.FormNotification" ng-hide="data.FormNotification.length =='0'">
					</pd-Notification-Print> -->
					<hr>
				</div>
			</div>
			<!--	</div> -->
			<!-- </div> -->

			<!-- END: Hidden from print -->

		</div>
		<!-- Common footer -->
		<pd-footer model="data.CrewTrailerNew"></pd-footer>

		<!-- END: Form Container -->
		<pd-skip-modal>
		</pd-skip-modal>
		<!-- Signature Modal -->
		<pd-signature-modal>
		</pd-signature-modal>
		<!-- Confim Modal -->
		<pd-confirm-modal>
		</pd-confirm-modal>
		<pd-confirm-data-loss-modal>
		</pd-confirm-data-loss-modal>
		<!-- Server side injected data set
    Not required on client app and will
    throw a file not found error.
    * THESE FILES SHOULD NOT BE INCLUDED IN THE RELEASE -->
		<script src="./formData.js">
		</script>
		<!-- Test data files.  Remove before release -->
		<script src="./testData.js">
		</script>
		<script src="./prefillData.js">
		</script>
		<!-- smoosh -->
		<!-- framework scripts-->
		<script src="./scripts/jquery-3.2.1.min.js">
		</script>
		<script src="./scripts/angular.min.js">
		</script>
		<script src="./scripts/angular-messages.min.js">
		</script>
		<script src="./scripts/popper.min.js">
		</script>
		<script src="./scripts/bootstrap.min.js">
		</script>
		<script src="./scripts/angular-strap.min.js">
		</script>
		<script src="./scripts/angular-strap.tpl.min.js">
		</script>
		<script src="./scripts/helper.js">
		</script>
		<script src="./scripts/signature_pad.min.js">
		</script>
		<script src="./scripts/signature.js">
		</script>
		<!-- services -->
		<script src="services/app.js">
		</script>
		<script src="services/common.js">
		</script>
		<!-- controllers -->
		<script src="controllers/index.js">
		</script>
		<!-- models -->
		<script src="models/model.js">
		</script>
		<!-- direcives -->
		<script src="directives/pdTestButtons.js">
		</script>
		<script src="directives/pdNavBar.js">
		</script>
		<script src="directives/pdSignatureModal.js">
		</script>
		<script src="directives/pdConfirmModal.js">
		</script>
		<script src="directives/pdConfirmDataLossModal.js">
		</script>
		<script src="directives/pdFormTitle.js">
		</script>
		<script src="directives/pdFormHeader.js">
		</script>
		<script src="directives/pdSignature.js">
		</script>
		<script src="directives/pdCheckBox.js">
		</script>
		<script src="directives/pdFormHeaderPrint.js">
		</script>
		<script src="directives/pdFooter.js">
		</script>
		<script src="directives/pdRadioButton.js">
		</script>
		<script src="directives/pdNotification.js">
		</script>
		<script src="directives/pdNotificationPrint.js">
		</script>
		<script src="directives/pdSignLegal.js">
		</script>
		<script src="directives/pdSkipModal.js">
		</script>

		<!-- filters -->
		<script src="filters/sumDataSet.js">
		</script>
		<script src="filters/splitToArray.js">
		</script>
		<script src="filters/trustHTML.js">
		</script>
		<script src="filters/checkUNIXTime.js">
		</script>
		<!-- endsmoosh -->
		<script src="components/pdCreateNotificationModal.js">
		</script>
		<!-- server-side initiated print request -->
		<script>
			$(document).ready(function () {
				if ((typeof shouldPrint !== 'undefined') && (shouldPrint)) {
					window.print()
				}
			});

			// Listen for messages from parent window
			window.addEventListener('message', function(event) {
				console.log('📨 Received message from parent:', event.data);
				
				if (event.data && event.data.type === 'generate-json-request') {
					console.log('🔍 Generate JSON request received');
					
					try {
						// Call the generateJSON function
						const jsonResult = generateJSON();
						console.log('📊 JSON generated:', jsonResult);
						
						// Send the result back to parent
						if (window.parent && window.parent !== window) {
							window.parent.postMessage({
								type: 'generate-json-response',
								result: jsonResult,
								timestamp: new Date().toISOString()
							}, '*');
							console.log('📤 JSON result sent to parent');
						}
						
					} catch (error) {
						console.error('❌ Error generating JSON:', error);
						
						// Send error back to parent
						if (window.parent && window.parent !== window) {
							window.parent.postMessage({
								type: 'generate-json-response',
								result: { error: error.message },
								timestamp: new Date().toISOString()
							}, '*');
						}
					}
				}
			});
			
			console.log('✅ Message listener setup complete for generateJSON');
		</script>
		<!-- FORM DATA -->
		<pd-create-notification-component model='notificationData'></pd-create-notification-component>
</body>

</html>