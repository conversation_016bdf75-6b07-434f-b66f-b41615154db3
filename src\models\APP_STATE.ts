import { STMR_HEADER } from "./STMR_HEADER";
import { STMR_CREW } from "./STMR_CREW";
import { STMR_TOPIC_ENTITY } from "./STMR_TOPIC_ENTITY";
import { STMR_ACTION } from "./STMR_ACTION";
import { FORM_HEADER } from "./FORM_HEADER";
import { TOPIC_HEADER } from "./TOPIC_HEADER";

export enum APP_STATE_TYPE {
    FORM = 'FORM',
    STMR = 'STMR'
}

export class APP_STATE {
    type?: APP_STATE_TYPE;
}

export class STMR_STATE extends APP_STATE {
    header: STMR_HEADER = new STMR_HEADER;
    crew: STMR_CREW[] = [];
    topics: TOPIC_HEADER[] = [];
    stmrTopicEntities: STMR_TOPIC_ENTITY[] = [];
    stmrActions: STMR_ACTION[] = [];
    constructor() {
        super();
        this.type = APP_STATE_TYPE.STMR
    }
}

export class FORM_STATE extends APP_STATE {
    header: FORM_HEADER = new FORM_HEADER;
    formData!: string;
    constructor() {
        super();
        this.type = APP_STATE_TYPE.FORM
    }
}